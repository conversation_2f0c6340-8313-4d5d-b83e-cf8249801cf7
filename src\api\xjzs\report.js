import request from '@/axios';

// 保存项目报告
export function saveProjectReport(data) {
  return request({
    url: '/xjzs/projectReport/save',
    method: 'post',
    data
  });
}

// 检查项目是否已存在报告
export function checkReportExists(projectId) {
  return request({
    url: '/xjzs/projectReport/checkExists',
    method: 'get',
    params: { projectId }
  });
}

// 获取项目报告详情
export function getProjectReport(projectId) {
  return request({
    url: '/xjzs/projectReport/detail',
    method: 'get',
    params: { projectId }
  });
}

// 导出项目报告
export function exportProjectReport(projectId) {
  return request({
    url: '/xjzs/projectReport/export',
    method: 'get',
    params: { projectId },
    responseType: 'blob'
  });
}