<template>
  <div class="template-manager">
    <basic-container>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="模板列表" name="list">
          <el-table :data="templateList" border style="width: 100%">
            <el-table-column prop="name" label="模板名称" width="180" />
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="type" label="类型" width="120" />
            <el-table-column prop="updateTime" label="更新时间" width="180" />
            <el-table-column label="操作" width="280">
              <template #default="scope">
                <el-button type="primary" size="small" @click="previewTemplate(scope.row)">
                  预览
                </el-button>
                <el-button type="success" size="small" @click="editTemplate(scope.row)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="deleteTemplate(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="添加模板" name="add">
          <el-form :model="templateForm" label-width="120px">
            <el-form-item label="模板名称">
              <el-input v-model="templateForm.name" />
            </el-form-item>
            <el-form-item label="模板描述">
              <el-input v-model="templateForm.description" type="textarea" />
            </el-form-item>
            <el-form-item label="模板类型">
              <el-select v-model="templateForm.type">
                <el-option label="PDF报告" value="pdf" />
                <el-option label="Word报告" value="word" />
                <el-option label="Excel报表" value="excel" />
              </el-select>
            </el-form-item>
            <el-form-item label="模板内容">
              <el-tabs v-model="editorTab">
                <el-tab-pane label="HTML" name="html">
                  <monaco-editor
                    v-model="templateForm.content"
                    language="html"
                    :options="{ theme: 'vs', automaticLayout: true }"
                    style="height: 500px"
                  />
                </el-tab-pane>
                <el-tab-pane label="CSS" name="css">
                  <monaco-editor
                    v-model="templateForm.css"
                    language="css"
                    :options="{ theme: 'vs', automaticLayout: true }"
                    style="height: 500px"
                  />
                </el-tab-pane>
              </el-tabs>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveTemplate">保存模板</el-button>
              <el-button @click="resetForm">重置</el-button>
              <el-button type="success" @click="previewCurrentTemplate">预览</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </basic-container>
    
    <!-- 模板预览对话框 -->
    <el-dialog title="模板预览" v-model="previewDialogVisible" fullscreen>
      <iframe :src="previewUrl" style="width: 100%; height: 80vh; border: none;"></iframe>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadPreview">下载PDF</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import MonacoEditor from '@/components/monaco-editor';
import { getTemplateList, getTemplate, saveTemplate, deleteTemplate } from '@/api/xjzs/template';

export default {
  name: 'TemplateManager',
  components: {
    MonacoEditor
  },
  setup() {
    const activeTab = ref('list');
    const editorTab = ref('html');
    const templateList = ref([]);
    const previewDialogVisible = ref(false);
    const previewUrl = ref('');
    
    const templateForm = reactive({
      id: null,
      name: '',
      description: '',
      type: 'pdf',
      content: '',
      css: ''
    });
    
    // 获取模板列表
    const fetchTemplateList = async () => {
      try {
        const res = await getTemplateList();
        templateList.value = res.data.data || [];
      } catch (error) {
        ElMessage.error('获取模板列表失败');
      }
    };
    
    // 预览模板
    const previewTemplate = (template) => {
      previewUrl.value = `/template/preview/${template.id}?t=${new Date().getTime()}`;
      previewDialogVisible.value = true;
    };
    
    // 预览当前编辑的模板
    const previewCurrentTemplate = () => {
      if (!templateForm.name) {
        ElMessage.warning('请先填写模板名称');
        return;
      }
      
      // 创建一个临时的Blob URL用于预览
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${templateForm.name}</title>
          <style>${templateForm.css}</style>
        </head>
        <body>
          ${templateForm.content}
        </body>
        </html>
      `;
      
      const blob = new Blob([htmlContent], { type: