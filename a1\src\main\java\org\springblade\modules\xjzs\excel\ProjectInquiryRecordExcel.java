/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;


import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 项目询价记录表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ProjectInquiryRecordExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * id;主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("id;主键")
	private Long id;
	/**
	 * 项目编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("项目编码")
	private String projectId;
	/**
	 * 产品名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品名称")
	private String productName;
	/**
	 * 技术规格
	 */
	@ColumnWidth(20)
	@ExcelProperty("技术规格")
	private String specifications;
	/**
	 * 数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("数量")
	private Long quantity;
	/**
	 * 质量标准
	 */
	@ColumnWidth(20)
	@ExcelProperty("质量标准")
	private String standard;
	/**
	 * 包装要求
	 */
	@ColumnWidth(20)
	@ExcelProperty("包装要求")
	private String packageRequirements;
	/**
	 * 交货期
	 */
	@ColumnWidth(20)
	@ExcelProperty("交货期")
	private Date deliveryDate;
	/**
	 * 服务内容
	 */
	@ColumnWidth(20)
	@ExcelProperty("服务内容")
	private String serviceContent;
	/**
	 * 服务期限
	 */
	@ColumnWidth(20)
	@ExcelProperty("服务期限")
	private String servicePeriod;
	/**
	 * 服务要求
	 */
	@ColumnWidth(20)
	@ExcelProperty("服务要求")
	private String serviceRequirements;
	/**
	 * 其他说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("其他说明")
	private String otherInstructions;
	/**
	 * 交货地点
	 */
	@ColumnWidth(20)
	@ExcelProperty("交货地点")
	private String deliveryAddress;
	/**
	 * 售后服务
	 */
	@ColumnWidth(20)
	@ExcelProperty("售后服务")
	private String aftersalesContent;
	/**
	 * 需附文件
	 */
	@ColumnWidth(20)
	@ExcelProperty("需附文件")
	private String attachment;
	/**
	 * 截止时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("截止时间")
	private Date deadline;
	/**
	 * 联系人姓名
	 */
	@ColumnWidth(20)
	@ExcelProperty("联系人姓名")
	private String contactName;
	/**
	 * 联系人电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("联系人电话")
	private String contactPhone;
	/**
	 * 报价总金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("报价总金额")
	private BigDecimal quotationTotalAmount;
	/**
	 * 报价时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("报价时间")
	private Date quotationTime;
	/**
	 * 报价公司
	 */
	@ColumnWidth(20)
	@ExcelProperty("报价公司")
	private String quotationCompany;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Long isDeleted;

}
