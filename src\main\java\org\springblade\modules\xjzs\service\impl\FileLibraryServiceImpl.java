/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.service.impl;

import org.springblade.modules.xjzs.pojo.entity.FileLibraryEntity;
import org.springblade.modules.xjzs.pojo.vo.FileLibraryVO;
import org.springblade.modules.xjzs.excel.FileLibraryExcel;
import org.springblade.modules.xjzs.mapper.FileLibraryMapper;
import org.springblade.modules.xjzs.service.IFileLibraryService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import java.util.List;

/**
 * 文件库 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
public class FileLibraryServiceImpl extends BaseServiceImpl<FileLibraryMapper, FileLibraryEntity> implements IFileLibraryService {

	@Override
	public IPage<FileLibraryVO> selectFileLibraryPage(IPage<FileLibraryVO> page, FileLibraryVO fileLibrary) {
		return page.setRecords(baseMapper.selectFileLibraryPage(page, fileLibrary));
	}

	@Override
	public List<FileLibraryExcel> exportFileLibrary(Wrapper<FileLibraryEntity> queryWrapper) {
		List<FileLibraryExcel> fileLibraryList = baseMapper.exportFileLibrary(queryWrapper);
		//fileLibraryList.forEach(fileLibrary -> {
		//	fileLibrary.setTypeName(DictCache.getValue(DictEnum.YES_NO, fileLibrary.getType()));
		//});
		return fileLibraryList;
	}

}
