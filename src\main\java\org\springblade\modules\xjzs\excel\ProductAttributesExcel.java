/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 商品属性 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ProductAttributesExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Integer id;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String feeName;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String attributeName;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String attributeValue;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Date createdAt;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Date updatedAt;

}
