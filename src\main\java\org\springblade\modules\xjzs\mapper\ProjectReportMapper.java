package org.springblade.modules.xjzs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springblade.modules.xjzs.entity.ProjectReportEntity;

/**
 * 项目报告Mapper接口
 */
public interface ProjectReportMapper extends BaseMapper<ProjectReportEntity> {

    /**
     * 检查项目是否存在报告
     */
    @Select("SELECT COUNT(1) FROM xjzs_project_report WHERE project_id = #{projectId} AND is_deleted = 0")
    Integer checkReportExists(@Param("projectId") Long projectId);
    
    /**
     * 获取项目报告详情（关联项目表）
     */
    @Select("SELECT r.*, p.name as project_name, p.type as project_type, p.content as project_content " +
            "FROM xjzs_project_report r " +
            "LEFT JOIN xjzs_project p ON r.project_id = p.id " +
            "WHERE r.project_id = #{projectId} AND r.is_deleted = 0 " +
            "ORDER BY r.create_time DESC LIMIT 1")
    ProjectReportEntity getProjectReportWithProject(@Param("projectId") Long projectId);
}

