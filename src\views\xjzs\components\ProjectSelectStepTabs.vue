<template>
  <div class="step-content">
    <!-- 项目名称选择 -->
    <div class="form-item">
      <label>项目名称</label>
      <el-select v-model="formData.selectedProject" placeholder="请选择项目" class="full-width" clearable filterable
        @change="handleProjectChange">
        <el-option v-for="item in projectOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>

    <!-- 项目信息 -->
    <div v-if="formData.selectedProject" class="project-info">
      <div class="info-item">
        <span class="info-label">项目编号:</span>
        <span class="info-value">{{ formData.projectCode }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">项目类型:</span>
        <span class="info-value">{{ formData.projectType }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">采购方式:</span>
        <span class="info-value">{{ formData.procurementMethod }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">算法类型:</span>
        <span class="info-value">{{ formData.algorithmCategory || '未设置' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">项目描述:</span>
        <span class="info-value">{{ formData.projectDescription }}</span>
      </div>
    </div>

    <!-- 竖向选择区域 -->
    <div v-if="formData.selectedProject" class="form-item" :class="{ 'highlight-table': shouldHighlightTable }">
      <label>标准表</label>
      <!-- 高亮提示信息 -->
      <div v-if="shouldHighlightTable" class="highlight-message">
        <i class="el-icon-success"></i>
        <span>项目信息已更新，请确认下方配置</span>
      </div>

      <!-- 竖向选择容器 -->
      <div class="vertical-selection-container" :class="{ 'table-highlighted': shouldHighlightTable }">
        <!-- 根据算法类型动态显示不同的竖向选择 -->
        <div class="vertical-selector">
          <!-- 当算法类型为工程咨询类时显示工程咨询类选项 -->
          <div v-if="formData.algorithmCategory === '工程咨询类'">
            <h4 class="section-title">工程咨询类配置</h4>

            <!-- 工程咨询类别选择 -->
            <div class="compact-section">
              <h5 class="compact-title">1.工程咨询类别</h5>
              <div class="compact-options">
                <div class="compact-option" :class="{ 'selected': selectedEngineering.category === '竣工决算审计' }"
                  @click="selectEngineeringCategory('竣工决算审计')">
                  <i class="el-icon-circle-check" v-if="selectedEngineering.category === '竣工决算审计'"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>竣工决算审计</span>
                </div>
              </div>
            </div>

            <!-- 专业系数选择 -->
            <div class="compact-section">
              <h5 class="compact-title">2.专业系数</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="option in professionalAdjustmentOptions" :key="option"
                  :class="{ 'selected': selectedEngineering.professionalAdjustment === option }"
                  @click="selectProfessionalAdjustment(option)">
                  <i class="el-icon-circle-check" v-if="selectedEngineering.professionalAdjustment === option"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ option }}</span>
                </div>
              </div>
            </div>

            <!-- 复杂程度系数选择 -->
            <div class="compact-section">
              <h5 class="compact-title">3.复杂程度系数</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="option in complexityAdjustmentOptions" :key="option.value"
                  :class="{ 'selected': selectedEngineering.complexityAdjustment === option.value }"
                  @click="selectComplexityAdjustment(option.value)">
                  <i class="el-icon-circle-check" v-if="selectedEngineering.complexityAdjustment === option.value"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ option.label }}</span>
                </div>
              </div>
            </div>

            <!-- 高程系数选择 -->
            <div class="compact-section">
              <h5 class="compact-title">4.高程系数</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="option in altitudeAdjustmentOptions" :key="option.value"
                  :class="{ 'selected': selectedEngineering.altitudeAdjustment === option.value }"
                  @click="selectAltitudeAdjustment(option.value)">
                  <i class="el-icon-circle-check" v-if="selectedEngineering.altitudeAdjustment === option.value"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ option.label }}</span>
                </div>
              </div>
            </div>

            <!-- 工程费用输入 -->
            <div class="compact-section">
              <h5 class="compact-title">5.工程费用（万元）</h5>
              <div class="input-grid">
                <div class="input-item">
                  <label>建安工程费</label>
                  <el-input-number v-model="selectedEngineering.constructionCost" controls-position="right" :min="0"
                    placeholder="建安工程费" size="small" style="width: 100%"
                    @change="updateEngineeringData"></el-input-number>
                </div>
                <div class="input-item">
                  <label>设备及工器具购置费</label>
                  <el-input-number v-model="selectedEngineering.constructionFee" controls-position="right" :min="0"
                    placeholder="设备费" size="small" style="width: 100%"
                    @change="updateEngineeringData"></el-input-number>
                </div>
                <div class="input-item">
                  <label>设备安装费</label>
                  <el-input-number v-model="selectedEngineering.equipmentFee" controls-position="right" :min="0"
                    placeholder="设备安装费" size="small" style="width: 100%"
                    @change="updateEngineeringData"></el-input-number>
                </div>
                <div class="input-item">
                  <label>联合试运转费</label>
                  <el-input-number v-model="selectedEngineering.testRunFee" controls-position="right" :min="0"
                    placeholder="联合试运转费" size="small" style="width: 100%"
                    @change="updateEngineeringData"></el-input-number>
                </div>
                <div class="input-item">
                  <label>其他费用调整系数</label>
                  <el-input-number v-model="selectedEngineering.additionalAdjustment" controls-position="right" :min="0"
                    placeholder="其他费用调整系数" size="small" style="width: 100%"
                    @change="updateEngineeringData"></el-input-number>
                </div>
                <div class="input-item">
                  <label>其他设计费</label>
                  <el-input-number v-model="selectedEngineering.otherDesignFees" controls-position="right" :min="0"
                    placeholder="其他设计费" size="small" style="width: 100%"
                    @change="updateEngineeringData"></el-input-number>
                </div>
              </div>
            </div>
          </div>

          <!-- 当算法类型为培训类时显示培训类选项 -->
          <div v-else-if="formData.algorithmCategory === '培训类'">
            <h4 class="section-title">培训类配置</h4>

            <!-- 培训方式选择 -->
            <div class="compact-section">
              <h5 class="compact-title">1.培训方式</h5>
              <div class="compact-options">
                <div class="compact-option" :class="{ 'selected': selectedTraining.training_type === '线上培训' }"
                  @click="selectTrainingType('线上培训')">
                  <i class="el-icon-circle-check" v-if="selectedTraining.training_type === '线上培训'"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>线上培训</span>
                </div>
                <div class="compact-option" :class="{ 'selected': selectedTraining.training_type === '线下培训' }"
                  @click="selectTrainingType('线下培训')">
                  <i class="el-icon-circle-check" v-if="selectedTraining.training_type === '线下培训'"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>线下培训</span>
                </div>
              </div>
            </div>

            <!-- 师资职称选择 -->
            <div class="compact-section">
              <h5 class="compact-title">2.师资职称</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="title in instructorTitles" :key="title"
                  :class="{ 'selected': selectedTraining.instructor_title === title }"
                  @click="selectInstructorTitle(title)">
                  <i class="el-icon-circle-check" v-if="selectedTraining.instructor_title === title"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ title }}</span>
                </div>
              </div>
            </div>

            <!-- 培训类别选择 -->
            <div class="compact-section">
              <h5 class="compact-title">3.培训类别</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="category in trainingCategoryOptions" :key="category"
                  :class="{ 'selected': selectedTraining.training_category === category }"
                  @click="selectTrainingCategory(category)">
                  <i class="el-icon-circle-check" v-if="selectedTraining.training_category === category"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ category }}</span>
                </div>
              </div>
            </div>

            <!-- 培训参数输入 -->
            <div class="compact-section">
              <h5 class="compact-title">4.培训参数</h5>
              <div class="input-grid">
                <div class="input-item">
                  <label>培训天数</label>
                  <el-input-number v-model="selectedTraining.training_days" controls-position="right" :min="1"
                    :max="365" placeholder="培训天数" size="small" style="width: 100%"
                    @change="updateTrainingData"></el-input-number>
                </div>
                <div class="input-item">
                  <label>培训人数</label>
                  <el-input-number v-model="selectedTraining.trainee_count" controls-position="right" :min="1"
                    :max="1000" placeholder="培训人数" size="small" style="width: 100%"
                    @change="updateTrainingData"></el-input-number>
                </div>
                <div class="input-item">
                  <label>每天学时</label>
                  <el-input-number v-model="selectedTraining.daily_hours" controls-position="right" :min="1" :max="24"
                    placeholder="每天学时" size="small" style="width: 100%" @change="updateTrainingData"></el-input-number>
                </div>
              </div>
            </div>
          </div>

          <!-- 当算法类型为货物类、劳务类、信息类、办公类、综合类时显示京东慧采选项 -->
          <div v-else>
            <h4 class="section-title">京东慧采配置</h4>

            <!-- 费用名称选择 -->
            <div class="compact-section">
              <h5 class="compact-title">1.费用名称</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="item in jdCategories.feeNames" :key="item.value"
                  :class="{ 'selected': selectedJd.feeName === item.value }" @click="selectFeeName(item.value)">
                  <i class="el-icon-circle-check" v-if="selectedJd.feeName === item.value"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </div>

            <!-- 一级分类选择 -->
            <div class="compact-section">
              <h5 class="compact-title">2.一级分类</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="item in jdCategories.firstCategories" :key="item.value"
                  :class="{ 'selected': selectedJd.firstCategory === item.value }"
                  @click="selectFirstCategory(item.value)">
                  <i class="el-icon-circle-check" v-if="selectedJd.firstCategory === item.value"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </div>

            <!-- 二级分类选择 -->
            <div class="compact-section">
              <h5 class="compact-title">3.二级分类</h5>
              <div class="compact-options">
                <div class="compact-option" v-for="item in jdCategories.secondCategories" :key="item.value"
                  :class="{ 'selected': selectedJd.secondCategory === item.value }"
                  @click="selectSecondCategory(item.value)">
                  <i class="el-icon-circle-check" v-if="selectedJd.secondCategory === item.value"></i>
                  <i class="el-icon-circle" v-else></i>
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </div>

            <!-- 单位和数量 -->
            <div class="compact-section">
              <h5 class="compact-title">4.单位和数量</h5>
              <div class="input-grid">
                <div class="input-item">
                  <label>单位</label>
                  <el-select v-model="selectedJd.unit" placeholder="请选择单位" size="small" style="width: 100%">
                    <el-option v-for="option in unitOptions" :key="option.value" :label="option.label"
                      :value="option.value">
                    </el-option>
                  </el-select>
                  <!-- 如果没有单位选项，显示提示信息 -->
                  <div v-if="selectedJd.feeName && unitOptions.length === 0" class="no-options-tip">
                    <span>该费用名称下暂无单位选项</span>
                  </div>
                </div>
                <div class="input-item">
                  <label>数量</label>
                  <el-input-number v-model="selectedJd.quantity" controls-position="right" :min="1" placeholder="数量"
                    size="small" style="width: 100%"></el-input-number>
                </div>
              </div>

              <!-- 添加按钮 -->
              <div class="add-item-container">
                <el-button type="primary" size="small" @click="addJdItem" :disabled="!canAddJdItem">
                  <i class="el-icon-plus"></i>
                  添加项目
                </el-button>
              </div>
            </div>

            <!-- 已添加的项目列表 -->
            <div v-if="jdTableRows.length > 0" class="added-items-section">
              <h5 class="compact-title">已添加的项目</h5>
              <div class="added-items-list">
                <div v-for="(item, index) in jdTableRows" :key="index" class="added-item">
                  <div class="item-content">
                    <div class="item-row">
                      <span class="item-label">费用名称:</span>
                      <el-tag type="primary" size="small">{{ item.feeName }}</el-tag>
                    </div>
                    <div class="item-row">
                      <span class="item-label">分类:</span>
                      <el-tag type="info" size="small">{{ item.firstCategory }}</el-tag>
                      <el-tag type="info" size="small">{{ item.secondCategory }}</el-tag>
                    </div>
                    <div class="item-row">
                      <span class="item-label">单位数量:</span>
                      <el-tag type="warning" size="small">{{ item.quantity }} {{ item.unit }}</el-tag>
                    </div>
                  </div>
                  <div class="item-actions">
                    <el-button type="danger" size="small" @click="removeJdItem(index)">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="button-container">
      <el-button type="primary" @click="nextStep" :disabled="isReportGenerated">
        开始生成
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getAllProjects } from '@/api/xjzs/project';
import { getJdCategories, getTrainingUnit } from '@/api/xjzs/trainingFee';

export default {
  name: 'ProjectSelectStepTabs',
  props: {
    formData: {
      type: Object,
      required: true
    },
    isReportGenerated: {
      type: Boolean,
      default: false
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {
    const projectOptions = ref([]);
    const shouldHighlightTable = ref(false);

    // 工程咨询类选择数据
    const selectedEngineering = reactive({
      category: '竣工决算审计',
      professionalAdjustment: '1.0',
      complexityAdjustment: '0.85',
      altitudeAdjustment: '1',
      constructionCost: 0,
      constructionFee: 0,
      equipmentFee: 0,
      testRunFee: 0,
      additionalAdjustment: 0,
      otherDesignFees: 0
    });

    // 培训类选择数据
    const selectedTraining = reactive({
      training_type: '线上培训',
      training_days: 1,
      trainee_count: 1,
      daily_hours: 1,
      instructor_title: '副高级职称',
      training_category: '处级及以下（三类）'
    });

    // 京东慧采当前选择数据
    const selectedJd = reactive({
      feeName: '',
      firstCategory: '',
      secondCategory: '',
      unit: '',
      quantity: 1,
      attributes: []
    });

    // 京东慧采已添加的项目列表
    const jdTableRows = ref([]);

    const unitOptions = ref([]);

    // 工程咨询类相关选项
    const professionalAdjustmentOptions = [
      '0.8',
      '1.0'
    ];

    const complexityAdjustmentOptions = [
      { label: '一般工程 - 0.85', value: '0.85' },
      { label: '较复杂工程 - 1.0', value: '1.0' },
      { label: '复杂工程 - 1.15', value: '1.15' }
    ];

    const altitudeAdjustmentOptions = [
      { label: '海拔高程2001 m以下 - 1', value: '1' },
      { label: '海拔高程2001～3000 m - 1.1', value: '1.1' },
      { label: '海拔高程3001～3500 m - 1.2', value: '1.2' },
      { label: '海拔高程3501～4000 m - 1.3', value: '1.3' }
    ];

    // 培训类相关选项
    const instructorTitles = [
      '副高级职称',
      '正高级职称',
      '中级职称',
      '初级职称'
    ];

    const trainingCategoryOptions = [
      '省部级及以上（一类）',
      '司局级（二类）',
      '处级及以下（三类）'
    ];

    // 京东慧采相关数据
    const jdCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: []
    });



    // 加载项目列表
    const loadProjects = async () => {
      try {
        const res = await getAllProjects();
        if (res && res.data) {
          projectOptions.value = res.data.data.map(item => ({
            value: item.id,
            label: item.name,
            project: item
          }));
        }
      } catch (error) {
        console.error('加载项目列表失败:', error);
        ElMessage.error('加载项目列表失败');
      }
    };

    // 工程咨询类选择方法
    const selectEngineeringCategory = (category) => {
      selectedEngineering.category = category;
      updateEngineeringData();
    };

    const selectProfessionalAdjustment = (value) => {
      selectedEngineering.professionalAdjustment = value;
      updateEngineeringData();
    };

    const selectComplexityAdjustment = (value) => {
      selectedEngineering.complexityAdjustment = value;
      updateEngineeringData();
    };

    const selectAltitudeAdjustment = (value) => {
      selectedEngineering.altitudeAdjustment = value;
      updateEngineeringData();
    };

    // 更新工程咨询数据到formData
    const updateEngineeringData = () => {
      if (props.formData.algorithmCategory === '工程咨询类') {
        props.formData.engineeringTableRows = [{
          category: selectedEngineering.category,
          professionalAdjustment: selectedEngineering.professionalAdjustment,
          complexityAdjustment: selectedEngineering.complexityAdjustment,
          altitudeAdjustment: selectedEngineering.altitudeAdjustment,
          constructionCost: selectedEngineering.constructionCost,
          constructionFee: selectedEngineering.constructionFee,
          equipmentFee: selectedEngineering.equipmentFee,
          testRunFee: selectedEngineering.testRunFee,
          additionalAdjustment: selectedEngineering.additionalAdjustment,
          otherDesignFees: selectedEngineering.otherDesignFees
        }];
      }
    };

    // 培训类选择方法
    const selectTrainingType = (type) => {
      selectedTraining.training_type = type;
      updateTrainingData();
    };

    const selectInstructorTitle = (title) => {
      selectedTraining.instructor_title = title;
      updateTrainingData();
    };

    const selectTrainingCategory = (category) => {
      selectedTraining.training_category = category;
      updateTrainingData();
    };

    // 更新培训数据到formData
    const updateTrainingData = () => {
      if (props.formData.algorithmCategory === '培训类') {
        props.formData.trainingTableRows = [{
          training_type: selectedTraining.training_type,
          training_days: selectedTraining.training_days,
          trainee_count: selectedTraining.trainee_count,
          daily_hours: selectedTraining.daily_hours,
          instructor_title: selectedTraining.instructor_title,
          training_category: selectedTraining.training_category
        }];
      }
    };

    // 京东慧采选择方法
    const selectFeeName = async (feeName) => {
      selectedJd.feeName = feeName;
      selectedJd.firstCategory = '';
      selectedJd.secondCategory = '';
      selectedJd.unit = '';

      if (!feeName) {
        selectedJd.attributes = [];
        unitOptions.value = [];
        return;
      }

      try {
        // 从 jdCategories.feeNames 中找到选中的费用名称对应的分类数据
        const selectedFeeData = jdCategories.feeNames.find(item => item.value === feeName);
        if (selectedFeeData) {
          // 自动填充一级分类和二级分类
          if (selectedFeeData.firstCategories) {
            const firstCategoryArray = selectedFeeData.firstCategories.split(',');
            selectedJd.firstCategory = firstCategoryArray[0];
          }

          if (selectedFeeData.secondCategories) {
            const secondCategoryArray = selectedFeeData.secondCategories.split(',');
            selectedJd.secondCategory = secondCategoryArray[0];
          }
        }

        // 加载单位选项
        const response = await getTrainingUnit(feeName);

        if (response.data && response.data.success) {
          unitOptions.value = response.data.data.map(item => ({
            value: item.unit,
            label: item.unit
          }));
        } else if (response.code === 200 && response.data) {
          unitOptions.value = response.data.map(item => ({
            value: item.unit,
            label: item.unit
          }));
        } else {
          unitOptions.value = [];
        }

        // 设置第一个单位为默认值
        if (unitOptions.value.length > 0) {
          selectedJd.unit = unitOptions.value[0].value;
        }
      } catch (error) {
        console.error('获取单位数据失败:', error);
        unitOptions.value = [];
      }
    };

    const selectFirstCategory = (category) => {
      selectedJd.firstCategory = category;
    };

    const selectSecondCategory = (category) => {
      selectedJd.secondCategory = category;
    };

    // 检查是否可以添加项目
    const canAddJdItem = computed(() => {
      return selectedJd.feeName && selectedJd.firstCategory && selectedJd.secondCategory && selectedJd.unit && selectedJd.quantity > 0;
    });

    // 添加京东慧采项目
    const addJdItem = () => {
      if (!canAddJdItem.value) {
        ElMessage.warning('请完整填写项目信息');
        return;
      }

      // 添加到列表
      jdTableRows.value.push({
        feeName: selectedJd.feeName,
        firstCategory: selectedJd.firstCategory,
        secondCategory: selectedJd.secondCategory,
        unit: selectedJd.unit,
        quantity: selectedJd.quantity,
        attributes: selectedJd.attributes || []
      });

      // 清空当前选择
      selectedJd.feeName = '';
      selectedJd.firstCategory = '';
      selectedJd.secondCategory = '';
      selectedJd.unit = '';
      selectedJd.quantity = 1;
      selectedJd.attributes = [];
      unitOptions.value = [];

      updateJdData();
      ElMessage.success('添加成功');
    };

    // 删除京东慧采项目
    const removeJdItem = (index) => {
      jdTableRows.value.splice(index, 1);
      updateJdData();
      ElMessage.success('删除成功');
    };

    // 更新京东慧采数据到formData
    const updateJdData = () => {
      if (props.formData.algorithmCategory && props.formData.algorithmCategory !== '培训类' && props.formData.algorithmCategory !== '工程咨询类') {
        // 过滤掉空行，只保存有效数据
        const validRows = jdTableRows.value.filter(row =>
          row.feeName && row.firstCategory && row.secondCategory
        ).map(row => ({
          feeName: row.feeName,
          firstCategory: row.firstCategory,
          secondCategory: row.secondCategory,
          quantity: row.quantity || 1,
          unit: row.unit || '个',
          attributes: row.attributes || []
        }));

        props.formData.jdTableRows = validRows;
      }
    };

    // 获取京东慧采分类数据
    const fetchJdCategories = async () => {
      try {
        const res = await getJdCategories();
        if (res.data.success) {
          const data = res.data.data;
          jdCategories.feeNames = data.feeNames || [];
          jdCategories.firstCategories = data.firstCategories || [];
          jdCategories.secondCategories = data.secondCategories || [];
        }
      } catch (error) {
        console.error('获取京东慧采分类数据失败:', error);
      }
    };

    // 设置京东慧采默认值
    const setDefaultJdValues = async () => {
      // 自动选择第一个费用名称
      if (jdCategories.feeNames && jdCategories.feeNames.length > 0) {
        const firstFeeName = jdCategories.feeNames[0].value;
        await selectFeeName(firstFeeName);
      }
    };

    // 处理项目选择
    const handleProjectChange = (projectId) => {
      if (!projectId) {
        // 清空项目信息
        props.formData.projectName = '';
        props.formData.projectCode = '';
        props.formData.projectType = '';
        props.formData.projectCategory = '';
        props.formData.projectDescription = '';
        props.formData.procurementMethod = '';
        props.formData.algorithmCategory = '';
        props.formData.id = null;
        shouldHighlightTable.value = false;
        return;
      }

      const selectedOption = projectOptions.value.find(p => p.value === projectId);
      if (selectedOption && selectedOption.project) {
        const project = selectedOption.project;

        // 将项目对象的属性赋值给formData
        props.formData.projectName = project.name;
        props.formData.projectCode = project.code || `P${project.id}`;
        props.formData.projectType = project.type || '';
        props.formData.projectCategory = project.category || '';
        props.formData.projectDescription = project.content || '';
        props.formData.procurementMethod = project.procurementMethod || '';
        props.formData.algorithmCategory = project.algorithmCategory || '';
        props.formData.id = project.id;

        // 显示高亮提示
        shouldHighlightTable.value = true;
        setTimeout(() => {
          shouldHighlightTable.value = false;
        }, 3000);
      }
    };

    // 下一步
    const nextStep = () => {
      if (!props.formData.selectedProject) {
        ElMessage.warning('请先选择项目');
        return;
      }

      // 根据算法类型保存相应的数据
      if (props.formData.algorithmCategory === '培训类') {
        // 验证培训类数据
        if (!selectedTraining.training_type || !selectedTraining.instructor_title || !selectedTraining.training_category) {
          ElMessage.warning('请完整选择培训类配置');
          return;
        }
        if (selectedTraining.training_days <= 0 || selectedTraining.trainee_count <= 0 || selectedTraining.daily_hours <= 0) {
          ElMessage.warning('请输入有效的培训参数');
          return;
        }

        // 创建培训数据副本
        const trainingData = {
          training_type: selectedTraining.training_type,
          training_days: selectedTraining.training_days,
          trainee_count: selectedTraining.trainee_count,
          daily_hours: selectedTraining.daily_hours,
          instructor_title: selectedTraining.instructor_title,
          training_category: selectedTraining.training_category
        };

        // 保存培训类数据
        props.formData.trainingTableRows = [trainingData];

      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 验证工程咨询类数据
        if (!selectedEngineering.category) {
          ElMessage.warning('请选择工程咨询类别');
          return;
        }

        // 保存工程咨询类数据
        props.formData.engineeringTableRows = [{
          category: selectedEngineering.category,
          professionalAdjustment: selectedEngineering.professionalAdjustment,
          complexityAdjustment: selectedEngineering.complexityAdjustment,
          altitudeAdjustment: selectedEngineering.altitudeAdjustment,
          constructionCost: selectedEngineering.constructionCost,
          constructionFee: selectedEngineering.constructionFee,
          equipmentFee: selectedEngineering.equipmentFee,
          testRunFee: selectedEngineering.testRunFee,
          additionalAdjustment: selectedEngineering.additionalAdjustment,
          otherDesignFees: selectedEngineering.otherDesignFees
        }];
      } else {
        // 验证京东慧采数据
        if (jdTableRows.value.length === 0) {
          ElMessage.warning('请至少添加一个京东慧采项目');
          return;
        }

        // 保存京东慧采数据
        props.formData.jdTableRows = jdTableRows.value;
      }

      emit('next-step');
    };

    // 监听算法类型变化，初始化对应的表格
    watch(() => props.formData.algorithmCategory, (newVal) => {
      if (newVal === '培训类') {
        // 培训类：重置为默认值
        selectedTraining.training_type = '线上培训';
        selectedTraining.training_days = 1;
        selectedTraining.trainee_count = 1;
        selectedTraining.daily_hours = 1;
        selectedTraining.instructor_title = '副高级职称';
        selectedTraining.training_category = '处级及以下（三类）';

        // 立即保存到formData，使待办清单能够显示
        props.formData.trainingTableRows = [{
          training_type: selectedTraining.training_type,
          training_days: selectedTraining.training_days,
          trainee_count: selectedTraining.trainee_count,
          daily_hours: selectedTraining.daily_hours,
          instructor_title: selectedTraining.instructor_title,
          training_category: selectedTraining.training_category
        }];
      } else if (newVal === '工程咨询类') {
        // 工程咨询类：重置为默认值
        selectedEngineering.category = '竣工决算审计';
        selectedEngineering.professionalAdjustment = '1.0';
        selectedEngineering.complexityAdjustment = '0.85';
        selectedEngineering.altitudeAdjustment = '1';
        selectedEngineering.constructionCost = 0;
        selectedEngineering.constructionFee = 0;
        selectedEngineering.equipmentFee = 0;
        selectedEngineering.testRunFee = 0;
        selectedEngineering.additionalAdjustment = 0;
        selectedEngineering.otherDesignFees = 0;

        // 立即保存到formData，使待办清单能够显示
        props.formData.engineeringTableRows = [{
          category: selectedEngineering.category,
          professionalAdjustment: selectedEngineering.professionalAdjustment,
          complexityAdjustment: selectedEngineering.complexityAdjustment,
          altitudeAdjustment: selectedEngineering.altitudeAdjustment,
          constructionCost: selectedEngineering.constructionCost,
          constructionFee: selectedEngineering.constructionFee,
          equipmentFee: selectedEngineering.equipmentFee,
          testRunFee: selectedEngineering.testRunFee,
          additionalAdjustment: selectedEngineering.additionalAdjustment,
          otherDesignFees: selectedEngineering.otherDesignFees
        }];
      } else if (newVal) {
        // 其他所有类型都使用京东慧采：重置为默认值并加载数据
        selectedJd.feeName = '';
        selectedJd.firstCategory = '';
        selectedJd.secondCategory = '';
        selectedJd.unit = '';
        selectedJd.quantity = 1;
        selectedJd.attributes = [];
        jdTableRows.value = [];
        unitOptions.value = [];

        // 加载京东慧采数据并设置默认值
        fetchJdCategories().then(() => {
          // 数据加载完成后自动选择第一个选项
          setDefaultJdValues();
        });

        // 为京东慧采类型设置空的初始数据
        props.formData.jdTableRows = [];
      }
    });

    onMounted(() => {
      loadProjects();
      // 根据算法类型加载相应数据
      if (props.formData.algorithmCategory === '培训类') {
        // 培训类不需要加载京东慧采数据
      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 工程咨询类不需要加载京东慧采数据
      } else {
        // 其他所有类型（包括货物类、劳务类、信息类、办公类、综合类、采购类等）都使用京东慧采
        fetchJdCategories().then(() => {
          // 如果已经有项目选择，则设置默认值
          if (props.formData.selectedProject) {
            setDefaultJdValues();
          }
        });
      }
    });

    return {
      projectOptions,
      shouldHighlightTable,
      // 选择数据
      selectedEngineering,
      selectedTraining,
      selectedJd,
      jdTableRows,
      // 选项数据
      professionalAdjustmentOptions,
      complexityAdjustmentOptions,
      altitudeAdjustmentOptions,
      instructorTitles,
      trainingCategoryOptions,
      jdCategories,
      unitOptions,
      canAddJdItem,
      // 选择方法
      selectEngineeringCategory,
      selectProfessionalAdjustment,
      selectComplexityAdjustment,
      selectAltitudeAdjustment,
      selectTrainingType,
      selectInstructorTitle,
      selectTrainingCategory,
      selectFeeName,
      selectFirstCategory,
      selectSecondCategory,
      addJdItem,
      removeJdItem,
      // 其他方法
      handleProjectChange,
      nextStep
    };
  }
}
</script>

<style scoped>
.step-content {
  padding: 10px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.full-width {
  width: 100%;
}

/* 项目信息标签样式 */
.project-info-tags {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.tag-row {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
  align-items: center;
}

.tag-row:last-child {
  margin-bottom: 0;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.tag-item.full-width {
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.tag-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.description-tag {
  max-width: 100%;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  padding: 8px 12px;
  height: auto;
  min-height: 32px;
}

/* 高亮提示信息 */
.highlight-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 4px;
  color: #409eff;
  font-size: 14px;
  margin-bottom: 15px;
}

/* 竖向选择容器样式 */
.vertical-selection-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.table-highlighted {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.vertical-selector {
  padding: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 30px;
  text-align: center;
}

/* 紧凑选项区域样式 */
.compact-section {
  margin-bottom: 20px;
}

.compact-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
  padding: 0;
}

.compact-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.compact-option {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
  font-size: 13px;
  min-height: 32px;
}

.compact-option:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.compact-option.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
  color: #409eff;
}

.compact-option i {
  font-size: 14px;
  flex-shrink: 0;
}

.compact-option span {
  font-size: 13px;
  line-height: 1.2;
  white-space: nowrap;
}

/* 输入网格样式 */
.input-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.input-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.input-item label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 0;
}

/* 高亮动画 */
.highlight-table {
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.02);
  }
}

.button-container {
  margin-top: 30px;
  text-align: center;
}

.button-container .el-button {
  min-width: 100px;
}

/* 添加项目按钮容器 */
.add-item-container {
  text-align: center;
  margin-top: 15px;
}

/* 已添加项目列表样式 */
.added-items-section {
  margin-top: 25px;
}

.added-items-list {
  max-height: 300px;
  overflow-y: auto;
}

.added-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.added-item:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.item-content {
  flex: 1;
}

.item-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.item-row:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 12px;
  color: #6c757d;
  min-width: 60px;
  font-weight: 500;
}

.item-actions {
  margin-left: 12px;
}

/* 提示信息样式 */
.no-options-tip {
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  color: #d48806;
  font-size: 12px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-row {
    flex-direction: column;
    gap: 8px;
  }

  .compact-options {
    flex-direction: column;
  }

  .input-grid {
    grid-template-columns: 1fr;
  }
}
</style>

<style scoped>
.project-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
  margin-right: 12px;
  flex-shrink: 0;
}

.info-value {
  color: #212529;
  flex: 1;
  word-break: break-word;
}
</style>