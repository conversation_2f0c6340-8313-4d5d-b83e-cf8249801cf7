#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      ##将docker脚本部署的redis服务映射为宿主机ip
      ##生产环境推荐使用阿里云高可用redis服务并设置密码
      host: **********
      port: 6379
      password:
      database: 0
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    url: ***************************************
    username: root
    password: Ystech@2025

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://**********:6379
    password:
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html


#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://**********:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: OkL85reOLjtr30xaPjtV
  #密钥key
  secret-key: OR7oala7v39rHB4CC7r1zR9479FDZVSXmYlD4ftU
  #存储桶
  bucket-name: images

#dify配置
dify:
  url: http://127.0.0.1/v1
  key:
    #京东慧采属性提取接口--搁置
    jdhc: app-JGd79ccqXgK7WiPwcYfQ1fUX
    #京东慧采单位提取接口 --搁置
    jdhcUnit: app-VvVa6neq92JvbmolO1ZUjjLm
    #培训类合规检查接口--搁置
    peixunhegui: app-PcfW81jJJJYlhRbZjHZ09QSo
    #培训类合规性检查AgentApiKey
    compliance: app-uATdnp1x595SkdzLChzo2buM
    #培训类AgentApiKey
    training: app-2d9mnVT4r1g16F5KAeMknZUc
    #货物类AgentApiKey
    goods: app-jimAmyz3ifsRVAt5FqExPubw
    #工程类AgentApiKey
    engineering: app-engineering-prod-key
    #工程类合规检查AgentApiKey
    engineeringCompliance: app-engineering-compliance-prod-key
    #采购类合规检查AgentApiKey
    procurementCompliance: app-procurement-compliance-prod-key
