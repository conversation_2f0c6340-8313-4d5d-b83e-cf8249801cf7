export default {
  expand: false,
  index: true,
  border: true,
  selection: true,
  column: [
    {
      label: "主键ID",
      prop: "id",
      display: false,
      hide: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      display: false,
      hide: true,
    },
    {
      label: "类型",
      prop: "type",
    },
    {
      label: "系数名称",
      prop: "coefficientName",
    },
    {
      label: "系数类型",
      prop: "coefficientType",
    },
    {
      label: "系数值",
      prop: "coefficientValue",
    },
    {
      label: "系数规则",
      prop: "coefficientRule",
    },
    {
      label: "描述",
      prop: "description",
    },
    {
      label: "创建人",
      prop: "createUser",
      display: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      display: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      display: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      display: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      display: false,
      hide: true,
    },
    {
      label: "状态（1:有效；0:无效）",
      prop: "status",
      display: false,
      hide: true,
    },
    {
      label: "逻辑删除标志（0:未删除；1:已删除)",
      prop: "isDeleted",
      display: false,
      hide: true,
    },
  ]
}
