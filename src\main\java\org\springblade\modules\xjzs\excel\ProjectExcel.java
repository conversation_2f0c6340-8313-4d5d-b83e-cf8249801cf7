/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 项目信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ProjectExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * id;主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("id;主键")
	private String id;
	/**
	 * 项目名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("项目名称")
	private String name;
	/**
	 * 项目类别
	 */
	@ColumnWidth(20)
	@ExcelProperty("项目类别")
	private String type;
	/**
	 * 采购内容
	 */
	@ColumnWidth(20)
	@ExcelProperty("采购内容")
	private String content;
	/**
	 * 预算金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("预算金额")
	private Long budget;
	/**
	 * 采购方式
	 */
	@ColumnWidth(20)
	@ExcelProperty("采购方式")
	private String procurementMethod;
	/**
	 * 实施单位
	 */
	@ColumnWidth(20)
	@ExcelProperty("实施单位")
	private String dept;
	/**
	 * 备注
	 */
	@ColumnWidth(20)
	@ExcelProperty("备注")
	private String remark;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Long isDeleted;

}
