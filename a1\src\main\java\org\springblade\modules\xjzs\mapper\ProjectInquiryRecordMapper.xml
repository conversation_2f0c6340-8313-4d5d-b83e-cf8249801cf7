<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.ProjectInquiryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="projectInquiryRecordResultMap" type="org.springblade.modules.xjzs.pojo.entity.ProjectInquiryRecordEntity">
        <result column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="product_name" property="productName"/>
        <result column="specifications" property="specifications"/>
        <result column="quantity" property="quantity"/>
        <result column="standard" property="standard"/>
        <result column="package_requirements" property="packageRequirements"/>
        <result column="delivery_date" property="deliveryDate"/>
        <result column="service_content" property="serviceContent"/>
        <result column="service_period" property="servicePeriod"/>
        <result column="service_requirements" property="serviceRequirements"/>
        <result column="other_instructions" property="otherInstructions"/>
        <result column="delivery_address" property="deliveryAddress"/>
        <result column="aftersales_content" property="aftersalesContent"/>
        <result column="attachment" property="attachment"/>
        <result column="deadline" property="deadline"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="quotation_total_amount" property="quotationTotalAmount"/>
        <result column="quotation_time" property="quotationTime"/>
        <result column="quotation_company" property="quotationCompany"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectProjectInquiryRecordPage" resultMap="projectInquiryRecordResultMap">
        select * from xjzs_project_inquiry_record where is_deleted = 0
    </select>


    <select id="exportProjectInquiryRecord" resultType="org.springblade.modules.xjzs.excel.ProjectInquiryRecordExcel">
        SELECT * FROM xjzs_project_inquiry_record ${ew.customSqlSegment}
    </select>

</mapper>
