<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.SupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="supplierResultMap" type="org.springblade.modules.xjzs.pojo.entity.SupplierEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectSupplierPage" resultMap="supplierResultMap">
        select * from xjzs_supplier where is_deleted = 0
    </select>


    <select id="exportSupplier" resultType="org.springblade.modules.xjzs.excel.SupplierExcel">
        SELECT * FROM xjzs_supplier ${ew.customSqlSegment}
    </select>

</mapper>
