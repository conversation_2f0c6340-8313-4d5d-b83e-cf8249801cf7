<template>
  <div class="price-calculator-container">
    <el-card class="calculator-card">
      <template #header>
        <div class="card-header">
          <span>京东慧采最高限价计算器</span>
          <el-tooltip content="使用箱线图分析，基于四分位距（IQR）找异常点并剔除。剔除异常值后，取最高十个价格的均价作为最高限价。" placement="top">
            <el-button class="info-button" icon="el-icon-info" circle></el-button>
          </el-tooltip>
        </div>
      </template>
      
      <el-form ref="calculatorForm" :model="form" label-width="120px" class="calculator-form">
        <el-form-item label="商品分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择商品分类" clearable @change="handleCategoryChange">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="品牌" prop="brandId">
          <el-select v-model="form.brandId" placeholder="请选择品牌" clearable>
            <el-option
              v-for="item in brandOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="calculatePrice" :loading="loading">计算最高限价</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="calculationResult !== null" class="result-container">
        <el-divider content-position="center">计算结果</el-divider>
        <div class="result-box">
          <div class="result-label">最高限价:</div>
          <div class="result-value">¥ {{ calculationResult.toFixed(2) }}</div>
        </div>
        <div class="result-description">
          <p>该价格是通过箱线图分析，基于四分位距（IQR）找异常点并剔除后，取最高十个价格的均价计算得出。</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { calculateMaxPrice } from '@/api/xjzs/trainingFee';
import { getList } from '@/api/xjzs/trainingFee';

export default {
  name: 'PriceCalculator',
  data() {
    return {
      form: {
        categoryId: '',
        brandId: ''
      },
      loading: false,
      calculationResult: null,
      categoryOptions: [],
      brandOptions: []
    };
  },
  mounted() {
    this.loadCategories();
  },
  methods: {
    // 加载分类数据
    loadCategories() {
      getList({
        current: 1,
        size: 1000,
        feeType: '京东慧采'
      }).then(res => {
        const data = res.data.data.records || [];
        // 提取不重复的分类
        const categories = [...new Set(data.map(item => item.firstCategory))];
        this.categoryOptions = categories.filter(Boolean).map(category => ({
          label: category,
          value: category
        }));
      });
    },
    
    // 分类变更时加载对应的品牌
    handleCategoryChange(categoryId) {
      if (!categoryId) {
        this.brandOptions = [];
        this.form.brandId = '';
        return;
      }
      
      getList({
        current: 1,
        size: 1000,
        feeType: '京东慧采',
        firstCategory: categoryId
      }).then(res => {
        const data = res.data.data.records || [];
        // 提取不重复的品牌
        const brands = [...new Set(data.map(item => item.brand))];
        this.brandOptions = brands.filter(Boolean).map(brand => ({
          label: brand,
          value: brand
        }));
      });
    },
    
    // 计算最高限价
    calculatePrice() {
      if (!this.form.categoryId) {
        this.$message.warning('请选择商品分类');
        return;
      }
      
      this.loading = true;
      calculateMaxPrice(this.form.categoryId, this.form.brandId)
        .then(res => {
          this.calculationResult = res.data.data;
          if (this.calculationResult === 0) {
            this.$message.warning('未找到符合条件的价格数据或计算结果为0');
          }
        })
        .catch(error => {
          this.$message.error('计算失败: ' + (error.message || '未知错误'));
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style scoped>
.price-calculator-container {
  padding: 20px;
  height: 100%;
}

.calculator-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calculator-form {
  margin-top: 20px;
}

.result-container {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  text-align: center;
}

.result-box {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
}

.result-label {
  font-size: 18px;
  margin-right: 10px;
}

.result-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.result-description {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}

.info-button {
  padding: 5px;
  font-size: 12px;
}
</style>
