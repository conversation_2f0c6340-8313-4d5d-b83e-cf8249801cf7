/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.service.impl;

import org.springblade.modules.xjzs.pojo.entity.SupplierInquiryRecordEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectInquiryRecordVO;
import org.springblade.modules.xjzs.pojo.vo.SupplierInquiryRecordVO;
import org.springblade.modules.xjzs.excel.SupplierInquiryRecordExcel;
import org.springblade.modules.xjzs.mapper.SupplierInquiryRecordMapper;
import org.springblade.modules.xjzs.service.ISupplierInquiryRecordService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 供应商询价历史记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class SupplierInquiryRecordServiceImpl extends BaseServiceImpl<SupplierInquiryRecordMapper, SupplierInquiryRecordEntity> implements ISupplierInquiryRecordService {

	@Override
	public IPage<SupplierInquiryRecordVO> selectSupplierInquiryRecordPage(IPage<SupplierInquiryRecordVO> page, SupplierInquiryRecordVO supplierInquiryRecord) {
		return page.setRecords(baseMapper.selectSupplierInquiryRecordPage(page, supplierInquiryRecord));
	}


	@Override
	public List<SupplierInquiryRecordExcel> exportSupplierInquiryRecord(Wrapper<SupplierInquiryRecordEntity> queryWrapper) {
		List<SupplierInquiryRecordExcel> supplierInquiryRecordList = baseMapper.exportSupplierInquiryRecord(queryWrapper);
		//supplierInquiryRecordList.forEach(supplierInquiryRecord -> {
		//	supplierInquiryRecord.setTypeName(DictCache.getValue(DictEnum.YES_NO, SupplierInquiryRecord.getType()));
		//});
		return supplierInquiryRecordList;
	}

	@Override
	public IPage<SupplierInquiryRecordVO> getSupplierInquiryRecordPage(IPage<SupplierInquiryRecordVO> page, SupplierInquiryRecordVO supplierInquiryRecord) {
		return page.setRecords(baseMapper.selectSupplierInquiryRecordPage(page, supplierInquiryRecord));
	}

}
