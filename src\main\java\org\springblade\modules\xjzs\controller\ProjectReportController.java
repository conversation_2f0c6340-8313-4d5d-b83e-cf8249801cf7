package org.springblade.modules.xjzs.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.Docx4J;
import org.docx4j.fonts.IdentityPlusMapper;
import org.docx4j.fonts.PhysicalFonts;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.json.JSONArray;
import org.json.JSONObject;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.springblade.core.tool.api.R;
import org.springblade.modules.xjzs.entity.ProjectReportEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectReportCheckResultVO;
import org.springblade.modules.xjzs.service.IProjectReportService;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.xjzs.util.DocxToPdfConverter;
import org.springblade.modules.xjzs.util.WordTemplateUtil;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import org.springblade.modules.dify.resp.WorkflowStreamResponse;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 项目报告控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/xjzs/projectReport")
@Tag(name = "项目报告接口")
public class ProjectReportController {

    private final IProjectReportService projectReportService;

    /**
     * 保存项目报告
     */
    @PostMapping("/save")
    @Operation(summary = "保存项目报告", description = "传入报告信息")
    public R<Boolean> save(@RequestBody ProjectReportEntity report) {
        try {
            boolean success = projectReportService.saveProjectReport(report);
            return R.data(success);
        } catch (Exception e) {
            log.error("保存项目报告失败", e);
            return R.fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 检查项目是否已存在报告
     */
    @GetMapping("/checkExists")
    @Operation(summary = "检查项目是否已存在报告", description = "传入项目ID")
    public R<Map<String, Boolean>> checkExists(@RequestParam Long projectId) {
        try {
            boolean exists = projectReportService.checkReportExists(projectId);
            Map<String, Boolean> result = new HashMap<>();
            result.put("exists", exists);
            return R.data(result);
        } catch (Exception e) {
            log.error("检查项目报告失败", e);
            return R.fail("检查失败：" + e.getMessage());
        }
    }
    /**
     * 合规校验接口
     */
    @GetMapping("/checkReport")
    @Operation(summary = "合规校验接口", description = "传入项目ID")
    public R<List<ProjectReportCheckResultVO>> checkReport(@RequestParam Long projectId) {
        try {
            List<ProjectReportCheckResultVO> vo = projectReportService.checkReport(projectId);
            projectReportService.updateReportCheckContent(projectId, JSON.toJSONString(vo));
            return R.data(vo);
        } catch (Exception e) {
            log.error("检查项目报告失败", e);
            return R.fail("检查失败：" + e.getMessage());
        }



    }

    /**
     * 新的合规性检查接口 - 使用Dify agent（流式返回）
     */
    @GetMapping("/checkComplianceWithDify")
    @Operation(summary = "使用Dify agent进行合规性检查", description = "传入项目ID")
    public Flux<WorkflowStreamResponse> checkComplianceWithDify(@RequestParam Long projectId) {
        return Flux.defer(() -> {
            try {
                // 直接返回Dify工作流的流式响应
                return projectReportService.checkComplianceWithDifyStream(projectId);

            } catch (Exception e) {
                log.error("Dify合规性检查失败", e);
                // 返回错误响应
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 工程类合规性检查接口 - 使用Dify agent（流式返回）
     */
    @GetMapping("/checkEngineeringComplianceWithDify")
    @Operation(summary = "使用Dify agent进行工程类合规性检查", description = "传入项目ID")
    public Flux<WorkflowStreamResponse> checkEngineeringComplianceWithDify(@RequestParam Long projectId) {
        return Flux.defer(() -> {
            try {
                // 直接返回Dify工作流的流式响应
                return projectReportService.checkEngineeringComplianceWithDifyStream(projectId);

            } catch (Exception e) {
                log.error("Dify工程类合规性检查失败", e);
                // 返回错误响应
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("工程类合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 采购类合规性检查接口 - 使用Dify agent（流式返回）
     */
    @GetMapping("/checkProcurementComplianceWithDify")
    @Operation(summary = "使用Dify agent进行采购类合规性检查", description = "传入项目ID")
    public Flux<WorkflowStreamResponse> checkProcurementComplianceWithDify(@RequestParam Long projectId) {
        return Flux.defer(() -> {
            try {
                // 直接返回Dify工作流的流式响应
                return projectReportService.checkProcurementComplianceWithDifyStream(projectId);

            } catch (Exception e) {
                log.error("Dify采购类合规性检查失败", e);
                // 返回错误响应
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("采购类合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 获取项目报告详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取项目报告详情", description = "传入项目ID")
    public R<ProjectReportEntity> detail(@RequestParam Long projectId) {
        try {
            ProjectReportEntity report = projectReportService.getProjectReport(projectId);
            return R.data(report);
        } catch (Exception e) {
            log.error("获取项目报告详情失败", e);
            return R.fail("获取详情失败：" + e.getMessage());
        }
    }



    /**
     * 解析报告内容JSON
     */
    private Map<String, Object> parseReportContent(String reportContent) {
        try {
            // 这里应该使用JSON解析库，如Jackson或Gson
            // 简化示例，实际项目中应该使用JSON库
            return new HashMap<>();
        } catch (Exception e) {
            log.error("解析报告内容失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 填充报告占位符
     */
    private void fillReportPlaceholders(Map<String, String> placeholders, Map<String, Object> reportContent, ProjectReportEntity report) {
        // 当前日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String currentDate = dateFormat.format(new Date());
        
        // 获取当前用户
        String currentUser = AuthUtil.getNickName();
        
        // 基本信息
        placeholders.put("reportDate", currentDate);
        placeholders.put("reportAuthor", currentUser);
        placeholders.put("reportId", report.getReportId());
        
        // 根据算法类别设置不同的编制依据和计算方法
        String algorithmCategory = report.getAlgorithmCategory();
        if ("工程咨询类".equals(algorithmCategory)) {
            placeholders.put("basisContent", "依据《工程咨询行业管理办法》（国家发展改革委2017年第9号令）、《建设工程造价咨询服务计费指导意见》（中价协〔2018〕11号）等文件，结合本项目特点，制定本限价。");
            placeholders.put("calculationMethod", "1、算法：根据《建设工程造价咨询服务计费指导意见》（中价协〔2018〕11号）的计费标准进行计算。\n" +
                    "2、计价标准：根据工程类别、工作成果、复杂程度等因素确定基础费率，并应用相应的调整系数。\n" +
                    "3、计算规则：咨询费用 = 基础费用 × 复杂系数 × 调整系数");
        } else {
            placeholders.put("basisContent", "依据《广东省湛江市人民政府采购中心关于印发政府采购限价管理办法（试行）》（2020年12月），《广东省湛江市人民政府采购中心关于进一步规范政府采购活动的通知》（2019年5月）等文件，结合本项目特点，制定本限价。");
            placeholders.put("calculationMethod", "直接使用（如政府、行业规定、第三方编制）、询价（平均价、中位价、最高价及其他合理的计算方式）、直线插入法计算公式、信息价（可结合价格变化趋势和合理预期）、成本计算等。");
        }
        
        // 最高限价
        BigDecimal totalPrice = report.getTotalPrice();
        if (totalPrice != null) {
            placeholders.put("totalPrice", String.format("%.2f", totalPrice));
            placeholders.put("unitPrice", String.format("%.2f", totalPrice));
            // 这里可以添加折扣率和优惠率的计算
            placeholders.put("discount", "0.95");
            placeholders.put("preferential", "5%");
        }
    }

    /**
     * 如果模板不存在，使用代码生成Word文档
     */
    private XWPFDocument createReportDocumentProgrammatically(ProjectReportEntity report) {
        XWPFDocument document = new XWPFDocument();
        
        // 创建标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        // 修复这里：使用关联的项目对象获取项目名称
        String projectName = report.getProject() != null ? report.getProject().getName() : "未知项目";
        titleRun.setText(projectName + "项目采购最高限价编制说明");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        
        // 创建副标题
        XWPFParagraph subtitleParagraph = document.createParagraph();
        subtitleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun subtitleRun = subtitleParagraph.createRun();
        subtitleRun.setText("(适用所有采购项目)");
        subtitleRun.setFontSize(12);
        
        // 创建各个章节
        createSection(document, "一、编制依据", "");
        createSection(document, "二、限价类型", "总价、单价、折扣率、优惠率等。");
        createSection(document, "三、计算方法", "直接使用（如政府、行业规定、第三方编制）、询价（平均价、中位价、最高价及其他合理的计算方式）、直线插入法计算公式、信息价（可结合价格变化趋势和合理预期）、成本计算等。");
        
        // 最高限价部分
        BigDecimal totalPrice = report.getTotalPrice();
        String priceContent = "总价 XX 万元，单价 XX 元，在某某标准基础上打 XX 折，在某某标准基础上优惠 XX%等。";
        if (totalPrice != null) {
            priceContent = String.format("总价 %.2f 元，单价 %.2f 元，在某某标准基础上打 0.95 折，在某某标准基础上优惠 5%%等。", 
                    totalPrice, totalPrice);
        }
        createSection(document, "四、最高限价", priceContent);
        
        // 创建签名区域
        createSignatureArea(document);
        
        return document;
    }

    /**
     * 创建文档章节
     */
    private void createSection(XWPFDocument document, String title, String content) {
        // 创建章节标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(14);
        
        // 创建章节内容
        if (content != null && !content.isEmpty()) {
            XWPFParagraph contentParagraph = document.createParagraph();
            contentParagraph.setAlignment(ParagraphAlignment.LEFT);
            XWPFRun contentRun = contentParagraph.createRun();
            contentRun.setText(content);
            contentRun.setFontSize(12);
        }
    }

    /**
     * 创建签名区域
     */
    private void createSignatureArea(XWPFDocument document) {
        // 添加空行
        document.createParagraph();
        
        // 创建表格
        XWPFTable table = document.createTable(1, 2);
        
        // 设置表格宽度
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        CTTblWidth tblWidth = tblPr.isSetTblW() ? tblPr.getTblW() : tblPr.addNewTblW();
        tblWidth.setW(BigInteger.valueOf(9000));
        tblWidth.setType(org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth.DXA);
        
        // 填充表格内容
        XWPFTableRow row = table.getRow(0);
        
        // 编制人
        XWPFTableCell cell1 = row.getCell(0);
        XWPFParagraph paragraph1 = cell1.getParagraphArray(0);
        XWPFRun run1 = paragraph1.createRun();
        run1.setText("编制人（签字）：");
        run1.addCarriageReturn();
        run1.addCarriageReturn();
        run1.setText("日期：        年    月    日");
        
        // 编制部门负责人
        XWPFTableCell cell2 = row.getCell(1);
        XWPFParagraph paragraph2 = cell2.getParagraphArray(0);
        XWPFRun run2 = paragraph2.createRun();
        run2.setText("编制部门负责人（签字）：");
        run2.addCarriageReturn();
        run2.addCarriageReturn();
        run2.setText("日期：        年    月    日");
    }



    /**
     * 将文本分割成多行
     */
    private String[] splitTextIntoLines(String text, int maxCharsPerLine) {
        if (text == null || text.isEmpty()) {
            return new String[0];
        }
        
        List<String> lines = new ArrayList<>();
        int length = text.length();
        int start = 0;
        
        while (start < length) {
            int end = Math.min(start + maxCharsPerLine, length);
            if (end < length) {
                // 尝试在空格、逗号、句号等处断行
                int breakPoint = text.lastIndexOf(' ', end);
                int commaPoint = text.lastIndexOf('，', end);
                int periodPoint = text.lastIndexOf('。', end);
                int maxBreakPoint = Math.max(Math.max(breakPoint, commaPoint), periodPoint);
                
                if (maxBreakPoint > start) {
                    end = maxBreakPoint + 1;
                }
            }
            
            lines.add(text.substring(start, end));
            start = end;
        }
        
        return lines.toArray(new String[0]);
    }
    @GetMapping("/export-pdf")
    @Operation(summary = "导出项目报告为PDF", description = "传入项目ID")
    public void exportReportPdf(@RequestParam Long projectId, HttpServletResponse response,@RequestParam String tableItems,@RequestParam String tabledata) throws Exception {
        // 获取报告详情
        ProjectReportEntity report = projectReportService.getProjectReport(projectId);
        if (report == null) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("请先保存报告");
            return;
        }
        try (InputStream is = getClass().getResourceAsStream("/doctemplete/peixun.docx")) {
            if (is == null) {
                log.error("模板文件不存在");
            }
            byte[] bytes = is.readAllBytes();
            log.error("成功读取模板文件，大小：" + bytes.length + " 字节");
        } catch (Exception e) {
            log.error("读取模板失败: " + e.getMessage());
            return;
        }
        // 加载Word模板
        XWPFDocument document;
        try {
            document = WordTemplateUtil.loadTemplate("doctemplete/peixun.docx");
        } catch (IOException e) {
            log.error("加载报告模板失败", e);
            document = createReportDocumentProgrammatically(report);
        }

        // 准备替换的占位符数据
        Map<String, String> placeholders = new HashMap<>();

        // 使用关联的项目信息
        if (report.getProject() != null) {
            placeholders.put("projectName", report.getProject().getName());
            placeholders.put("projectType", report.getProject().getType());
//            placeholders.put("", report.getProject().getContent());
            placeholders.put("projectBudget", report.getProject().getBudget() != null ?
                    report.getProject().getBudget().toString() : "");
            placeholders.put("procurementMethod", report.getProject().getProcurementMethod());
            placeholders.put("projectContent", buildContent( tableItems,tabledata));
        }

        // 解析报告内容JSON
        Map<String, Object> reportContent = parseReportContent(report.getReportContent());

        // 填充报告内容
        fillReportPlaceholders(placeholders, reportContent, report);

        // 替换模板中的占位符
        WordTemplateUtil.replacePlaceholders(document, placeholders);

        // 将 Word 文档转为 PDF
        ByteArrayOutputStream docxOutputStream = new ByteArrayOutputStream();
        document.write(docxOutputStream);
        document.close();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String currentDate = sdf.format(new Date());

        try {
            // 使用 Docx4j 将 DOCX 转 PDF
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();

            DocxToPdfConverter.convertDocxToPdf(new ByteArrayInputStream(docxOutputStream.toByteArray()), pdfOutputStream);


            // 设置响应头
            String fileName = (report.getProject() != null ? report.getProject().getName() : "项目") + "最高限价计算报告"+currentDate+".pdf";
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

            // 输出 PDF
            response.getOutputStream().write(pdfOutputStream.toByteArray());
        } catch (Exception e) {

            // 如果PDF转换失败，降级为DOCX输出
            String fileName = (report.getProject() != null ? report.getProject().getName() : "项目") + "最高限价计算报告"+currentDate+".docx";
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

            // 输出 DOCX
            response.getOutputStream().write(docxOutputStream.toByteArray());
        }
        response.getOutputStream().flush();
    }

    /**
     * 待添加其他类型的实现
     * @param jsonString
     * @param tabledata
     * @return
     */
    private static String buildContent(String jsonString,String tabledata) {
        JSONArray tabledataArray = new JSONArray(tabledata);
        StringBuilder resultTable = new StringBuilder();
        
        // 添加空值检查
        if(tabledataArray.length() > 0 && tabledataArray.getJSONObject(0).has("trainingLocation")) {
            resultTable.append("培训方式:").
                    append(tabledataArray.getJSONObject(0).getString("trainingLocation"))
                    .append(",人数:").append(tabledataArray.getJSONObject(0).getInt("trainingPeople"))
                    .append("人,每日学时:").append(tabledataArray.getJSONObject(0).getInt("hoursPerDay"))
                    .append("小时,培训天数:").append(tabledataArray.getJSONObject(0).getInt("trainingDays"))
                    .append("天。\n");
        }

        JSONArray jsonArray = new JSONArray(jsonString);
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String name = jsonObject.getString("name");
            Integer unitPrice = jsonObject.getInt("unitPrice");
            String unit = jsonObject.getString("unit");
            Integer quantity = jsonObject.getInt("quantity");
            Integer totalPrice = jsonObject.getInt("totalPrice");

            result.append("\n").append(name).append("，标准:").append(unitPrice).append("元/").append(unit).append("，共：").append(quantity).append(unit).append("，金额:").append(totalPrice).append("元；\n");
        }

        // Calculate total
        int totalSum = 0;
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            totalSum += jsonObject.getInt("totalPrice");
        }

        // Final result string
        String finalResult =resultTable.toString()  + result.append("\n合计：") .append( totalSum).append("元").toString();

       return  finalResult;
    }


}
