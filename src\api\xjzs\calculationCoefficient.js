import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '//xjzs/calculationCoefficient/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '//xjzs/calculationCoefficient/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '//xjzs/calculationCoefficient/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '//xjzs/calculationCoefficient/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '//xjzs/calculationCoefficient/submit',
    method: 'post',
    data: row
  })
}

