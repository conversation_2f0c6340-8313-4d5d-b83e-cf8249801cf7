export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  gridBtn: false,
  columnBtn: false,
  refreshBtn:false,
  column: [
    {
      label: "id;主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "规则名称",
      prop: "ruleName",
      type: "input",
      search: true,
      rules: [{
        required: true,
        message: "请输入规则名称",
        trigger: "blur"
      }]
    },
    {
      label: "适用类型",
      prop: "ruleType",
      type: "select",
      search: true,
      dicData: [
        {
          label: "材料费用",
          value: "材料费用"
        },
        {
          label: "人力费用",
          value: "人力费用"
        },
        {
          label: "服务费用",
          value: "服务费用"
        }
      ],
      rules: [{
        required: true,
        message: "请选择适用类型",
        trigger: "blur"
      }]
    },
    {
      label: "计算公式",
      prop: "calculationFormula",
      type: "textarea",
      span: 24,
      minRows: 3,
      maxRows: 6,
      rules: [{
        required: true,
        message: "请输入计算公式",
        trigger: "blur"
      }]
    },
    {
      label: "备注",
      prop: "remark",
      type: "textarea",
      span: 24,
      minRows: 2,
      maxRows: 4
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      // format: "yyyy-MM-dd HH:mm:ss",
      // valueFormat: "yyyy-MM-dd HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      span: 12,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "datetime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      hide: true,

    },
    {
      label: "状态",
      prop: "status",
      type: "select",
      search: true,
      dicData: [
        {
          label: "启用",
          value: 1
        },
        {
          label: "禁用",
          value: 0
        }
      ],
      value: 1,
      span: 12,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    }
  ]
}
