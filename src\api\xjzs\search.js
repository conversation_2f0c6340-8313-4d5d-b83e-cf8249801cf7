import request from '@/axios';

/**
 * 项目名称模糊查询
 * @param {string} name - 项目名称关键字
 * @returns {Promise} - 返回查询结果
 */
export const searchProjects = (name) => {
  return request({
    url: '/xjzs/project/list',
    method: 'get',
    params: {
      name,
      size: 10 // 限制返回10条结果
    }
  })
}

/**
 * 根据项目ID获取项目详情
 * @param {string} id - 项目ID
 * @returns {Promise} - 返回项目详情
 */
export const getProjectById = (id) => {
  return request({
    url: '/xjzs/project/detail',
    method: 'get',
    params: {
      id
    }
  })
}
