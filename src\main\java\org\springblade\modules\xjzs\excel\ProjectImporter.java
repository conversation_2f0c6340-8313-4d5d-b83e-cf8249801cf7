package org.springblade.modules.xjzs.excel;

import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;
import org.springblade.modules.xjzs.service.IProjectService;
import java.util.List;

/**
 * 项目信息表数据导入类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ProjectImporter implements ExcelImporter<ProjectExcel> {

    private final IProjectService service;
    private final Boolean isCovered;

    @Override
    public void save(List<ProjectExcel> data) {
        service.importProject(data, isCovered);
    }
}