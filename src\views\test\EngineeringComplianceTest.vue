<template>
  <div class="engineering-compliance-test">
    <h2>工程类合规检查测试</h2>
    
    <div class="test-controls">
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="项目ID">
          <el-input v-model="testForm.projectId" placeholder="请输入项目ID" />
        </el-form-item>
        
        <el-form-item label="项目类型">
          <el-select v-model="testForm.algorithmCategory" placeholder="请选择项目类型">
            <el-option label="培训类" value="培训类" />
            <el-option label="工程咨询类" value="工程咨询类" />
            <el-option label="货物类" value="货物类" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testCompliance" :loading="testing">
            测试合规检查
          </el-button>
          <el-button @click="clearResults">清空结果</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="test-results" v-if="results.length > 0">
      <h3>测试结果</h3>
      <div class="result-item" v-for="(result, index) in results" :key="index">
        <div class="result-header">
          <span class="result-type">{{ result.type }}</span>
          <span class="result-status" :class="result.status">{{ result.status }}</span>
          <span class="result-time">{{ result.timestamp }}</span>
        </div>
        <div class="result-content" v-if="result.content">
          <pre>{{ result.content }}</pre>
        </div>
        <div class="result-error" v-if="result.error">
          <span class="error-text">错误: {{ result.error }}</span>
        </div>
      </div>
    </div>
    
    <div class="streaming-output" v-if="streamingContent">
      <h3>实时输出</h3>
      <div class="streaming-content">
        <pre>{{ streamingContent }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { checkComplianceWithDify, checkEngineeringComplianceWithDify } from '@/api/xjzs/projectReport';

export default {
  name: 'EngineeringComplianceTest',
  setup() {
    const testForm = reactive({
      projectId: '',
      algorithmCategory: '工程咨询类'
    });
    
    const testing = ref(false);
    const results = ref([]);
    const streamingContent = ref('');
    
    const testCompliance = async () => {
      if (!testForm.projectId) {
        ElMessage.error('请输入项目ID');
        return;
      }
      
      testing.value = true;
      streamingContent.value = '';
      
      try {
        const isEngineering = testForm.algorithmCategory === '工程咨询类';
        const checkFunction = isEngineering ? checkEngineeringComplianceWithDify : checkComplianceWithDify;
        const apiType = isEngineering ? '工程类合规检查' : '培训类合规检查';
        
        console.log(`开始测试${apiType}，项目ID: ${testForm.projectId}`);
        
        let allContent = '';
        
        const response = await checkFunction(testForm.projectId, (progressData) => {
          // 实时更新流式内容
          let content = '';

          if (progressData.event === 'text_chunk' && progressData.data && progressData.data.text) {
            content = progressData.data.text;
          } else if (progressData.event === 'workflow_finished' && progressData.data &&
                    progressData.data.outputs && progressData.data.outputs.text) {
            content = progressData.data.outputs.text;
            console.log(`${apiType}完成，最终内容:`, content);
          }

          if (content) {
            streamingContent.value += content;
            allContent += content;
          }
        });
        
        // 添加测试结果
        results.value.unshift({
          type: apiType,
          status: 'success',
          content: allContent || (response.data ? response.data.detail : response),
          timestamp: new Date().toLocaleString(),
          error: null
        });
        
        ElMessage.success(`${apiType}测试完成`);
        
      } catch (error) {
        console.error('合规检查测试失败:', error);
        
        results.value.unshift({
          type: testForm.algorithmCategory === '工程咨询类' ? '工程类合规检查' : '培训类合规检查',
          status: 'error',
          content: null,
          timestamp: new Date().toLocaleString(),
          error: error.message || '未知错误'
        });
        
        ElMessage.error('合规检查测试失败: ' + (error.message || '未知错误'));
      } finally {
        testing.value = false;
      }
    };
    
    const clearResults = () => {
      results.value = [];
      streamingContent.value = '';
    };
    
    return {
      testForm,
      testing,
      results,
      streamingContent,
      testCompliance,
      clearResults
    };
  }
};
</script>

<style scoped>
.engineering-compliance-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-results {
  margin-top: 20px;
}

.result-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.result-header {
  background: #f8f9fa;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
}

.result-type {
  font-weight: bold;
  color: #333;
}

.result-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.result-status.success {
  background: #d4edda;
  color: #155724;
}

.result-status.error {
  background: #f8d7da;
  color: #721c24;
}

.result-time {
  color: #666;
  font-size: 12px;
}

.result-content {
  padding: 15px;
  background: #fff;
}

.result-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.result-error {
  padding: 15px;
  background: #fff3cd;
}

.error-text {
  color: #856404;
}

.streaming-output {
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.streaming-output h3 {
  background: #f8f9fa;
  margin: 0;
  padding: 10px 15px;
  border-bottom: 1px solid #ddd;
}

.streaming-content {
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.streaming-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
