/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 培训费用 Excel实体类
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class TrainingFeeExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

//	商品sku	商品名称	品牌	一级分类	二级分类	三级分类	实时价	协议价


	/**
	 * 费用编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("商品sku")
	private String feeCode;

	/**
	 * 费用名称
	 */
	@ColumnWidth(30)
	@ExcelProperty("商品名称")
	private String feeName;

	/**
	 * 费用类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("费用类型")
	private String feeType;

	/**
	 * 品牌
	 */
	@ColumnWidth(20)
	@ExcelProperty("品牌")
	private String brand;

	/**
	 * 一级分类
	 */
	@ColumnWidth(20)
	@ExcelProperty("一级分类")
	private String firstCategory;

	/**
	 * 二级分类
	 */
	@ColumnWidth(20)
	@ExcelProperty("二级分类")
	private String secondCategory;

	/**
	 * 三级分类 (可选)
	 */
	@ColumnWidth(20)
	@ExcelProperty("三级分类")
	private String thirdCategory;

	/**
	 * 三级分类 (可选)
	 */
	@ColumnWidth(20)
	@ExcelProperty("实时价")
	private String realTimePrice;

	/**
	 * 价格
	 */
	@ColumnWidth(15)
	@ExcelProperty("协议价")
	private String agreementPrice;

}

