import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/xjzs/projectInquiryRecord/project-inquiry-list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      type: 'project' // 指定查询项目询价记录
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/xjzs/projectInquiryRecord/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/xjzs/projectInquiryRecord/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/xjzs/projectInquiryRecord/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/xjzs/projectInquiryRecord/submit',
    method: 'post',
    data: row
  })
}

export const exportInquiry = (id) => {
  return request({
    url: '/xjzs/projectInquiryRecord/export',
    method: 'get',
    params: {
      id
    },
    responseType: 'blob'
  })
}

export const exportInquiryReply = (id) => {
  return request({
    url: '/xjzs/projectInquiryRecord/exportReply',
    method: 'get',
    params: {
      id
    },
    responseType: 'blob'
  })
}

export const uploadQuotation = (id, file) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('inquiryId', id);
  return request({
    url: '/xjzs/projectInquiryRecord/upload-quotation',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

export const getSupplierList = (inquiryId) => {
  return request({
    url: '/xjzs/projectInquiryRecord/supplier-list',
    method: 'get',
    params: {
      inquiryId
    }
  })
}
