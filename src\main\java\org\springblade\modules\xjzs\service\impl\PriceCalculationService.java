/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: Chill <PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 价格计算服务
 * 实现箱线图分析、四分位距（IQR）异常点检测等统计算法
 */
@Slf4j
@Service
public class PriceCalculationService {

    /**
     * 使用箱线图分析，基于四分位距（IQR）找异常点并剔除
     * 剔除异常值后，取最高十个价格的均价作为最高限价
     *
     * @param prices 价格列表
     * @return 计算得出的最高限价
     */
    public double calculateMaximumPrice(List<Double> prices) {
        if (prices == null || prices.isEmpty()) {
            return 0.0;
        }

        // 1. 排序价格列表
        List<Double> sortedPrices = new ArrayList<>(prices);
        Collections.sort(sortedPrices);

        // 2. 计算四分位数
        double q1 = calculateQuartile(sortedPrices, 0.25);
        double q3 = calculateQuartile(sortedPrices, 0.75);
        
        // 3. 计算四分位距（IQR）
        double iqr = q3 - q1;
        
        // 4. 定义异常值的上下界限
        double lowerBound = q1 - 1.5 * iqr;
        double upperBound = q3 + 1.5 * iqr;
        
        log.info("价格分析 - Q1: {}, Q3: {}, IQR: {}, 下界: {}, 上界: {}", q1, q3, iqr, lowerBound, upperBound);
        
        // 5. 剔除异常值
        List<Double> normalPrices = new ArrayList<>();
        for (Double price : sortedPrices) {
            if (price >= lowerBound && price <= upperBound) {
                normalPrices.add(price);
            }
        }
        
        log.info("价格分析 - 原始价格数量: {}, 剔除异常值后数量: {}", prices.size(), normalPrices.size());
        
        // 6. 如果剔除异常值后没有数据，则返回原始数据的平均值
        if (normalPrices.isEmpty()) {
            return calculateAverage(sortedPrices);
        }
        
        // 7. 对剔除异常值后的价格进行排序（降序）
        normalPrices.sort(Collections.reverseOrder());
        
        // 8. 取最高十个价格（如果不足十个则取全部）
        int count = Math.min(10, normalPrices.size());
        List<Double> topPrices = normalPrices.subList(0, count);
        
        double maxPrice;
        // 9. 如果价格数量不足10个，直接取最高值；否则计算最高十个价格的均价
        if (normalPrices.size() < 10) {
            maxPrice = normalPrices.get(0); // 直接取最高值
            log.info("价格分析 - 价格数量不足10个，直接取最高值: {}", maxPrice);
        } else {
            maxPrice = calculateAverage(topPrices);
            log.info("价格分析 - 取最高{}个价格的均价: {}", count, maxPrice);
        }
        
        return maxPrice;
    }
    
    /**
     * 计算四分位数
     *
     * @param sortedData 已排序的数据
     * @param percentile 百分位数（0.25表示第一四分位数，0.75表示第三四分位数）
     * @return 四分位数值
     */
    private double calculateQuartile(List<Double> sortedData, double percentile) {
        int n = sortedData.size();
        double pos = percentile * (n - 1);
        int intPos = (int) pos;
        double fraction = pos - intPos;
        
        if (n == 0) {
            return 0.0;
        }
        
        if (intPos + 1 < n) {
            return sortedData.get(intPos) + fraction * (sortedData.get(intPos + 1) - sortedData.get(intPos));
        } else {
            return sortedData.get(intPos);
        }
    }
    
    /**
     * 计算平均值
     *
     * @param data 数据列表
     * @return 平均值
     */
    private double calculateAverage(List<Double> data) {
        if (data == null || data.isEmpty()) {
            return 0.0;
        }
        
        double sum = 0.0;
        for (Double value : data) {
            sum += value;
        }
        
        return sum / data.size();
    }
}
