package org.springblade.modules.xjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springblade.modules.xjzs.service.ITrainingFeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 培训类审计服务实现
 */
@Service
@Slf4j
public class TrainingAuditServiceImpl implements IAuditService {
    
    private static final int HOURS_PER_DAY = 8;
    private static final String DEFAULT_TITLE = "副高级职称";
    private static final BigDecimal COMPREHENSIVE_RATE = new BigDecimal("550"); // 综合定额-三类培训（处级及以下）550元/人·天
    private static final BigDecimal DEFAULT_RATE = new BigDecimal("300"); // 默认费率
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private ITrainingFeeService trainingFeeService;
    
    @Override
    public String getServiceType() {
        return "培训类";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        try {
            log.info("开始计算培训类审计费用: {}", params);
            
            // 解析培训数据
            List<Map<String, Object>> trainingData = null;
            if (StringUtil.isNotBlank(params.getTableData())) {
                try {
                    trainingData = objectMapper.readValue(params.getTableData(), 
                        new TypeReference<List<Map<String, Object>>>() {});
                } catch (Exception e) {
                    log.error("解析培训数据失败", e);
                }
            }
            
            if (trainingData == null || trainingData.isEmpty()) {
                return AuditResult.fail("培训数据为空");
            }
            
            // 创建计算结果
            AuditResult result = AuditResult.success(BigDecimal.ZERO);
            
            // 添加计算思路说明
            List<String> calculationProcess = new ArrayList<>();
            
            // 总费用
            BigDecimal totalCost = BigDecimal.ZERO;
            
            // 遍历每一行培训数据
            for (Map<String, Object> trainingRow : trainingData) {
                // 获取培训场景（线上/线下）
                String trainingLocation = (String) trainingRow.get("trainingLocation");
                boolean isOffline = "线下".equals(trainingLocation);
                
                calculationProcess.add("确定培训类型：" + (isOffline ? "由于选择了线下培训，因此该培训是线下培训。" : "由于选择了线上培训，因此该培训是线上培训。"));
                
                // 获取师资职称
                String teacherTitle = (String) trainingRow.get("teacherTitle");
                if (StringUtil.isBlank(teacherTitle)) {
                    teacherTitle = DEFAULT_TITLE;
                }
                calculationProcess.add("确定师资职称：" + teacherTitle);
                
                // 获取每天学时
                Integer hoursPerDay = null;
                if (trainingRow.get("hoursPerDay") != null) {
                    if (trainingRow.get("hoursPerDay") instanceof Integer) {
                        hoursPerDay = (Integer) trainingRow.get("hoursPerDay");
                    } else {
                        hoursPerDay = Integer.valueOf(trainingRow.get("hoursPerDay").toString());
                    }
                }
                if (hoursPerDay == null || hoursPerDay <= 0) {
                    hoursPerDay = HOURS_PER_DAY;
                }
                calculationProcess.add("确定每天学时：" + hoursPerDay + "学时/天。");
                
                // 获取培训天数
                Integer trainingDays = null;
                if (trainingRow.get("trainingDays") != null) {
                    if (trainingRow.get("trainingDays") instanceof Integer) {
                        trainingDays = (Integer) trainingRow.get("trainingDays");
                    } else {
                        trainingDays = Integer.valueOf(trainingRow.get("trainingDays").toString());
                    }
                }
                if (trainingDays == null || trainingDays <= 0) {
                    trainingDays = 1;
                }
                calculationProcess.add("确定培训天数：" + trainingDays + "天。");
                
                // 获取培训人数（仅线下培训需要）
                Integer trainingPeople = null;
                if (trainingRow.get("trainingPeople") != null) {
                    if (trainingRow.get("trainingPeople") instanceof Integer) {
                        trainingPeople = (Integer) trainingRow.get("trainingPeople");
                    } else {
                        trainingPeople = Integer.valueOf(trainingRow.get("trainingPeople").toString());
                    }
                }
                if (trainingPeople == null) {
                    trainingPeople = 0;
                }
                calculationProcess.add("确定培训人数：" + trainingPeople + "人。");
                
                // 计算总学时
                int totalHours = trainingDays * hoursPerDay;
                calculationProcess.add("确定总学时数：总学时数 = 培训天数 × 每天学时 = " + trainingDays + " × " + hoursPerDay + " = " + totalHours + " 学时。");
                
                // 创建师资信息
                Map<String, Object> teacherInfo = new HashMap<>();
                teacherInfo.put("title", teacherTitle);
                teacherInfo.put("hours", totalHours);
                
                // 计算师资费用
                BigDecimal teacherFee = calculateTeacherFee(Collections.singletonList(teacherInfo), result, calculationProcess);
                
                // 计算综合费用（如果是线下培训）
                BigDecimal comprehensiveFee = BigDecimal.ZERO;
                if (isOffline && trainingPeople > 0) {
                    comprehensiveFee = calculateComprehensiveFee(trainingPeople, trainingDays, result, calculationProcess);
                }
                
                // 计算该行的总费用
                BigDecimal rowCost;
                if (isOffline) {
                    // 线下：培训费用 = ∑（师资费标准×学时）+（人数×天数×550）
                    rowCost = teacherFee.add(comprehensiveFee);
                    calculationProcess.add("确定该行培训预算：总预算 = 师资费总费用 + 综合定额费总费用 = " + 
                        teacherFee.toPlainString() + " 元 + " + comprehensiveFee.toPlainString() + 
                        " 元 = " + rowCost.toPlainString() + " 元。");
                } else {
                    // 线上：培训费用 = ∑（师资费×学时）
                    rowCost = teacherFee;
                    calculationProcess.add("确定该行培训预算：总预算 = 师资费总费用 = " + teacherFee.toPlainString() + " 元。");
                }
                
                // 累加到总费用
                totalCost = totalCost.add(rowCost);
            }
            
            // 设置最终总费用
            calculationProcess.add("确定最终总预算：" + totalCost.toPlainString() + " 元。");
            result.setTotalCost(totalCost);
            result.setCalculationProcess(calculationProcess);
            
            return result;
        } catch (Exception e) {
            log.error("计算培训类审计费用时发生错误", e);
            return AuditResult.fail("计算错误: " + e.getMessage());
        }
    }
    
    /**
     * 计算师资费用
     */
    private BigDecimal calculateTeacherFee(List<Map<String, Object>> teacherTitles, AuditResult result, List<String> calculationProcess) {
        BigDecimal totalFee = BigDecimal.ZERO;
        
        StringBuilder teacherFeeDescription = new StringBuilder("确定师资费：");
        
        for (Map<String, Object> teacher : teacherTitles) {
            String title = (String) teacher.get("title");
            Integer hours = Integer.valueOf(teacher.get("hours").toString());
            
            // 根据职称获取费率
            BigDecimal rate = getTeacherRate(title);
            
            // 计算该讲师的费用
            BigDecimal fee = rate.multiply(new BigDecimal(hours));
            
            teacherFeeDescription.append(title).append("，单价为").append(rate).append("元/学时。所以，师资费总费用 = ")
                .append(hours).append(" 学时 × ").append(rate).append(" 元/学时 = ").append(fee).append(" 元。");
            
            // 添加计算步骤
            Map<String, Object> detail = new HashMap<>();
            detail.put("type", "师资费");
            detail.put("title", title);
            detail.put("hours", hours);
            detail.put("rate", rate);
            detail.put("fee", fee);
            detail.put("description", title + "讲师费用: " + hours + "学时 × " + rate + "元/学时 = " + fee + "元");
            result.addDetail(detail);
            
            totalFee = totalFee.add(fee);
        }
        
        calculationProcess.add(teacherFeeDescription.toString());
        
        return totalFee;
    }
    
    /**
     * 计算综合费用（线下培训）
     */
    private BigDecimal calculateComprehensiveFee(int people, int days, AuditResult result, List<String> calculationProcess) {
        // 综合定额-三类培训（处级及以下）550元/人·天
        BigDecimal fee = COMPREHENSIVE_RATE.multiply(new BigDecimal(people)).multiply(new BigDecimal(days));
        
        calculationProcess.add("确定综合定额费：单价为" + COMPREHENSIVE_RATE + "元/人·天。所以，综合定额费总费用 = " + 
            people + " 人 × " + days + " 天 × " + COMPREHENSIVE_RATE + " 元/人·天 = " + fee + " 元。");
        
        // 添加计算步骤
        Map<String, Object> detail = new HashMap<>();
        detail.put("type", "综合费用");
        detail.put("people", people);
        detail.put("days", days);
        detail.put("rate", COMPREHENSIVE_RATE);
        detail.put("fee", fee);
        detail.put("description", "综合费用: " + people + "人 × " + days + "天 × " + COMPREHENSIVE_RATE + "元/人·天 = " + fee + "元");
        result.addDetail(detail);
        
        return fee;
    }
    
    /**
     * 根据讲师职称从数据库获取费率
     * 
     * @param title 讲师职称，对应TrainingFeeEntity中的feeName字段
     * @return 对应的费率
     */
    private BigDecimal getTeacherRate(String title) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
                .eq(TrainingFeeEntity::getFeeType, "培训类")
                .eq(TrainingFeeEntity::getFeeName, title)
                .eq(TrainingFeeEntity::getIsDeleted, 0)
                .last("LIMIT 1");
            
            // 查询数据库
            TrainingFeeEntity entity = trainingFeeService.getOne(queryWrapper);
            
            // 如果找到记录，则返回对应的价格
            if (entity != null && StringUtil.isNotBlank(entity.getPrice())) {
                try {
                    return new BigDecimal(entity.getPrice());
                } catch (NumberFormatException e) {
                    log.warn("无法解析培训费用价格: {}", entity.getPrice());
                }
            }
            
            // 如果没有找到记录或价格无效，则使用默认费率
            log.warn("未找到职称 {} 的培训费用标准: {}", title, DEFAULT_RATE);
            return DEFAULT_RATE;
        } catch (Exception e) {
            log.error("获取讲师费率时发生错误", e);
            return DEFAULT_RATE;
        }
    }
}
