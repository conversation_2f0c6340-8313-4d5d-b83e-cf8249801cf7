package org.springblade.modules.xjzs.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 房产物业评估咨询服务实现类
 * 工程咨询类 -> 房产物业评估咨询
 */
@Service
@Slf4j
public class PropertyAppraisalConsultingServiceImpl implements IAuditService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public String getServiceType() {
        return "房产物业评估咨询";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        try {
            log.info("开始计算房产物业评估咨询审计费用: {}", params);
            
            // 解析表格数据
            List<Map<String, Object>> tableData = null;
            if (StringUtil.isNotBlank(params.getTableData())) {
                try {
                    tableData = objectMapper.readValue(params.getTableData(), 
                        new TypeReference<List<Map<String, Object>>>() {});
                } catch (Exception e) {
                    log.error("解析房产物业评估咨询数据失败", e);
                }
            }
            
            if (tableData == null || tableData.isEmpty()) {
                return AuditResult.fail("房产物业评估咨询数据为空");
            }
            
            // 创建计算结果
            AuditResult result = AuditResult.success(BigDecimal.ZERO);
            
            // 添加计算思路说明
            List<String> calculationProcess = new ArrayList<>();
            calculationProcess.add("=== 房产物业评估咨询费用计算开始 ===");
            
            // 总费用
            BigDecimal totalCost = BigDecimal.ZERO;
            
            // 遍历每一行数据
            for (int i = 0; i < tableData.size(); i++) {
                Map<String, Object> row = tableData.get(i);
                calculationProcess.add("\n--- 第" + (i + 1) + "行数据计算 ---");
                
                // 获取评估值
                BigDecimal appraisalValue = getDecimalValue(row, "appraisalValue");
                if (appraisalValue == null || appraisalValue.compareTo(BigDecimal.ZERO) <= 0) {
                    calculationProcess.add("警告：第" + (i + 1) + "行评估值无效或为空，跳过计算");
                    continue;
                }
                
                calculationProcess.add("确定评估值：" + appraisalValue.toPlainString() + " 万元");
                
                // 计算房产物业评估咨询费用
                BigDecimal rowCost = calculatePropertyAppraisalFee(appraisalValue, result, calculationProcess);
                
                calculationProcess.add("第" + (i + 1) + "行计算结果：" + rowCost.toPlainString() + " 元");
                totalCost = totalCost.add(rowCost);
            }
            
            // 设置最终总费用
            calculationProcess.add("\n=== 房产物业评估咨询费用计算完成 ===");
            calculationProcess.add("最终总费用：" + totalCost.toPlainString() + " 元");
            result.setTotalCost(totalCost);
            result.setCalculationProcess(calculationProcess);
            
            return result;
        } catch (Exception e) {
            log.error("计算房产物业评估咨询审计费用时发生错误", e);
            return AuditResult.fail("计算错误: " + e.getMessage());
        }
    }
    
    /**
     * 计算房产物业评估咨询费用
     */
    private BigDecimal calculatePropertyAppraisalFee(BigDecimal appraisalValue, AuditResult result, List<String> calculationProcess) {
        calculationProcess.add("开始计算房产物业评估咨询费用：");
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if (appraisalValue.compareTo(new BigDecimal("100")) <= 0) {
            // 评估值 ≤ 100万元
            fee = appraisalValue.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            calculationProcess.add("适用规则：评估值 ≤ 100万元");
            calculationProcess.add("计算公式：房产物业评估咨询 = 评估值 × 0.005");
            calculationProcess.add("计算过程：" + appraisalValue.toPlainString() + " × 10000 × 0.005 = " + fee.toPlainString() + " 元");
        } else if (appraisalValue.compareTo(new BigDecimal("1000")) <= 0) {
            // 评估值在 101~1000万元
            BigDecimal baseFee = new BigDecimal("100").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            BigDecimal excessAmount = appraisalValue.subtract(new BigDecimal("100"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025"));
            fee = baseFee.add(excessFee);
            
            calculationProcess.add("适用规则：评估值在 101~1000万元");
            calculationProcess.add("计算公式：房产物业评估咨询 = 100 × 0.005 + (评估值-100) × 0.0025");
            calculationProcess.add("基础费用：100 × 10000 × 0.005 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：(" + appraisalValue.toPlainString() + " - 100) × 10000 × 0.0025 = " + 
                excessAmount.toPlainString() + " × 10000 × 0.0025 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + baseFee.toPlainString() + " + " + excessFee.toPlainString() + " = " + fee.toPlainString() + " 元");
        } else if (appraisalValue.compareTo(new BigDecimal("2000")) <= 0) {
            // 评估值在 1001~2000万元
            BigDecimal baseFee1 = new BigDecimal("100").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            BigDecimal baseFee2 = new BigDecimal("900").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025")); // (1000-100) = 900
            BigDecimal excessAmount = appraisalValue.subtract(new BigDecimal("1000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0015"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            
            calculationProcess.add("适用规则：评估值在 1001~2000万元");
            calculationProcess.add("计算公式：房产物业评估咨询 = 100 × 0.005 + (1000-100) × 0.0025 + (评估值-1000) × 0.0015");
            calculationProcess.add("第一段：100 × 10000 × 0.005 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：(1000-100) × 10000 × 0.0025 = 900 × 10000 × 0.0025 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：(" + appraisalValue.toPlainString() + " - 1000) × 10000 × 0.0015 = " + 
                excessAmount.toPlainString() + " × 10000 × 0.0015 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + baseFee1.toPlainString() + " + " + baseFee2.toPlainString() + " + " + 
                excessFee.toPlainString() + " = " + fee.toPlainString() + " 元");
        } else {
            // 评估值 > 2000万元（按照规律推断，可能需要更多分段）
            calculationProcess.add("警告：评估值超过2000万元，当前规则未覆盖此范围，请补充计算规则");
            // 暂时按照最高段的费率计算
            BigDecimal baseFee1 = new BigDecimal("100").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            BigDecimal baseFee2 = new BigDecimal("900").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025"));
            BigDecimal baseFee3 = new BigDecimal("1000").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0015"));
            BigDecimal excessAmount = appraisalValue.subtract(new BigDecimal("2000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.001")); // 假设更高段费率为0.1%
            fee = baseFee1.add(baseFee2).add(baseFee3).add(excessFee);
            
            calculationProcess.add("临时处理：评估值 > 2000万元，按推测规则计算");
            calculationProcess.add("第一段：100 × 10000 × 0.005 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：900 × 10000 × 0.0025 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：1000 × 10000 × 0.0015 = " + baseFee3.toPlainString() + " 元");
            calculationProcess.add("第四段（推测）：" + excessAmount.toPlainString() + " × 10000 × 0.001 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        }
        
        // 添加计算明细
        Map<String, Object> detail = new HashMap<>();
        detail.put("type", "房产物业评估咨询");
        detail.put("appraisalValue", appraisalValue);
        detail.put("fee", fee);
        detail.put("description", "房产物业评估咨询费用: 评估值" + appraisalValue + "万元 = " + fee + "元");
        result.addDetail(detail);
        
        return fee;
    }
    
    /**
     * 从Map中获取BigDecimal值的辅助方法
     */
    private BigDecimal getDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return new BigDecimal(value.toString());
        } else if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        return null;
    }
}