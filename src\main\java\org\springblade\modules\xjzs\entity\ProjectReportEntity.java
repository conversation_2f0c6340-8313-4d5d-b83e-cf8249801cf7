package org.springblade.modules.xjzs.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.modules.xjzs.pojo.entity.ProjectEntity;

/**
 * 项目报告实体类
 */
@Data
@TableName("xjzs_project_report")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目报告实体")
public class ProjectReportEntity  extends TenantEntity {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private Long projectId;

    /**
     * 项目信息（非数据库字段，通过关联查询获取）
     */
    @TableField(exist = false)
    @Schema(description = "项目信息")
    private ProjectEntity project;

    /**
     * 报告编号
     */
    @Schema(description = "报告编号")
    private String reportId;

    /**
     * 计算方式
     */
    @Schema(description = "计算方式")
    private String calculationMethod;

    /**
     * 算法类别
     */
    @Schema(description = "算法类别")
    private String algorithmCategory;

    /**
     * 总价
     */
    @Schema(description = "总价")
    private BigDecimal totalPrice;

    /**
     * 报告内容（JSON格式）
     */
    @Schema(description = "报告内容（JSON格式）")
    private String reportContent;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @Schema(description = "是否删除")
    private Integer isDeleted;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 合规校验内容（JSON格式）
     */
    @Schema(description = "合规校验内容（JSON格式）")
    private String checkReportContent;

}
