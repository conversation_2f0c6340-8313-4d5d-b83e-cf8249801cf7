/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.xjzs.excel.TrainingFeeImporter;
import org.springblade.modules.xjzs.pojo.entity.ProductAttributesEntity;
import org.springblade.modules.xjzs.pojo.vo.ProductAttributesVO;
import org.springblade.modules.xjzs.service.IProductAttributesService;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity;
import org.springblade.modules.xjzs.pojo.vo.TrainingFeeVO;
import org.springblade.modules.xjzs.excel.TrainingFeeExcel;
import org.springblade.modules.xjzs.wrapper.TrainingFeeWrapper;
import org.springblade.modules.xjzs.pojo.dto.ImportResult;
import org.springblade.modules.xjzs.service.ITrainingFeeService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.ArrayList;

/**
 * 计价标准 控制器
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/trainingFee")
@Tag(name = "计价标准", description = "计价标准接口")
public class TrainingFeeController extends BladeController {

	private final ITrainingFeeService trainingFeeService;
	private final IProductAttributesService productAttributesService;
	/**
	 * 计价标准 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入trainingFee")
	public R<TrainingFeeVO> detail(TrainingFeeEntity trainingFee) {
		TrainingFeeEntity detail = trainingFeeService.getOne(Condition.getQueryWrapper(trainingFee));
		return R.data(TrainingFeeWrapper.build().entityVO(detail));
	}
	/**
	 * 计价标准 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入trainingFee")
	public R<IPage<TrainingFeeVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> trainingFee, Query query) {
		IPage<TrainingFeeEntity> pages = trainingFeeService.page(Condition.getPage(query), Condition.getQueryWrapper(trainingFee, TrainingFeeEntity.class));
		return R.data(TrainingFeeWrapper.build().pageVO(pages));
	}

	/**
	 * 计价标准 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入trainingFee")
	public R<IPage<TrainingFeeVO>> page(TrainingFeeVO trainingFee, Query query) {
		IPage<TrainingFeeVO> pages = trainingFeeService.selectTrainingFeePage(Condition.getPage(query), trainingFee);
		return R.data(pages);
	}

	/**
	 * 计价标准 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入trainingFee")
	public R<Boolean> save(@Valid @RequestBody TrainingFeeEntity trainingFee) {
		return R.status(trainingFeeService.save(trainingFee));
	}

	/**
	 * 计价标准 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入trainingFee")
	public R<Boolean> update(@Valid @RequestBody TrainingFeeEntity trainingFee) {
		return R.status(trainingFeeService.updateById(trainingFee));
	}

	/**
	 * 计价标准 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入trainingFee")
	public R<Boolean> submit(@Valid @RequestBody TrainingFeeEntity trainingFee) {
		return R.status(trainingFeeService.saveOrUpdate(trainingFee));
	}

	/**
	 * 计价标准 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R<Boolean> remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(trainingFeeService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-trainingFee")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入trainingFee")
	public void exportTrainingFee(@Parameter(hidden = true) @RequestParam Map<String, Object> trainingFee, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<TrainingFeeEntity> queryWrapper = Condition.getQueryWrapper(trainingFee, TrainingFeeEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(TrainingFee::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(TrainingFeeEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<TrainingFeeExcel> list = trainingFeeService.exportTrainingFee(queryWrapper);
		ExcelUtil.export(response, "计价标准数据" + DateUtil.time(), "计价标准数据表", list, TrainingFeeExcel.class);
	}

	/**
	 * 导入计价标准数据
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "导入数据", description = "传入excel")
	public R<ImportResult> importTrainingFee(@RequestParam(value = "file", required = false) MultipartFile file,
									  @RequestParam("isCovered") Integer isCovered) {
		try {
			List<TrainingFeeExcel> data = ExcelUtil.read(file, TrainingFeeExcel.class);
			ImportResult result = trainingFeeService.importTrainingFee(data, isCovered != null && isCovered == 1);

			if (result.isSuccess()) {
				return R.data(result);
			} else {
				return R.fail(result.getMessage());
			}
		} catch (Exception e) {
			log.error("导入计价标准数据失败", e);
			ImportResult result = ImportResult.fail("导入失败: " + e.getMessage());
			return R.fail(result.getMessage());
		}
	}

	/**
	 * 计算最高限价
	 */
	@GetMapping("/calculate-max-price")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "计算最高限价", description = "根据分类和品牌计算最高限价")
	public R<Double> calculateMaxPrice(@RequestParam(required = false) String categoryId,
									  @RequestParam(required = false) String brandId) {
		try {
			Double maxPrice = trainingFeeService.calculateMaximumPrice(categoryId, brandId);
			return R.data(maxPrice);
		} catch (Exception e) {
			log.error("计算最高限价失败", e);
			return R.fail("计算失败: " + e.getMessage());
		}
	}

	/**
	 * 下载导入模板
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "下载模板", description = "")
	public void downloadTemplate(HttpServletResponse response) throws IOException {
		List<TrainingFeeExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "计价标准导入模板", "计价标准", list, TrainingFeeExcel.class);
	}

	/**
	 * 获取京东慧采标准表分类数据
	 */
	@GetMapping("/jd-categories")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "获取京东慧采分类数据", description = "获取费用名称、一级分类、二级分类数据")
	public R<Map<String, List<Map<String, String>>>> getJdCategories() {
		try {
			Map<String, List<Map<String, String>>> categories = trainingFeeService.getJdCategories();
			return R.data(categories);
		} catch (Exception e) {
			log.error("获取京东慧采分类数据失败", e);
			return R.fail("获取分类数据失败: " + e.getMessage());
		}
	}

	/**
	 * 根据费用名称获取商品属性
	 */
	@GetMapping("/product-attributes")
	@ApiOperationSupport(order = 14)
	@Operation(summary = "获取商品属性", description = "根据费用名称获取对应的商品属性")
	public R<List<ProductAttributesVO>> getProductAttributes(@RequestParam String feeName,@RequestParam String unit) {
		try {
			List<ProductAttributesVO> attributes = productAttributesService.getProductAttributesByUnit(feeName,unit);
			return R.data(attributes);
		} catch (Exception e) {
			log.error("获取商品属性失败", e);
			return R.fail("获取商品属性失败: " + e.getMessage());
		}
	}

	/**
	 * 获取培训类标准表分类数据
	 */
	@GetMapping("/training-categories")
	@ApiOperationSupport(order = 14)
	@Operation(summary = "获取培训类分类数据", description = "获取费用名称、一级分类、二级分类、师资职称数据")
	public R<Map<String, List<Map<String, String>>>> getTrainingCategories() {
		try {
			Map<String, List<Map<String, String>>> categories = trainingFeeService.getTrainingCategories();
			return R.data(categories);
		} catch (Exception e) {
			log.error("获取培训类分类数据失败", e);
			return R.fail("获取分类数据失败: " + e.getMessage());
		}
	}

	/**
	 * 获取培训类属性数据
	 */
	@GetMapping("/training-attributes")
	@ApiOperationSupport(order = 15)
	@Operation(summary = "获取培训类属性数据", description = "根据费用名称获取培训类属性")
	public R<Map<String, Object>> getTrainingAttributes(@RequestParam String feeName) {
		try {
			Map<String, Object> attributes = trainingFeeService.getTrainingAttributes(feeName);
			return R.data(attributes);
		} catch (Exception e) {
			log.error("获取培训类属性数据失败", e);
			return R.fail("获取属性数据失败: " + e.getMessage());
		}
	}
	/**
	 * 根据费用名称获取不同的单位
	 */
	@GetMapping("/dis-unit")
	@ApiOperationSupport(order = 16)
	@Operation(summary = "获取单位列表", description = "根据费用名称获取不同的单位")
	public R<List<TrainingFeeVO>> getDisUnit(@RequestParam String feeName) {
		try {
			List<TrainingFeeVO> units = trainingFeeService.getDisUnit(feeName);
			return R.data(units);
		} catch (Exception e) {
			log.error("获取单位列表失败", e);
			return R.fail("获取单位列表失败: " + e.getMessage());
		}
	}


}

