package org.springblade.modules.xjzs.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 工程项目设计服务实现类
 * 工程咨询类 -> 工程项目设计
 */
@Service
@Slf4j
public class EngineeringDesignServiceImpl implements IAuditService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public String getServiceType() {
        return "工程项目设计";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        try {
            log.info("开始计算工程项目设计审计费用: {}", params);
            
            // 解析表格数据
            List<Map<String, Object>> tableData = null;
            if (StringUtil.isNotBlank(params.getTableData())) {
                try {
                    tableData = objectMapper.readValue(params.getTableData(), 
                        new TypeReference<List<Map<String, Object>>>() {});
                } catch (Exception e) {
                    log.error("解析工程项目设计数据失败", e);
                }
            }
            
            if (tableData == null || tableData.isEmpty()) {
                return AuditResult.fail("工程项目设计数据为空");
            }
            
            // 创建计算结果
            AuditResult result = AuditResult.success(BigDecimal.ZERO);
            
            // 添加计算思路说明
            List<String> calculationProcess = new ArrayList<>();
            calculationProcess.add("=== 工程项目设计费用计算开始 ===");
            
            // 总费用
            BigDecimal totalCost = BigDecimal.ZERO;
            
            // 遍历每一行数据
            for (int i = 0; i < tableData.size(); i++) {
                Map<String, Object> row = tableData.get(i);
                calculationProcess.add("\n--- 第" + (i + 1) + "行数据计算 ---");
                
                // 获取计费额
                BigDecimal billingAmount = getDecimalValue(row, "billingAmount");
                if (billingAmount == null || billingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    calculationProcess.add("警告：第" + (i + 1) + "行计费额无效或为空，跳过计算");
                    continue;
                }
                
                calculationProcess.add("确定计费额：" + billingAmount.toPlainString() + " 万元");
                
                // 获取调整系数
                BigDecimal professionalAdjustment = getDecimalValue(row, "professionalAdjustment");
                if (professionalAdjustment == null) {
                    professionalAdjustment = BigDecimal.ONE;
                }
                calculationProcess.add("专业调整系数：" + professionalAdjustment.toPlainString());
                
                BigDecimal complexityAdjustment = getDecimalValue(row, "complexityAdjustment");
                if (complexityAdjustment == null) {
                    complexityAdjustment = BigDecimal.ONE;
                }
                calculationProcess.add("工程复杂程度调整系数：" + complexityAdjustment.toPlainString());
                
                BigDecimal additionalAdjustment = getDecimalValue(row, "additionalAdjustment");
                if (additionalAdjustment == null) {
                    additionalAdjustment = BigDecimal.ONE;
                }
                calculationProcess.add("附加调整系数：" + additionalAdjustment.toPlainString());
                
                // 获取其他设计收费
                BigDecimal otherDesignFee = getDecimalValue(row, "otherDesignFee");
                if (otherDesignFee == null) {
                    otherDesignFee = BigDecimal.ZERO;
                }
                calculationProcess.add("其他设计收费：" + otherDesignFee.toPlainString() + " 万元");
                
                // 计算工程项目设计费用
                BigDecimal rowCost = calculateDesignFee(billingAmount, professionalAdjustment, 
                    complexityAdjustment, additionalAdjustment, otherDesignFee, result, calculationProcess);
                
                calculationProcess.add("第" + (i + 1) + "行计算结果：" + rowCost.toPlainString() + " 万元");
                totalCost = totalCost.add(rowCost);
            }
            
            // 设置最终总费用
            calculationProcess.add("\n=== 工程项目设计费用计算完成 ===");
            calculationProcess.add("最终总费用：" + totalCost.toPlainString() + " 万元");
            result.setTotalCost(totalCost.multiply(new BigDecimal("10000"))); // 转换为元
            result.setCalculationProcess(calculationProcess);
            
            return result;
        } catch (Exception e) {
            log.error("计算工程项目设计审计费用时发生错误", e);
            return AuditResult.fail("计算错误: " + e.getMessage());
        }
    }
    
    /**
     * 计算工程项目设计费用
     */
    private BigDecimal calculateDesignFee(BigDecimal billingAmount, BigDecimal professionalAdjustment,
                                        BigDecimal complexityAdjustment, BigDecimal additionalAdjustment,
                                        BigDecimal otherDesignFee, AuditResult result, List<String> calculationProcess) {
        calculationProcess.add("开始计算工程项目设计费用：");
        
        BigDecimal baseFee = BigDecimal.ZERO;
        
        if (billingAmount.compareTo(new BigDecimal("200")) <= 0) {
            // 计费额 ≤ 200万元
            baseFee = new BigDecimal("9.0").multiply(billingAmount).divide(new BigDecimal("200"), 6, RoundingMode.HALF_UP);
            calculationProcess.add("适用规则：计费额 ≤ 200万元");
            calculationProcess.add("基础费用计算：9.0 × " + billingAmount.toPlainString() + " / 200 = " + baseFee.toPlainString() + " 万元");
        } else if (billingAmount.compareTo(new BigDecimal("500")) <= 0) {
            // 计费额在 200~500万元
            BigDecimal ratio = billingAmount.subtract(new BigDecimal("200"))
                .divide(new BigDecimal("300"), 6, RoundingMode.HALF_UP); // (计费额-200)/(500-200)
            BigDecimal increment = ratio.multiply(new BigDecimal("20.9").subtract(new BigDecimal("9.0"))); // × (20.9-9.0)
            baseFee = new BigDecimal("9.0").add(increment);
            
            calculationProcess.add("适用规则：计费额在 200~500万元");
            calculationProcess.add("计算公式：9.0 + (计费额-200)/(500-200) × (20.9-9.0)");
            calculationProcess.add("比例计算：(" + billingAmount.toPlainString() + " - 200) / 300 = " + ratio.toPlainString());
            calculationProcess.add("增量计算：" + ratio.toPlainString() + " × (20.9 - 9.0) = " + increment.toPlainString());
            calculationProcess.add("基础费用：9.0 + " + increment.toPlainString() + " = " + baseFee.toPlainString() + " 万元");
        } else if (billingAmount.compareTo(new BigDecimal("1000")) <= 0) {
            // 计费额在 500~1000万元
            // 注意：原规则中第三段的起始值应该是20.9而不是38.8，这里按逻辑修正
            BigDecimal ratio = billingAmount.subtract(new BigDecimal("500"))
                .divide(new BigDecimal("500"), 6, RoundingMode.HALF_UP); // (计费额-500)/(1000-500)
            BigDecimal increment = ratio.multiply(new BigDecimal("38.8").subtract(new BigDecimal("20.9"))); // × (38.8-20.9)
            baseFee = new BigDecimal("20.9").add(increment);
            
            calculationProcess.add("适用规则：计费额在 500~1000万元");
            calculationProcess.add("计算公式：20.9 + (计费额-500)/(1000-500) × (38.8-20.9)");
            calculationProcess.add("注意：已修正起始值为20.9（原规则中可能有误）");
            calculationProcess.add("比例计算：(" + billingAmount.toPlainString() + " - 500) / 500 = " + ratio.toPlainString());
            calculationProcess.add("增量计算：" + ratio.toPlainString() + " × (38.8 - 20.9) = " + increment.toPlainString());
            calculationProcess.add("基础费用：20.9 + " + increment.toPlainString() + " = " + baseFee.toPlainString() + " 万元");
        } else {
            // 计费额 > 1000万元（按照规律推断）
            calculationProcess.add("警告：计费额超过1000万元，当前规则未覆盖此范围，请补充计算规则");
            // 暂时按照最高段的费率计算
            baseFee = new BigDecimal("38.8");
            calculationProcess.add("临时处理：计费额 > 1000万元，暂按38.8万元计算");
        }
        
        // 应用调整系数
        BigDecimal adjustedBaseFee = baseFee
            .multiply(professionalAdjustment)
            .multiply(complexityAdjustment)
            .multiply(additionalAdjustment);
        
        calculationProcess.add("应用调整系数：");
        calculationProcess.add("调整后基础费用 = 基础费用 × 专业调整系数 × 工程复杂程度调整系数 × 附加调整系数");
        calculationProcess.add("调整后基础费用 = " + baseFee.toPlainString() + " × " + professionalAdjustment.toPlainString() + 
            " × " + complexityAdjustment.toPlainString() + " × " + additionalAdjustment.toPlainString() + 
            " = " + adjustedBaseFee.toPlainString() + " 万元");
        
        // 加上其他设计收费
        BigDecimal totalFee = adjustedBaseFee.add(otherDesignFee);
        calculationProcess.add("最终费用 = 调整后基础费用 + 其他设计收费");
        calculationProcess.add("最终费用 = " + adjustedBaseFee.toPlainString() + " + " + otherDesignFee.toPlainString() + 
            " = " + totalFee.toPlainString() + " 万元");
        
        // 添加计算明细
        Map<String, Object> detail = new HashMap<>();
        detail.put("type", "工程项目设计");
        detail.put("billingAmount", billingAmount);
        detail.put("baseFee", baseFee);
        detail.put("professionalAdjustment", professionalAdjustment);
        detail.put("complexityAdjustment", complexityAdjustment);
        detail.put("additionalAdjustment", additionalAdjustment);
        detail.put("adjustedBaseFee", adjustedBaseFee);
        detail.put("otherDesignFee", otherDesignFee);
        detail.put("totalFee", totalFee);
        detail.put("description", "工程项目设计费用: 计费额" + billingAmount + "万元 = " + totalFee + "万元");
        result.addDetail(detail);
        
        return totalFee;
    }
    
    /**
     * 从Map中获取BigDecimal值的辅助方法
     */
    private BigDecimal getDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return new BigDecimal(value.toString());
        } else if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        return null;
    }
}