/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.xjzs.pojo.entity.SupplierInquiryRecordEntity;

import java.io.Serial;

/**
 * 供应商询价历史记录表 视图实体类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierInquiryRecordVO extends SupplierInquiryRecordEntity {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 项目名称
	 */
	@Schema(description = "项目名称")
	private String projectName;
	/**
	 * 部门名称
	 */
	@Schema(description = "部门名称")
	private String deptName;
	/**
	 * 用户名称
	 */
	@Schema(description = "用户名称")
	private String userName;
	/**
	 * 数量
	 */
	@Schema(description = "数量")
	private Double quantity;
	/**
	 * 服务名称
	 */
	@Schema(description = "服务名称")
	private String serviceName;
	/**
	 * 服务内容
	 */
	@Schema(description = "服务内容")
	private String serviceContent;
	/**
	 * 预算金额
	 */
	@Schema(description = "预算金额")
	private Double projectBudget;



}
