/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 计价标准 实体类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@TableName("xjzs_training_fee")
@Schema(description = "TrainingFee对象")
@EqualsAndHashCode(callSuper = true)
public class TrainingFeeEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 费用编码
	 */
	@Schema(description = "费用编码")
	private String feeCode;
	/**
	 * 费用名称
	 */
	@Schema(description = "费用名称")
	private String feeName;
	/**
	 * 费用类型
	 */
	@Schema(description = "费用类型")
	private String feeType;
	/**
	 * 品牌
	 */
	@Schema(description = "品牌")
	private String brand;
	/**
	 * 一级分类
	 */
	@Schema(description = "一级分类")
	private String firstCategory;
	/**
	 * 二级分类
	 */
	@Schema(description = "二级分类")
	private String secondCategory;

	/**
	 * 规格
	 */
	@Schema(description = "规格")
	private String specification;
	/**
	 * 价格
	 */
	@Schema(description = "价格")
	private String price;
	/**
	 * 价格
	 */
	@Schema(description = "单位")
	private String unit;

}
