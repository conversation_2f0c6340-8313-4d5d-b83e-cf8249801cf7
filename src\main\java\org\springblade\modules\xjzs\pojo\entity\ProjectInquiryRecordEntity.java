/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 项目询价记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@TableName("xjzs_project_inquiry_record")
@Schema(description = "ProjectInquiryRecord对象")
@EqualsAndHashCode(callSuper = true)
public class ProjectInquiryRecordEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 项目编码
	 */
	@Schema(description = "项目编码")
	private Long projectId;
	/**
	 * 产品名称
	 */
	@Schema(description = "产品名称")
	private String productName;
	/**
	 * 技术规格
	 */
	@Schema(description = "技术规格")
	private String specifications;
	/**
	 * 数量
	 */
	@Schema(description = "数量")
	private Double quantity;
	/**
	 * 质量标准
	 */
	@Schema(description = "质量标准")
	private String quantityStandard;
	/**
	 * 服务内容
	 */
	@Schema(description = "服务内容")
	private String serviceContent;
	/**
	 * 服务期限
	 */
	@Schema(description = "服务期限")
	private String servicePeriod;
	/**
	 * 服务要求
	 */
	@Schema(description = "服务要求")
	private String serviceRequirements;
	/**
	 * 截止日期
	 */
	@Schema(description = "截止日期")
	private Date deadline;
	/**
	 * 联系人姓名
	 */
	@Schema(description = "联系人姓名")
	private String contactName;
	/**
	 * 联系人电话
	 */
	@Schema(description = "联系人电话")
	private String contactPhone;
	/**
	 * 项目类别
	 */
	@Schema(description = "项目类别")
	private String projectType;
	/**
	 * 预算金额
	 */
	@Schema(description = "预算金额")
	private Double projectBudget;
	/**
	 * 采购方式
	 */
	@Schema(description = "采购方式")
	private String procurementMethod;
	/**
	 * 服务名称
	 */
	@Schema(description = "服务名称")
	private String serviceName;

}
