<template>
  <div>
    <div class="calculation-steps">
      <div class="step-item">
        <div class="step-number">1</div>
        <div class="step-text">
          <div class="step-title">项目信息确认</div>
          <ul class="step-list">
            <li>项目名称：{{ formData.projectName || '未设置' }}</li>
            <li>项目类型：{{ formData.projectType || '未设置' }}</li>
            <li>算法类型：{{ formData.algorithmCategory || '未设置' }}</li>
            <li>采购方式：{{ formData.procurementMethod || '未设置' }}</li>
          </ul>
        </div>
      </div>

      <div class="step-item">
        <div class="step-number">2</div>
        <div class="step-text">
          <div class="step-title">配置信息确认</div>
          <ul class="step-list">
            <!-- 培训类配置 -->
            <li v-if="formData.algorithmCategory === '培训类'">
              培训配置：{{ getTrainingConfigSummary() }}
            </li>
            <!-- 工程咨询类配置 -->
            <li v-else-if="formData.algorithmCategory === '工程咨询类'">
              工程咨询配置：{{ getEngineeringConfigSummary() }}
            </li>
            <!-- 京东慧采配置 -->
            <li v-else>
              采购配置：{{ getJdConfigSummary() }}
            </li>
            <li>数据完整性和合规性已验证</li>
          </ul>
        </div>
      </div>

      <div class="step-item">
        <div class="step-number">3</div>
        <div class="step-text">
          <div class="step-title">计算准备</div>
          <ul class="step-list">
            <li>系统已准备就绪</li>
            <li>将根据项目信息和配置进行智能分析计算</li>
            <li>计算完成后将生成详细的限价报告</li>
          </ul>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
export default {
  name: 'TodoListStep',
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {

    const getTrainingConfigSummary = () => {
      if (props.formData.trainingTableRows && props.formData.trainingTableRows.length > 0) {
        const training = props.formData.trainingTableRows[0];
        return `${training.training_type} - ${training.training_days}天 - ${training.trainee_count}人`;
      }
      return '培训参数已配置';
    };

    const getEngineeringConfigSummary = () => {
      if (props.formData.engineeringTableRows && props.formData.engineeringTableRows.length > 0) {
        const engineering = props.formData.engineeringTableRows[0];
        return `${engineering.category[1]} - 专业系数${engineering.professionalAdjustment}`;
      }
      return '工程咨询参数已配置';
    };

    const getJdConfigSummary = () => {
      if (props.formData.jdTableRows && props.formData.jdTableRows.length > 0) {
        const jd = props.formData.jdTableRows[0];
        return `${jd.feeName} - 数量${jd.quantity}${jd.unit || '个'}`;
      }
      return '采购参数已配置';
    };

    const nextStep = () => {
      emit('next-step');
    };

    const prevStep = () => {
      emit('prev-step');
    };

    return {
      getTrainingConfigSummary,
      getEngineeringConfigSummary,
      getJdConfigSummary,
      nextStep,
      prevStep
    };
  }
}
</script>

<style scoped>
/* 简约的待办清单样式 */
.calculation-steps {
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 0;
  background: none;
  border: none;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #73a9ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.step-text {
  flex: 1;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
}

.step-list {
  margin: 0;
  padding-left: 0;
  list-style: none;
  color: #606266;
}

.step-list li {
  position: relative;
  padding-left: 16px;
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.step-list li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: #c0c4cc;
  font-weight: bold;
}

.step-list li:last-child {
  margin-bottom: 0;
}
</style>
