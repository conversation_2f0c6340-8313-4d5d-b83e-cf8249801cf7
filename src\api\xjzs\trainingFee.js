import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '//xjzs/trainingFee/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '//xjzs/trainingFee/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '//xjzs/trainingFee/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '//xjzs/trainingFee/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '//xjzs/trainingFee/submit',
    method: 'post',
    data: row
  })
}

// 导入Excel数据
export const importTrainingFee = (file, isCovered = false) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('isCovered', isCovered ? 1 : 0);
  return request({
    url: '/xjzs/trainingFee/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData,
    timeout: 300000 // 5分钟超时
  })
}

// 下载Excel模板
export const downloadTemplate = () => {
  return request({
    url: '/xjzs/trainingFee/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 计算最高限价
export const calculateMaxPrice = (categoryId, brandId) => {
  return request({
    url: '/xjzs/trainingFee/calculate-max-price',
    method: 'get',
    params: {
      categoryId,
      brandId
    }
  })
}

// 获取京东慧采标准表分类数据
export const getJdCategories = () => {
  return request({
    url: '/xjzs/trainingFee/jd-categories',
    method: 'get'
  })
}

// 根据费用名称获取商品属性
export const getProductAttributes = (feeName, unit) => {
  return request({
    url: '/xjzs/trainingFee/product-attributes',
    method: 'get',
    params: {
      feeName,
      unit
    }
  })
}

// 获取培训类标准表分类数据
export const getTrainingCategories = () => {
  return request({
    url: '/xjzs/trainingFee/training-categories',
    method: 'get'
  })
}



// 根据费用名称获取培训属性
export const getTrainingAttributes = (feeName) => {
  return request({
    url: '/xjzs/trainingFee/training-attributes',
    method: 'get',
    params: {
      feeName
    }
  })
}

// 根据费用名称获取培训属性
export const getTrainingUnit = (feeName) => {
  return request({
    url: '/xjzs/trainingFee/dis-unit',
    method: 'get',
    params: {
      feeName
    }
  })
}

