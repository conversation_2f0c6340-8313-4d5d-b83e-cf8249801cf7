export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  menu: false,
  addBtn:false,
  delBtn: false,
  selection: true,
  dialogClickModal: false,
  align: 'center',
  column: [
    {
      label: "项目编码",
      prop: "projectId",
      type: "input",
      hide: true
    },
    {
      label: "项目名称",
      prop: "projectName",
      type: "input",
      search: true,
      width: 350
    },
    {
      label: "预算金额（万元）",
      prop: "projectBudget",
      type: "input",
      width: 90
    },
    {
      label: "询价部门",
      prop: "deptName",
      type: "input",
      search: true
    },
    {
      label: "经办人",
      prop: "userName",
      type: "input",
      search: true
    },
    {
      label: "询价时间",
      prop: "inquiryTime",
      type: "date",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD",
      width: 100
    },
    {
      label: "名称",
      prop: "serviceName",
      type: "input"
    },
    {
      label: "技术参数或服务内容",
      prop: "serviceContent",
      type: "input",
      width: 200
    },
    {
      label: "数量",
      prop: "quantity",
      type: "input",
      width: 60
    },
    {
      label: "询价对象",
      prop: "supplier",
      type: "input"
    },
    {
      label: "询价渠道",
      prop: "inquiryChannel",
      type: "input"
    },
    {
      label: "单价(万元)",
      prop: "unitPrice",
      type: "input",
      width: 70,
      formatter: ({ unitPrice }) => {
          if (unitPrice == null || unitPrice === '') return '';
          // 转换为万元，保留4位小数（可根据需要调整）
          return (parseFloat(unitPrice) / 10000).toFixed(2);
        }
    },
    {
      label: "总价(万元)",
      prop: "totalPrice",
      type: "input",
      width: 70,
      formatter: ({ unitPrice }) => {
          if (unitPrice == null || unitPrice === '') return '';
          // 转换为万元，保留4位小数（可根据需要调整）
          return (parseFloat(unitPrice) / 10000).toFixed(2);
        }
    },
    {
      label: "备注",
      prop: "remark",
      type: "input"
    }
  ]
}