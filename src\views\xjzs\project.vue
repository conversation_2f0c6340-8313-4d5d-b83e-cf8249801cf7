<template>
  <basic-container>
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.project_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="warning"
                   plain
                   icon="el-icon-upload"
                   @click="handleImport">导 入
        </el-button>
        <el-button type="info"
                   plain
                   icon="el-icon-download"
                   @click="handleDownloadTemplate">下载模板
        </el-button>
        <!-- <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   @click="handleExport">导 出
        </el-button> -->
      </template>
      <template #menu-right>
        <div class="search-box">
          <el-input
            v-model="projectNameFilter"
            placeholder="输入项目名称进行过滤"
            clearable
            @keyup.enter="handleProjectNameSearch"
            style="width: 220px"
          >
            <template #prefix>
              <el-icon><el-icon-search /></el-icon>
            </template>
          </el-input>
          <el-button
            icon="el-icon-refresh"
            circle
            @click="handleProjectNameSearch"
            style="margin-left: 8px"
          ></el-button>
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, importProject} from "@/api/xjzs/project";
  import option from "@/option/xjzs/project";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        projectNameFilter: '',
        searchTimer: null
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.project_add, false),
          viewBtn: this.validData(this.permission.project_view, false),
          delBtn: this.validData(this.permission.project_delete, false),
          editBtn: this.validData(this.permission.project_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleImport() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (e) => {
          const file = e.target.files[0];
          if (!file) return;

          this.$confirm('是否覆盖已有数据?', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          }).then((res) => {
            const isCovered = res === 'confirm';

            const loading = this.$loading({
              lock: true,
              text: '正在导入...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });

            importProject(file, isCovered).then(() => {
              this.$message.success('导入成功');
              this.onLoad(this.page);
            }).catch((error) => {
              console.error('导入失败:', error);
              this.$message.error('导入失败: ' + (error.message || '未知错误'));
            }).finally(() => {
              loading.close();
            });
          }).catch(() => {
            // 用户取消选择，不执行导入
          });
        };
        input.click();
      },
      handleExport() {
        let downloadUrl = `/xjzs/project/export-project?${this.website.tokenHeader}=${getToken()}`;
        const {
        } = this.query;
        let values = {
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `项目信息表${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      handleDownloadTemplate() {
        window.open('/file/项目导入模板.xlsx');
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      // 处理项目名称搜索
      handleProjectNameSearch() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      onLoad(page, params = {}) {
        this.loading = true;

        const {
        } = this.query;

        let values = {
          ...params
        };

        // 添加项目名称过滤条件
        if (this.projectNameFilter) {
          values.name_like = this.projectNameFilter;
        }

        getList(page.currentPage, page.pageSize, values).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  margin-right: 10px;
  height: 32px; /* 与刷新按钮保持一致的高度 */
}
</style>
