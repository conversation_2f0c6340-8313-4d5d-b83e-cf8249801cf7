<template>
  <basic-container>
    <div class="project-detail-container">
      <div class="detail-header">
        <h2>项目详情</h2>
        <el-button @click="goBack" icon="el-icon-back">返回</el-button>
      </div>
      
      <el-card class="detail-card" v-loading="loading">
        <template #header>
          <div class="card-header">
            <span>{{ project.name || '项目信息' }}</span>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="generateReport" v-if="!hasReport">生成报告</el-button>
              <el-button type="success" size="small" @click="viewReport" v-if="hasReport">查看报告</el-button>
            </div>
          </div>
        </template>
        
        <div class="project-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目编号">{{ project.code || `P${project.id}` }}</el-descriptions-item>
            <el-descriptions-item label="项目类型">{{ project.type || '-' }}</el-descriptions-item>
            <el-descriptions-item label="项目分类">{{ project.category || '-' }}</el-descriptions-item>
            <el-descriptions-item label="采购方式">{{ project.procurementMethod || '-' }}</el-descriptions-item>
            <el-descriptions-item label="算法类型">{{ project.algorithmCategory || '-' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ project.createTime || '-' }}</el-descriptions-item>
            <el-descriptions-item label="项目描述" :span="2">{{ project.content || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="project-files" v-if="project.files && project.files.length > 0">
          <h3>项目附件</h3>
          <el-table :data="project.files" style="width: 100%">
            <el-table-column prop="name" label="文件名称"></el-table-column>
            <el-table-column prop="size" label="文件大小"></el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="text" @click="downloadFile(scope.row)">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </basic-container>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getDetail } from '@/api/xjzs/project';
import { getProjectReport } from '@/api/xjzs/report';
import { checkReportExists } from '@/api/xjzs/projectReport';

export default {
  name: 'ProjectDetail',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const projectId = route.params.id;
    
    const loading = ref(true);
    const project = reactive({});
    const hasReport = ref(false);
    
    const fetchProjectDetail = async () => {
      try {
        loading.value = true;
        const res = await getDetail(projectId);
        if (res.data.success) {
          Object.assign(project, res.data.data);
          // 检查是否有报告
          checkReportExists(projectId).then(reportRes => {
            hasReport.value = reportRes.data.data;
          });
        } else {
          ElMessage.error(res.data.msg || '获取项目详情失败');
        }
      } catch (error) {
        console.error('获取项目详情出错:', error);
        ElMessage.error('获取项目详情出错');
      } finally {
        loading.value = false;
      }
    };
    
    const goBack = () => {
      router.back();
    };
    
    const generateReport = () => {
      router.push({ path: '/xjzs/project-assistant', query: { projectId } });
    };
    
    const viewReport = () => {
      // 跳转到报告查看页面
      router.push({ path: '/xjzs/project-assistant', query: { projectId, step: '4' } });
    };
    
    const downloadFile = (file) => {
      // 实现文件下载逻辑
      ElMessage.info('文件下载功能待实现');
    };
    
    onMounted(() => {
      fetchProjectDetail();
    });
    
    return {
      project,
      loading,
      hasReport,
      goBack,
      generateReport,
      viewReport,
      downloadFile
    };
  }
};
</script>

<style lang="scss" scoped>
.project-detail-container {
  padding: 20px;
  
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
    }
  }
  
  .detail-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .project-info {
      margin-bottom: 20px;
    }
    
    .project-files {
      margin-top: 30px;
      
      h3 {
        margin-bottom: 15px;
      }
    }
  }
}
</style>