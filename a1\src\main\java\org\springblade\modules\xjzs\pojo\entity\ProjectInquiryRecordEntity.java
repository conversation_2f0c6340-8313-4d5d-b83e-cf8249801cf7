/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 项目询价记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@TableName("xjzs_project_inquiry_record")
@Schema(description = "ProjectInquiryRecord对象")
@EqualsAndHashCode(callSuper = true)
public class ProjectInquiryRecordEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 项目编码
	 */
	@Schema(description = "项目编码")
	private String projectId;
	/**
	 * 产品名称
	 */
	@Schema(description = "产品名称")
	private String productName;
	/**
	 * 技术规格
	 */
	@Schema(description = "技术规格")
	private String specifications;
	/**
	 * 数量
	 */
	@Schema(description = "数量")
	private Long quantity;
	/**
	 * 质量标准
	 */
	@Schema(description = "质量标准")
	private String standard;
	/**
	 * 包装要求
	 */
	@Schema(description = "包装要求")
	private String packageRequirements;
	/**
	 * 交货期
	 */
	@Schema(description = "交货期")
	private Date deliveryDate;
	/**
	 * 服务内容
	 */
	@Schema(description = "服务内容")
	private String serviceContent;
	/**
	 * 服务期限
	 */
	@Schema(description = "服务期限")
	private String servicePeriod;
	/**
	 * 服务要求
	 */
	@Schema(description = "服务要求")
	private String serviceRequirements;
	/**
	 * 其他说明
	 */
	@Schema(description = "其他说明")
	private String otherInstructions;
	/**
	 * 交货地点
	 */
	@Schema(description = "交货地点")
	private String deliveryAddress;
	/**
	 * 售后服务
	 */
	@Schema(description = "售后服务")
	private String aftersalesContent;
	/**
	 * 需附文件
	 */
	@Schema(description = "需附文件")
	private String attachment;
	/**
	 * 截止时间
	 */
	@Schema(description = "截止时间")
	private Date deadline;
	/**
	 * 联系人姓名
	 */
	@Schema(description = "联系人姓名")
	private String contactName;
	/**
	 * 联系人电话
	 */
	@Schema(description = "联系人电话")
	private String contactPhone;
	/**
	 * 报价总金额
	 */
	@Schema(description = "报价总金额")
	private BigDecimal quotationTotalAmount;
	/**
	 * 报价时间
	 */
	@Schema(description = "报价时间")
	private Date quotationTime;
	/**
	 * 报价公司
	 */
	@Schema(description = "报价公司")
	private String quotationCompany;

}
