<template>
  <div class="engine-calculation-step-tabs">
    <!-- 计算控制区域 -->
    <div v-if="!isStreamCalculating && !calculationResult" class="calculation-control">
      <el-button type="primary" @click="startStreamCalculationNew" :loading="isStreamCalculating" size="large">
        <i class="el-icon-video-play"></i>
        开始计算
      </el-button>
    </div>

    <!-- 流式计算显示区域 - 直接显示AI内容 -->
    <div v-if="isStreamCalculating" class="stream-calculation">
      <XMarkdown :markdown="processMarkdown(streamContent) || '等待计算开始...'" class="stream-text" :options="{
        html: true,
        breaks: true,
        linkify: true
      }" />
    </div>

    <!-- 计算完成结果显示 - 直接显示AI内容 -->
    <div v-if="calculationResult && !isStreamCalculating" class="calculation-result">
      <div v-if="calculationResult.calculationProcess">
        <XMarkdown :markdown="processMarkdown(calculationResult.calculationProcess)" :options="{
          html: true,
          breaks: true,
          linkify: true
        }" />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { calculateAuditWithInputsStream, calculateGoodsAuditWithInputsStream, calculateEngineeringAuditWithInputsStream } from '@/api/xjzs/audit';
import { XMarkdown } from 'vue-element-plus-x';

export default {
  name: 'EngineCalculationStepTabs',
  components: {
    XMarkdown
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  emits: ['next-step', 'calculation-complete'],
  setup(props, { emit }) {
    const calculationResult = ref(null);
    const isStreamCalculating = ref(false);
    const streamContent = ref('');

    // 监听自动启动标志
    watch(() => props.formData.autoStartCalculation, (newVal, oldVal) => {
      console.log('🔍 autoStartCalculation 变化:', { oldVal, newVal });
      if (newVal) {
        console.log('🚀 检测到自动启动标志，开始计算');
        // 重置标志
        props.formData.autoStartCalculation = false;
        // 延迟一点开始计算
        setTimeout(() => {
          console.log('⚡ 开始执行自动计算');
          startStreamCalculationNew();
        }, 500);
      }
    }, { immediate: true });

    // 格式化价格
    const formatPrice = (price) => {
      if (!price) return '0.00';
      return parseFloat(price).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    };

    // 处理Markdown内容
    const processMarkdown = (content) => {
      if (!content) return '';
      
      let processed = content.toString();
      
      // 简单的格式化处理
      processed = processed
        .replace(/\*\*(.*?)\*\*/g, '**$1**')
        .replace(/(\d+\.?\d*)\s*元/g, '**$1元**')
        .replace(/(计算|分析|结果)/g, '**$1**');
      
      return processed;
    };

    // 流式计算方法
    const startStreamCalculationNew = async () => {
      if (isStreamCalculating.value) return;

      try {
        isStreamCalculating.value = true;
        streamContent.value = '';
        calculationResult.value = null;

        // 根据项目类型确定使用的API和参数
        let inputs = {};
        let apiFunction = null;

        if (props.formData.projectType === '培训类' || props.formData.projectType === '服务') {
          // 培训类默认参数
          inputs = {
            training_type: '线上培训',
            training_days: 5,
            trainee_count: 2,
            daily_hours: 2,
            instructor_title: '正高级职称',
            training_category: '省部级及以上（一类）'
          };

          // 如果有培训类表格数据，使用第一行数据
          if (props.formData.trainingTableRows && props.formData.trainingTableRows.length > 0) {
            const firstRow = props.formData.trainingTableRows[0];
            inputs = {
              training_type: firstRow.training_type || inputs.training_type,
              training_days: firstRow.training_days || inputs.training_days,
              trainee_count: firstRow.trainee_count || inputs.trainee_count,
              daily_hours: firstRow.daily_hours || inputs.daily_hours,
              instructor_title: firstRow.instructor_title || inputs.instructor_title,
              training_category: firstRow.training_category || inputs.training_category
            };
          }

          apiFunction = calculateAuditWithInputsStream;
        } else if (props.formData.projectType === '货物类' || props.formData.projectType === '货物') {
          // 货物类默认参数
          inputs = {
            goods_data: '[]'
          };

          // 如果有京东慧采表格数据，直接传递数组对象给dify
          if (props.formData.jdTableRows && props.formData.jdTableRows.length > 0) {
            inputs = {
              goods_data: props.formData.jdTableRows
            };
          }

          apiFunction = calculateGoodsAuditWithInputsStream;
        } else if (props.formData.projectType === '工程咨询类' || props.formData.projectType === '工程') {
          // 工程类默认参数（初始值为0，用户可选择性填写）
          inputs = {
            ConsultingType: '',
            BuildInstallCost: 0,
            TotalInvestment: 0,
            LandGovFees: 0,
            AppraisedValue: 0,
            Bill_Architectural: 0,
            Bill_Equipment: 0,
            Bill_TrialRun: 0,
            ProfFactor: 1.0,
            ComplexityFactor: 0.85,
            ElevationFactor: 1,
            AdditionalFactor: 0,
            OtherDesignFee: 0
          };

          // 如果有工程类表格数据，使用第一行数据
          if (props.formData.engineeringTableRows && props.formData.engineeringTableRows.length > 0) {
            const firstRow = props.formData.engineeringTableRows[0];
            
            // 处理category字段
            let consultingType = '';
            if (firstRow.category) {
              if (Array.isArray(firstRow.category)) {
                consultingType = firstRow.category[firstRow.category.length - 1] || '竣工决算审计';
              } else {
                consultingType = firstRow.category;
              }
            } else {
              consultingType = '竣工决算审计';
            }

            inputs = {
              ConsultingType: consultingType,
              BuildInstallCost: parseFloat(firstRow.constructionCost) || inputs.BuildInstallCost,
              TotalInvestment: parseFloat(firstRow.constructionFee) || inputs.TotalInvestment,
              LandGovFees: parseFloat(firstRow.equipmentFee) || inputs.LandGovFees,
              AppraisedValue: parseFloat(firstRow.testRunFee) || inputs.AppraisedValue,
              Bill_Architectural: 0,
              Bill_Equipment: 0,
              Bill_TrialRun: 0,
              ProfFactor: parseFloat(firstRow.professionalAdjustment) || inputs.ProfFactor,
              ComplexityFactor: parseFloat(firstRow.complexityAdjustment) || inputs.ComplexityFactor,
              ElevationFactor: parseFloat(firstRow.altitudeAdjustment) || inputs.ElevationFactor,
              AdditionalFactor: parseFloat(firstRow.additionalAdjustment) || inputs.AdditionalFactor,
              OtherDesignFee: parseFloat(firstRow.otherDesignFees) || inputs.OtherDesignFee
            };
          }

          apiFunction = calculateEngineeringAuditWithInputsStream;
        } else {
          throw new Error(`不支持的项目类型: ${props.formData.projectType}`);
        }

        console.log('开始流式计算，参数:', inputs);

        // 设置超时定时器
        const timeoutId = setTimeout(() => {
          if (isStreamCalculating.value) {
            console.warn('流式计算超时，自动停止');
            isStreamCalculating.value = false;
            ElMessage.warning('AI计算超时，请重试');
          }
        }, 60000); // 60秒超时

        // 调用对应的流式API
        const response = await apiFunction(inputs);

        // 清除超时定时器
        clearTimeout(timeoutId);

        // 检查响应状态
        if (!response.ok) {
          const errorText = await response.text();
          console.error('流式API响应错误:', response.status, errorText);
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        console.log('流式API响应成功，开始处理数据流');

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let lastDataTime = Date.now();

        // 添加数据接收监控
        const dataTimeoutId = setInterval(() => {
          if (Date.now() - lastDataTime > 30000) {
            console.warn('30秒内没有接收到数据，可能连接中断');
            clearInterval(dataTimeoutId);
            if (isStreamCalculating.value) {
              isStreamCalculating.value = false;
              ElMessage.warning('数据传输中断，请重试');
            }
          }
        }, 5000);

        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
              console.log('流式数据读取完成');
              break;
            }

            lastDataTime = Date.now();
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // 按行处理数据
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.trim() === '') continue;

              await processStreamLine(line);
            }
          }

          // 处理单行数据的函数
          async function processStreamLine(line) {
            let jsonStr = '';

            // 处理不同的数据格式
            if (line.startsWith('data: ')) {
              jsonStr = line.substring(6).trim();
            } else if (line.startsWith('data:')) {
              // 处理没有空格的 data: 格式
              jsonStr = line.substring(5).trim();
            } else if (line.trim().startsWith('{') && line.trim().endsWith('}')) {
              // 直接是JSON格式
              jsonStr = line.trim();
            } else if (line.trim() === '' || line.trim() === 'data:' || line.trim() === 'data: ') {
              // 跳过空行或只有data:的行
              return;
            } else {
              // 其他格式，跳过
              return;
            }

            // 跳过空的JSON字符串或结束标志
            if (!jsonStr || jsonStr === '' || jsonStr === '[DONE]') {
              if (jsonStr === '[DONE]') {
                console.log('收到结束标志');
              }
              return;
            }

            try {
              const data = JSON.parse(jsonStr);
              console.log('✅ 成功解析JSON数据:', data.event, data);

              // 检查是否是错误事件
              if (data.event === 'error') {
                console.error('服务端返回错误:', data.error || data.description || '未知错误');
                ElMessage.error(data.error || data.description || '服务端处理失败');
                isStreamCalculating.value = false;
                return; // 退出流式处理
              }

              // 根据事件类型处理
              switch (data.event) {
                case 'workflow_started':
                  console.log('🚀 工作流开始:', data.data?.id);
                  streamContent.value += '🚀 开始计算...\n\n';
                  break;

                case 'node_started':
                  console.log('📝 节点开始:', data.data?.title, data.data?.node_type);
                  if (data.data?.node_type === 'llm') {
                    streamContent.value += `📝 ${data.data?.title || 'AI分析'}开始...\n`;
                  }
                  break;

                case 'text_chunk':
                  // 文本块事件，包含实时生成的文本片段
                  if (data.answer && data.answer.trim()) {
                    streamContent.value += data.answer;
                  } else if (data.data && data.data.text && data.data.text.trim()) {
                    streamContent.value += data.data.text;
                  }
                  break;

                case 'workflow_finished':
                  console.log('🏁 工作流完成');

                  // 尝试多种方式获取最终结果
                  let finalAnswer = '';

                  // 方式1：通过后端的getAnswer方法（data.answer字段）
                  if (data.answer && data.answer.trim()) {
                    finalAnswer = data.answer.trim();
                    console.log('🏁 通过data.answer获取到内容，长度:', finalAnswer.length);
                  }

                  // 方式2：直接从data.data.outputs中获取
                  if (!finalAnswer && data.data && data.data.outputs) {
                    console.log('🏁 尝试从data.data.outputs获取内容:', data.data.outputs);

                    // 首先检查是否有text字段
                    if (data.data.outputs.text && data.data.outputs.text.trim()) {
                      finalAnswer = data.data.outputs.text.trim();
                      console.log('🏁 从outputs.text获取到内容，长度:', finalAnswer.length);
                    }
                    // 然后检查output数组
                    else if (data.data.outputs.output && Array.isArray(data.data.outputs.output)) {
                      const outputArray = data.data.outputs.output;
                      const results = [];

                      // 检查内容特征来判断计算类型
                      let isGoodsCalculation = false;
                      for (let i = 0; i < outputArray.length; i++) {
                        if (outputArray[i] && outputArray[i].includes('<think>')) {
                          isGoodsCalculation = true;
                          break;
                        }
                      }

                      for (let i = 0; i < outputArray.length; i++) {
                        if (outputArray[i] && outputArray[i].trim()) {
                          results.push(outputArray[i].trim());
                        }
                      }

                      if (results.length > 0) {
                        if (isGoodsCalculation && results.length > 1) {
                          // 货物类计算：多个结果用分隔符连接
                          finalAnswer = results.join('\n\n' + '='.repeat(50) + '\n\n');
                        } else {
                          // 服务类计算：直接连接，不加分隔符
                          finalAnswer = results.join('');
                        }
                        console.log('🏁 从output数组获取到内容，长度:', finalAnswer.length);
                      }
                    }
                    // 最后检查是否是字符串格式
                    else if (typeof data.data.outputs === 'string') {
                      finalAnswer = data.data.outputs.trim();
                      console.log('🏁 从outputs字符串获取到内容，长度:', finalAnswer.length);
                    }
                  }

                  // 如果获取到了最终结果，保存它
                  if (finalAnswer) {
                    console.log('🎯 成功获取到最终结果，长度:', finalAnswer.length);
                    console.log('🎯 最终结果预览:', finalAnswer.substring(0, 200) + '...');

                    // 保存计算结果
                    calculationResult.value = {
                      totalCost: extractTotalCost(finalAnswer),
                      calculationProcess: finalAnswer
                    };

                    console.log('💾 保存计算结果:', calculationResult.value);

                    // 更新formData
                    props.formData.calculationResult = calculationResult.value;
                    console.log('💾 更新formData.calculationResult:', props.formData.calculationResult);

                    // 发送计算完成事件
                    emit('calculation-complete', calculationResult.value);

                    // 计算完成后自动跳转到下一步
                    setTimeout(() => {
                      emit('next-step');
                    }, 1500);
                  } else {
                    console.warn('⚠️ 没有获取到最终结果');
                    console.log('🔍 data.answer:', data.answer);
                    console.log('🔍 data.data:', data.data);
                  }
                  break;
              }
            } catch (parseError) {
              console.warn('解析数据失败:', parseError, '原始数据:', line);
            }
          }
        } finally {
          clearInterval(dataTimeoutId);
        }

      } catch (error) {
        console.error('流式计算失败:', error);
        ElMessage.error('计算失败: ' + error.message);
      } finally {
        console.log('🔄 重置计算状态，isStreamCalculating:', isStreamCalculating.value, '-> false');
        console.log('📊 当前计算结果:', calculationResult.value);
        console.log('📊 formData.calculationResult:', props.formData.calculationResult);
        console.log('🎭 模板显示条件检查:');
        console.log('  - 开始按钮显示条件 (!isStreamCalculating && !calculationResult):', !isStreamCalculating.value && !calculationResult.value);
        console.log('  - 计算过程显示条件 (isStreamCalculating):', isStreamCalculating.value);
        console.log('  - 结果显示条件 (calculationResult && !isStreamCalculating):', !!calculationResult.value && !isStreamCalculating.value);
        isStreamCalculating.value = false;
        console.log('🔄 重置后的显示条件:');
        console.log('  - 开始按钮显示条件 (!isStreamCalculating && !calculationResult):', !isStreamCalculating.value && !calculationResult.value);
        console.log('  - 结果显示条件 (calculationResult && !isStreamCalculating):', !!calculationResult.value && !isStreamCalculating.value);
      }
    };

    // 提取总费用
    const extractTotalCost = (text) => {
      if (!text) return 0;
      
      const patterns = [
        /最高限价[：:]\s*(\d+(?:\.\d+)?)\s*元/,
        /总费用[：:]\s*(\d+(?:\.\d+)?)\s*元/,
        /合计[：:]\s*(\d+(?:\.\d+)?)\s*元/,
        /(\d+(?:\.\d+)?)\s*元/
      ];
      
      for (const pattern of patterns) {
        const match = text.match(pattern);
        if (match) {
          return parseFloat(match[1]);
        }
      }
      
      return 0;
    };

    // 组件挂载时检查是否需要自动开始计算
    onMounted(() => {
      console.log('🔧 EngineCalculationStepTabs 组件已挂载');
      console.log('📊 当前 autoStartCalculation 状态:', props.formData.autoStartCalculation);

      if (props.formData.autoStartCalculation) {
        console.log('🚀 组件挂载时检测到自动启动标志');
        props.formData.autoStartCalculation = false;
        setTimeout(() => {
          console.log('⚡ 组件挂载后开始执行自动计算');
          startStreamCalculationNew();
        }, 1000);
      }
    });

    return {
      calculationResult,
      isStreamCalculating,
      streamContent,
      startStreamCalculationNew,
      formatPrice,
      processMarkdown
    };
  }
}
</script>

<style scoped>
.engine-calculation-step-tabs {
  padding: 0;
}

.calculation-control {
  text-align: center;
  padding: 20px 0;
}

.stream-calculation {
  padding: 20px 0;
}

.stream-content {
  padding: 10px 0;
  max-height: 400px;
  overflow-y: auto;
}

.calculation-result {
  padding: 20px 0;
}





.stream-text {
  line-height: 1.6;
}
</style>
