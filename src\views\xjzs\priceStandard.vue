<template>
  <basic-container>
    <div class="price-standard-container">
      <el-tabs v-model="activeTab" class="standard-tabs">
        <el-tab-pane label="培训类" name="培训类">
          <!-- 嵌入培训费用组件，传入类型属性 -->
          <training-fee v-if="activeTab === '培训类'" :fee-type="activeTab" class="fee-component"></training-fee>
        </el-tab-pane>
        <el-tab-pane label="工程咨询类" name="工程咨询类">
          <!-- 工程咨询类也使用training-fee组件，但传入不同的类型属性 -->
          <training-fee v-if="activeTab === '工程咨询类'" :fee-type="activeTab" class="fee-component"></training-fee>
        </el-tab-pane>
        <el-tab-pane label="京东慧采" name="京东慧采">
             <training-fee v-if="activeTab === '京东慧采'" :fee-type="activeTab" class="fee-component"></training-fee>
        </el-tab-pane>
      </el-tabs>
    </div>
  </basic-container>
</template>

<script>
import TrainingFee from './trainingFee.vue';

export default {
  name: 'PriceStandard',
  components: {
    TrainingFee
  },
  data() {
    return {
      activeTab: '培训类'
    };
  }
};
</script>

<style scoped>
.price-standard-container {
  padding: 10px;
  height: calc(100vh - 200px);
  overflow: hidden;
}

.standard-tabs {
  margin-top: 15px;
  height: 100%;
}

.empty-tab-content {
  padding: 40px 0;
}

.fee-component {
  height: calc(100% - 50px);
  overflow: auto;
}
</style>

