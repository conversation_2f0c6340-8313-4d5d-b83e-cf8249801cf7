/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 文件库 实体类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@TableName("xjzs_file_library")
@Schema(description = "FileLibrary对象")
@EqualsAndHashCode(callSuper = true)
public class FileLibraryEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 文件名称
	 */
	@Schema(description = "文件名称")
	private String fileName;

	/**
	 * 文件类型：国家标准、行业标准、地方标准
	 */
	@Schema(description = "文件类型：国家标准、行业标准、地方标准")
	private String fileType;

	/**
	 * 文件路径(MinIO存储路径)
	 */
	@Schema(description = "文件路径")
	private String filePath;

	/**
	 * 文件URL(MinIO访问URL)
	 */
	@Schema(description = "文件URL")
	private String fileUrl;

	/**
	 * 文件大小(字节)
	 */
	@Schema(description = "文件大小")
	private Long fileSize;

	/**
	 * 原始文件名
	 */
	@Schema(description = "原始文件名")
	private String originalFileName;

	/**
	 * 发布单位
	 */
	@Schema(description = "发布单位")
	private String publishUnit;

	/**
	 * 发布时间
	 */
	@Schema(description = "发布时间")
	private LocalDate publishTime;

	/**
	 * 状态：生效中、已失效
	 */
	@Schema(description = "状态：生效中、已失效")
	private String fileStatus;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

}
