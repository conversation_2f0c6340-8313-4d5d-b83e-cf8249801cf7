/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.resource.builder.OssBuilder;
import org.springblade.modules.xjzs.pojo.entity.FileLibraryEntity;
import org.springblade.modules.xjzs.pojo.vo.FileLibraryVO;
import org.springblade.modules.xjzs.service.IFileLibraryService;
import org.springblade.modules.xjzs.wrapper.FileLibraryWrapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

/**
 * 文件库 控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/file-library")
@Tag(name = "文件库", description = "文件库接口")
public class FileLibraryController extends BladeController {

	private final IFileLibraryService fileLibraryService;
	private final OssBuilder ossBuilder;

	/**
	 * 文件库 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入fileLibrary")
	public R<FileLibraryVO> detail(FileLibraryEntity fileLibrary) {
		FileLibraryEntity detail = fileLibraryService.getOne(Condition.getQueryWrapper(fileLibrary));
		return R.data(FileLibraryWrapper.build().entityVO(detail));
	}

	/**
	 * 文件库 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入fileLibrary")
	public R<IPage<FileLibraryVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> fileLibrary, Query query) {
		IPage<FileLibraryEntity> pages = fileLibraryService.page(Condition.getPage(query), Condition.getQueryWrapper(fileLibrary, FileLibraryEntity.class));
		return R.data(FileLibraryWrapper.build().pageVO(pages));
	}

	/**
	 * 文件库 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入fileLibrary")
	public R<IPage<FileLibraryVO>> page(FileLibraryVO fileLibrary, Query query) {
		IPage<FileLibraryVO> pages = fileLibraryService.selectFileLibraryPage(Condition.getPage(query), fileLibrary);
		return R.data(pages);
	}

	/**
	 * 文件库 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入fileLibrary")
	public R save(@RequestBody FileLibraryEntity fileLibrary) {
		return R.status(fileLibraryService.save(fileLibrary));
	}

	/**
	 * 文件库 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入fileLibrary")
	public R update(@RequestBody FileLibraryEntity fileLibrary) {
		return R.status(fileLibraryService.updateById(fileLibrary));
	}

	/**
	 * 文件库 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入fileLibrary")
	public R submit(@RequestBody FileLibraryEntity fileLibrary) {
		return R.status(fileLibraryService.saveOrUpdate(fileLibrary));
	}

	/**
	 * 文件库 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(fileLibraryService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 文件上传
	 */
	@PostMapping("/upload")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "文件上传", description = "上传文件到MinIO")
	public R<BladeFile> uploadFile(@RequestParam("file") MultipartFile file) {
		try {
			// 上传文件到MinIO
			BladeFile bladeFile = ossBuilder.template().putFile(file.getOriginalFilename(), file.getInputStream());
			return R.data(bladeFile);
		} catch (Exception e) {
			return R.fail("文件上传失败: " + e.getMessage());
		}
	}

	/**
	 * 文件上传并保存文件库记录
	 */
	@PostMapping("/upload-and-save")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "文件上传并保存", description = "上传文件到MinIO并保存文件库记录")
	public R<FileLibraryVO> uploadAndSave(
			@RequestParam("file") MultipartFile file,
			@RequestParam("fileName") String fileName,
			@RequestParam("fileType") String fileType,
			@RequestParam(value = "publishUnit", required = false) String publishUnit,
			@RequestParam(value = "publishTime", required = false) String publishTime,
			@RequestParam(value = "status", defaultValue = "生效中") String fileStatus,
			@RequestParam(value = "remark", required = false) String remark) {
		try {
			// 上传文件到MinIO
			BladeFile bladeFile = ossBuilder.template().putFile(file.getOriginalFilename(), file.getInputStream());

			// 创建文件库记录
			FileLibraryEntity fileLibrary = new FileLibraryEntity();
			fileLibrary.setFileName(fileName);
			fileLibrary.setFileType(fileType);
			fileLibrary.setPublishUnit(publishUnit);
			if (publishTime != null && !publishTime.isEmpty()) {
				fileLibrary.setPublishTime(java.time.LocalDate.parse(publishTime));
			}
			fileLibrary.setFileStatus(fileStatus);
			fileLibrary.setRemark(remark);

			// 设置文件相关信息
			fileLibrary.setFilePath(bladeFile.getName());
			fileLibrary.setFileUrl(bladeFile.getLink());
			fileLibrary.setFileSize(file.getSize());
			fileLibrary.setOriginalFileName(file.getOriginalFilename());

			// 保存到数据库
			fileLibraryService.save(fileLibrary);

			return R.data(FileLibraryWrapper.build().entityVO(fileLibrary));
		} catch (Exception e) {
			return R.fail("文件上传并保存失败: " + e.getMessage());
		}
	}

	/**
	 * 文件下载
	 */
	@GetMapping("/download/{id}")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "文件下载", description = "根据文件库ID下载文件")
	public void downloadFile(@PathVariable Long id, HttpServletResponse response) throws IOException {
		FileLibraryEntity fileLibrary = fileLibraryService.getById(id);
		if (fileLibrary == null) {
			response.setStatus(HttpServletResponse.SC_NOT_FOUND);
			return;
		}

		// 获取文件URL并重定向
		String fileUrl = ossBuilder.template().fileLink(fileLibrary.getFilePath());
		response.sendRedirect(fileUrl);
	}

	/**
	 * 获取文件预览URL
	 */
	@GetMapping("/preview-url/{id}")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "获取文件预览URL", description = "根据文件库ID获取文件预览URL")
	public R<String> getPreviewUrl(@PathVariable Long id) {
		try {
			FileLibraryEntity fileLibrary = fileLibraryService.getById(id);
			if (fileLibrary == null) {
				return R.fail("文件不存在");
			}

			// 生成预览URL（文件外链）
			String previewUrl = ossBuilder.template().fileLink(fileLibrary.getFilePath());
			return R.data(previewUrl);

		} catch (Exception e) {
			return R.fail("获取预览URL失败: " + e.getMessage());
		}
	}

//	/**
//	 * 文件库 导出
//	 */
//	@GetMapping("/export-file-library")
//	@ApiOperationSupport(order = 12)
//	@Operation(summary = "导出", description = "传入fileLibrary")
//	public void exportFileLibrary(@Parameter(hidden = true) @RequestParam Map<String, Object> fileLibrary, BladeUser bladeUser, HttpServletResponse response) {
//		QueryWrapper<FileLibraryEntity> queryWrapper = Condition.getQueryWrapper(fileLibrary, FileLibraryEntity.class);
//		List<FileLibraryExcel> list = fileLibraryService.exportFileLibrary(queryWrapper);
//		ExcelUtil.export(response, "文件库数据", "文件库数据表", list, FileLibraryExcel.class, bladeUser);
//	}

}
