/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.xjzs.pojo.dto.ImportResult;
import org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity;
import org.springblade.modules.xjzs.pojo.vo.TrainingFeeVO;
import org.springblade.modules.xjzs.excel.TrainingFeeExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;
import java.util.Map;

/**
 * 计价标准 服务类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface ITrainingFeeService extends BaseService<TrainingFeeEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param trainingFee 查询参数
	 * @return IPage<TrainingFeeVO>
	 */
	IPage<TrainingFeeVO> selectTrainingFeePage(IPage<TrainingFeeVO> page, TrainingFeeVO trainingFee);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<TrainingFeeExcel>
	 */
	List<TrainingFeeExcel> exportTrainingFee(Wrapper<TrainingFeeEntity> queryWrapper);

	/**
	 * 导入计价标准数据
	 * 导入成功后会异步处理分类和属性
	 *
	 * @param data Excel数据列表
	 * @param isCovered 是否覆盖现有数据
	 * @return 导入结果
	 */
	ImportResult importTrainingFee(List<TrainingFeeExcel> data, Boolean isCovered);

	/**
	 * 计算京东慧采商品的最高限价
	 * 使用箱线图分析，基于四分位距（IQR）找异常点并剔除
	 * 剔除异常值后，取最高十个价格的均价作为最高限价
	 *
	 * @param categoryId 商品分类ID
	 * @param brandId 品牌ID（可选）
	 * @return 计算得出的最高限价
	 */
	Double calculateMaximumPrice(String categoryId, String brandId);

	/**
	 * 根据京东标准表参数，查询对应商品
	 * @param feeName
	 * @param unit
	 * @param keyWords
	 * @return
	 */
	List<TrainingFeeEntity> searchByKeywords(@Param("feeName") String feeName, @Param("unit") String unit, @Param("keyWords") List<String> keyWords);
	/**
	 * 获取京东慧采标准表分类数据
	 * 
	 * @return 包含费用名称、一级分类、二级分类的数据
	 */
	Map<String, List<Map<String, String>>> getJdCategories();

	/**
	 * 根据费用名称获取商品属性
	 * 
	 * @param feeName 费用名称
	 * @return 商品属性列表
	 */
	void getProductAttributes(String feeName);

	/**
	 * 获取培训类标准表分类数据
	 * 
	 * @return 分类数据
	 */
	Map<String, List<Map<String, String>>> getTrainingCategories();

	/**
	 * 获取培训类属性数据
	 * 
	 * @param feeName 费用名称
	 * @return 属性数据
	 */
	Map<String, Object> getTrainingAttributes(String feeName);

	/**
	 * 根据费用名称获取不同的单位
	 * 
	 * @param feeName 费用名称
	 * @return 单位列表
	 */
	List<TrainingFeeVO> getDisUnit(String feeName);

}




