export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "id;主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "项目编码",
      prop: "projectId",
      type: "input",
    },
    {
      label: "询价时间",
      prop: "inquiryTime",
      type: "input",
    },
    {
      label: "名称",
      prop: "name",
      type: "input",
    },
    {
      label: "内容",
      prop: "content",
      type: "input",
    },
    {
      label: "数量",
      prop: "quantity",
      type: "input",
    },
    {
      label: "询价供应商",
      prop: "supplier",
      type: "input",
    },
    {
      label: "询价渠道",
      prop: "inquiryChannel",
      type: "input",
    },
    {
      label: "单价",
      prop: "unitPrice",
      type: "input",
    },
    {
      label: "总价",
      prop: "totalPrice",
      type: "input",
    },
    {
      label: "备注",
      prop: "remark",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
