export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  align: 'center',
  column: [
    {
      label: "id;主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "文件名称",
      prop: "fileName",
      type: "input",
      search: true,
      width: 300,
      rules: [{
        required: true,
        message: "请输入文件名称",
        trigger: "blur"
      }]
    },
    {
      label: "文件上传",
      prop: "fileUpload",
      type: "upload",
      span: 24,
      drag: true,
      loadText: '文件上传中，请稍等',
      tip: '支持上传PDF、DOC、DOCX、XLS、XLSX等格式文件，大小不超过50MB',
      accept: '.pdf,.doc,.docx,.xls,.xlsx,.txt,.zip,.rar',
      limit: 1,
      addDisplay: true,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新文件",
      prop: "fileUpdate",
      type: "upload",
      span: 24,
      drag: true,
      loadText: '文件上传中，请稍等',
      tip: '重新上传文件将替换原有文件，支持PDF、DOC、DOCX、XLS、XLSX等格式，大小不超过50MB',
      accept: '.pdf,.doc,.docx,.xls,.xlsx,.txt,.zip,.rar',
      limit: 1,
      addDisplay: false,
      editDisplay: true,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "文件信息",
      prop: "fileInfo",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
      hide: true,
      formatter: (row) => {
        if (row.originalFileName) {
          return `${row.originalFileName} (${(row.fileSize / 1024 / 1024).toFixed(2)}MB)`;
        }
        return '无文件';
      }
    },
    {
      label: "文件类型",
      prop: "fileType",
      type: "select",
      search: true,
      dicData: [
        {
          label: "全部",
          value: ""
        },
        {
          label: "国家标准",
          value: "国家标准"
        },
        {
          label: "行业标准",
          value: "行业标准"
        },
        {
          label: "地方标准",
          value: "地方标准"
        }
      ],
      rules: [{
        required: true,
        message: "请选择文件类型",
        trigger: "blur"
      }]
    },
    {
      label: "发布单位",
      prop: "publishUnit",
      type: "input",
    },
    {
      label: "发布时间",
      prop: "publishTime",
      type: "date",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD"
    },
    {
      label: "状态",
      prop: "fileStatus",
      type: "select",
      search: true,
      dicData: [
        {
          label: "生效中",
          value: "生效中"
        },
        {
          label: "已失效",
          value: "已失效"
        }
      ],
      rules: [{
        required: true,
        message: "请选择状态",
        trigger: "blur"
      }]
    },
    {
      label: "备注",
      prop: "remark",
      type: "textarea",
      span: 24,
      minRows: 3,
      maxRows: 6
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      hide: true,
    },
    {
      label: "修改人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "修改时间",
      prop: "updateTime",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      hide: true,
    }
  ]
}
