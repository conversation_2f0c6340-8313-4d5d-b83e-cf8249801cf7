/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.xjzs.pojo.entity.ProjectEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectVO;
import java.util.Objects;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目信息表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public class ProjectWrapper extends BaseEntityWrapper<ProjectEntity, ProjectVO>  {

	public static ProjectWrapper build() {
		return new ProjectWrapper();
 	}

	@Override
	public ProjectVO entityVO(ProjectEntity project) {
		ProjectVO projectVO = Objects.requireNonNull(BeanUtil.copyProperties(project, ProjectVO.class));

		//User createUser = UserCache.getUser(project.getCreateUser());
		//User updateUser = UserCache.getUser(project.getUpdateUser());
		//projectVO.setCreateUserName(createUser.getName());
		//projectVO.setUpdateUserName(updateUser.getName());

		return projectVO;
	}

	/**
	 * 实体列表转VO列表
	 */
	public List<ProjectVO> listVO(List<ProjectEntity> list) {
		return list.stream().map(this::entityVO).collect(Collectors.toList());
	}

}
