/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.xjzs.pojo.entity.ProjectInquiryRecordEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectInquiryRecordVO;
import org.springblade.modules.xjzs.excel.ProjectInquiryRecordExcel;
import org.springblade.modules.xjzs.wrapper.ProjectInquiryRecordWrapper;
import org.springblade.modules.xjzs.service.IProjectInquiryRecordService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 项目询价记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/projectInquiryRecord")
@Tag(name = "项目询价记录表", description = "项目询价记录表接口")
public class ProjectInquiryRecordController extends BladeController {

	private final IProjectInquiryRecordService projectInquiryRecordService;

	/**
	 * 项目询价记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入projectInquiryRecord")
	public R<ProjectInquiryRecordVO> detail(ProjectInquiryRecordEntity projectInquiryRecord) {
		ProjectInquiryRecordEntity detail = projectInquiryRecordService.getOne(Condition.getQueryWrapper(projectInquiryRecord));
		return R.data(ProjectInquiryRecordWrapper.build().entityVO(detail));
	}
	/**
	 * 项目询价记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入projectInquiryRecord")
	public R<IPage<ProjectInquiryRecordVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> projectInquiryRecord, Query query) {
		IPage<ProjectInquiryRecordEntity> pages = projectInquiryRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(projectInquiryRecord, ProjectInquiryRecordEntity.class));
		return R.data(ProjectInquiryRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 项目询价记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入projectInquiryRecord")
	public R<IPage<ProjectInquiryRecordVO>> page(ProjectInquiryRecordVO projectInquiryRecord, Query query) {
		IPage<ProjectInquiryRecordVO> pages = projectInquiryRecordService.selectProjectInquiryRecordPage(Condition.getPage(query), projectInquiryRecord);
		return R.data(pages);
	}

	/**
	 * 项目询价记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入projectInquiryRecord")
	public R save(@Valid @RequestBody ProjectInquiryRecordEntity projectInquiryRecord) {
		return R.status(projectInquiryRecordService.save(projectInquiryRecord));
	}

	/**
	 * 项目询价记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入projectInquiryRecord")
	public R update(@Valid @RequestBody ProjectInquiryRecordEntity projectInquiryRecord) {
		return R.status(projectInquiryRecordService.updateById(projectInquiryRecord));
	}

	/**
	 * 项目询价记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入projectInquiryRecord")
	public R submit(@Valid @RequestBody ProjectInquiryRecordEntity projectInquiryRecord) {
		return R.status(projectInquiryRecordService.saveOrUpdate(projectInquiryRecord));
	}

	/**
	 * 项目询价记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(projectInquiryRecordService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-projectInquiryRecord")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入projectInquiryRecord")
	public void exportProjectInquiryRecord(@Parameter(hidden = true) @RequestParam Map<String, Object> projectInquiryRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ProjectInquiryRecordEntity> queryWrapper = Condition.getQueryWrapper(projectInquiryRecord, ProjectInquiryRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ProjectInquiryRecord::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ProjectInquiryRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ProjectInquiryRecordExcel> list = projectInquiryRecordService.exportProjectInquiryRecord(queryWrapper);
		ExcelUtil.export(response, "项目询价记录表数据" + DateUtil.time(), "项目询价记录表数据表", list, ProjectInquiryRecordExcel.class);
	}

}
