<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="de3eef48-8e4d-4694-bd94-a4d840c400a4" name="变更" comment="1.合规性检查回显">
      <change beforePath="$PROJECT_DIR$/src/views/xjzs/components/GenerateReportStep.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/xjzs/components/GenerateReportStep.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/xjzs/components/ProjectSelectStep.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/xjzs/components/ProjectSelectStep.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/xjzs/manager_home.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/xjzs/manager_home.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/xjzs/projectAssistantWaterfall.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/xjzs/projectAssistantWaterfall.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/xjzs/user_home.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/xjzs/user_home.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="GotoClassSymbolConfiguration">
    <file-type-list>
      <filtered-out-file-type name="$XSLT" />
      <filtered-out-file-type name="Darcs" />
      <filtered-out-file-type name="Bazaar" />
      <filtered-out-file-type name="CloudFoundry" />
      <filtered-out-file-type name="Chef" />
      <filtered-out-file-type name="Cvs" />
      <filtered-out-file-type name="DeployHQ" />
      <filtered-out-file-type name="Docker" />
      <filtered-out-file-type name="ElasticBeanstalk" />
      <filtered-out-file-type name="Eleventy" />
      <filtered-out-file-type name="ESLint" />
      <filtered-out-file-type name="Floobits" />
      <filtered-out-file-type name="Google Cloud" />
      <filtered-out-file-type name="Git" />
      <filtered-out-file-type name="GitIgnore" />
      <filtered-out-file-type name="Kubernetes Helm" />
      <filtered-out-file-type name="HgIgnore" />
      <filtered-out-file-type name="Mercurial" />
      <filtered-out-file-type name="Ignore" />
      <filtered-out-file-type name="IgnoreLang" />
      <filtered-out-file-type name="Jetpack" />
      <filtered-out-file-type name="JSHint" />
      <filtered-out-file-type name="Monotone" />
      <filtered-out-file-type name="Nodemon" />
      <filtered-out-file-type name="Npm" />
      <filtered-out-file-type name="NuxtJS" />
      <filtered-out-file-type name="Perforce" />
      <filtered-out-file-type name="Prettier" />
      <filtered-out-file-type name="StyleLint" />
      <filtered-out-file-type name="Stylint" />
      <filtered-out-file-type name="Swagger Codegen" />
      <filtered-out-file-type name="Team Foundation" />
      <filtered-out-file-type name="Up" />
      <filtered-out-file-type name="Vercel" />
      <filtered-out-file-type name="Yarn" />
      <filtered-out-file-type name="AIDL" />
      <filtered-out-file-type name="AndroidDataBinding" />
      <filtered-out-file-type name="CommandLine" />
      <filtered-out-file-type name="Dockerfile" />
      <filtered-out-file-type name="DTD" />
      <filtered-out-file-type name="EditorConfig" />
      <filtered-out-file-type name="GitExclude" />
      <filtered-out-file-type name="Groovy" />
      <filtered-out-file-type name="HTML" />
      <filtered-out-file-type name="HtmlCompatible" />
      <filtered-out-file-type name="Fossil" />
      <filtered-out-file-type name="Sourcegraph" />
      <filtered-out-file-type name="JAVA" />
      <filtered-out-file-type name="JQL" />
      <filtered-out-file-type name="JShellLanguage" />
      <filtered-out-file-type name="JSON" />
      <filtered-out-file-type name="JSON Lines" />
      <filtered-out-file-type name="JSON5" />
      <filtered-out-file-type name="JSONPath" />
      <filtered-out-file-type name="JSRegexp" />
      <filtered-out-file-type name="JSUnicodeRegexp" />
      <filtered-out-file-type name="JVM" />
      <filtered-out-file-type name="kotlin" />
      <filtered-out-file-type name="KND" />
      <filtered-out-file-type name="Lombok.Config" />
      <filtered-out-file-type name="Manifest" />
      <filtered-out-file-type name="Markdown" />
      <filtered-out-file-type name="Mermaid" />
      <filtered-out-file-type name="MultiDexKeep" />
      <filtered-out-file-type name="TEXT" />
      <filtered-out-file-type name="Properties" />
      <filtered-out-file-type name="protobase" />
      <filtered-out-file-type name="protobuf" />
      <filtered-out-file-type name="prototext" />
      <filtered-out-file-type name="Python" />
      <filtered-out-file-type name="PythonRegExp" />
      <filtered-out-file-type name="PythonVerboseRegExp" />
      <filtered-out-file-type name="RegExp" />
      <filtered-out-file-type name="RELAX-NG" />
      <filtered-out-file-type name="Renderscript" />
      <filtered-out-file-type name="requirements.txt" />
      <filtered-out-file-type name="RoomSql" />
      <filtered-out-file-type name="Shell Script" />
      <filtered-out-file-type name="SHRINKER_CONFIG" />
      <filtered-out-file-type name="Smali" />
      <filtered-out-file-type name="SPI" />
      <filtered-out-file-type name="SVG" />
      <filtered-out-file-type name="textmate" />
      <filtered-out-file-type name="TOML" />
      <filtered-out-file-type name="UAST" />
      <filtered-out-file-type name="UastContextLanguage" />
      <filtered-out-file-type name="XHTML" />
      <filtered-out-file-type name="XML" />
      <filtered-out-file-type name="XPath" />
      <filtered-out-file-type name="XPath2" />
      <filtered-out-file-type name="XsdRegExp" />
      <filtered-out-file-type name="yaml" />
    </file-type-list>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2xnFznE6DU9bcNI8PTRZN6OeohF" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/src/views/xjzs" />
    <property name="project.structure.last.edited" value="库" />
    <property name="project.structure.proportion" value="0.0" />
    <property name="project.structure.side.proportion" value="0.0" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\gitee\zjyc-zgxjbszs-frontend\src\views\xjzs" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="de3eef48-8e4d-4694-bd94-a4d840c400a4" name="变更" comment="" />
      <created>1748566196947</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748566196947</updated>
    </task>
    <task id="LOCAL-00001" summary="1.首页发起询价、智能编审、智能问答图片修复。">
      <created>1749177781474</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749177781474</updated>
    </task>
    <task id="LOCAL-00002" summary="1.loadProjects为computed，处理报错，去掉mounted的loadProjects">
      <created>1749178253087</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749178253087</updated>
    </task>
    <task id="LOCAL-00003" summary="1.项目标准表，修改添加行的按钮样式">
      <created>1749179171165</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749179171165</updated>
    </task>
    <task id="LOCAL-00004" summary="1.智能编审页面科技科技感优化">
      <created>1750725388203</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1750725388203</updated>
    </task>
    <task id="LOCAL-00005" summary="1.文件库">
      <created>1752483783119</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752483783119</updated>
    </task>
    <task id="LOCAL-00006" summary="1.文件库的fileStatus修改">
      <created>1753064233339</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753064233339</updated>
    </task>
    <task id="LOCAL-00007" summary="1.文件库优化">
      <created>1753339854244</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753339854244</updated>
    </task>
    <task id="LOCAL-00008" summary="1.供应商询价bug修复">
      <created>1753691641352</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753691641352</updated>
    </task>
    <task id="LOCAL-00009" summary="1.导出和上传回函bug修复">
      <created>1753695929125</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753695929125</updated>
    </task>
    <task id="LOCAL-00010" summary="1.询价记录表基础修改">
      <created>1753778850072</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753778850072</updated>
    </task>
    <task id="LOCAL-00011" summary="1.采购询价记录表">
      <created>1753782929008</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753782929008</updated>
    </task>
    <task id="LOCAL-00012" summary="1.初版v1">
      <created>1753839758346</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753839758346</updated>
    </task>
    <task id="LOCAL-00013" summary="1.增加返回首页按钮">
      <created>1755565229927</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755565229927</updated>
    </task>
    <task id="LOCAL-00014" summary="1.区分用户端和管理端首页">
      <created>1755574135085</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755574135085</updated>
    </task>
    <task id="LOCAL-00015" summary="1.首页项目列表">
      <created>1755650327109</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1755650327109</updated>
    </task>
    <task id="LOCAL-00016" summary="1.新增按钮改为圆形图标">
      <created>1755651044934</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1755651044934</updated>
    </task>
    <task id="LOCAL-00017" summary="1.编制项目最高限价报告回显">
      <created>1755653439496</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1755653439496</updated>
    </task>
    <task id="LOCAL-00018" summary="1.标准表回显">
      <created>1755658230702</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1755658230702</updated>
    </task>
    <task id="LOCAL-00019" summary="1.引擎计算回显">
      <created>1755660127305</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1755660127305</updated>
    </task>
    <task id="LOCAL-00020" summary="1.工程类宽度显示异常问题修复">
      <created>1755662391577</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1755662391577</updated>
    </task>
    <task id="LOCAL-00021" summary="1.工程类标准表单回显">
      <created>1755680197405</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1755680197405</updated>
    </task>
    <task id="LOCAL-00022" summary="1.合规性检查回显">
      <created>1755681340614</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1755681340615</updated>
    </task>
    <option name="localTasksCounter" value="23" />
    <servers />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="v1" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="1.首页发起询价、智能编审、智能问答图片修复。" />
    <MESSAGE value="1.loadProjects为computed，处理报错，去掉mounted的loadProjects" />
    <MESSAGE value="1.项目标准表，修改添加行的按钮样式" />
    <MESSAGE value="1.智能编审页面科技科技感优化" />
    <MESSAGE value="1.文件库" />
    <MESSAGE value="1.文件库的fileStatus修改" />
    <MESSAGE value="1.文件库优化" />
    <MESSAGE value="1.供应商询价bug修复" />
    <MESSAGE value="1.导出和上传回函bug修复" />
    <MESSAGE value="1.询价记录表基础修改" />
    <MESSAGE value="1.采购询价记录表" />
    <MESSAGE value="1.初版v1" />
    <MESSAGE value="1.增加返回首页按钮" />
    <MESSAGE value="1.区分用户端和管理端首页" />
    <MESSAGE value="1.首页项目列表" />
    <MESSAGE value="1.新增按钮改为圆形图标" />
    <MESSAGE value="1.编制项目最高限价报告回显" />
    <MESSAGE value="1.标准表回显" />
    <MESSAGE value="1.引擎计算回显" />
    <MESSAGE value="1.工程类宽度显示异常问题修复" />
    <MESSAGE value="1.工程类标准表单回显" />
    <MESSAGE value="1.合规性检查回显" />
    <option name="LAST_COMMIT_MESSAGE" value="1.合规性检查回显" />
  </component>
</project>