--2025-06-13 为 xjzs_project_inquiry_record 表添加报价相关字段 
ALTER TABLE xjzs_project_inquiry_record ADD COLUMN quotation_total_amount DECIMAL(15,2);
ALTER TABLE xjzs_project_inquiry_record ADD COLUMN quotation_time TIMESTAMP;
ALTER TABLE xjzs_project_inquiry_record ADD COLUMN quotation_company VARCHAR(255);
COMMENT ON COLUMN xjzs_project_inquiry_record.quotation_total_amount IS '报价总金额';
COMMENT ON COLUMN xjzs_project_inquiry_record.quotation_time IS '报价时间';
COMMENT ON COLUMN xjzs_project_inquiry_record.quotation_company IS '报价公司';


-- 20250623 添加计价规则表
CREATE TABLE xjzs_pricing_rules (
    id int8 PRIMARY KEY,
    rule_name varchar(255),
    rule_type varchar(255),
    calculation_formula text,
    remark varchar(255),
    create_user int8,
    create_dept int8,
    create_time timestamp(6),
    update_user int8,
    update_time timestamp(6),
    status int2,
    is_deleted int2,
    tenant_id varchar(20)
);

-- 添加注释
COMMENT ON TABLE xjzs_pricing_rules IS '定价规则表';
COMMENT ON COLUMN xjzs_pricing_rules.id IS 'id';
COMMENT ON COLUMN xjzs_pricing_rules.rule_name IS '规则名';
COMMENT ON COLUMN xjzs_pricing_rules.rule_type IS '适用类型';
COMMENT ON COLUMN xjzs_pricing_rules.calculation_formula IS '计价公式';
COMMENT ON COLUMN xjzs_pricing_rules.remark IS '备注';
COMMENT ON COLUMN xjzs_pricing_rules.create_user IS '创建人';
COMMENT ON COLUMN xjzs_pricing_rules.create_dept IS '创建部门';
COMMENT ON COLUMN xjzs_pricing_rules.create_time IS '创建时间';
COMMENT ON COLUMN xjzs_pricing_rules.update_user IS '更新人';
COMMENT ON COLUMN xjzs_pricing_rules.update_time IS '更新时间';
COMMENT ON COLUMN xjzs_pricing_rules.status IS '状态';
COMMENT ON COLUMN xjzs_pricing_rules.is_deleted IS '是否已删除';
COMMENT ON COLUMN xjzs_pricing_rules.tenant_id IS '租户id';

-- 添加索引
CREATE INDEX idx_xjzs_pricing_rules_rule_type ON xjzs_pricing_rules(rule_type);
CREATE INDEX idx_xjzs_pricing_rules_status ON xjzs_pricing_rules(status);
CREATE INDEX idx_xjzs_pricing_rules_is_deleted ON xjzs_pricing_rules(is_deleted);
CREATE INDEX idx_xjzs_pricing_rules_create_time ON xjzs_pricing_rules(create_time);