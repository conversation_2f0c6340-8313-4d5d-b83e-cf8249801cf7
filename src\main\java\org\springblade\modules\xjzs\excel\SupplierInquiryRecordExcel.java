/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 供应商询价历史记录表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SupplierInquiryRecordExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * id;主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("id;主键")
	private Long id;
	/**
	 * 项目编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("项目编码")
	private String projectId;
	/**
	 * 询价时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("询价时间")
	private Date inquiryTime;
	/**
	 * 名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("名称")
	private String name;
	/**
	 * 内容
	 */
	@ColumnWidth(20)
	@ExcelProperty("内容")
	private String content;
	/**
	 * 数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("数量")
	private Long quantity;
	/**
	 * 询价供应商
	 */
	@ColumnWidth(20)
	@ExcelProperty("询价供应商")
	private String supplier;
	/**
	 * 询价渠道
	 */
	@ColumnWidth(20)
	@ExcelProperty("询价渠道")
	private String inquiryChannel;
	/**
	 * 单价
	 */
	@ColumnWidth(20)
	@ExcelProperty("单价")
	private Long unitPrice;
	/**
	 * 总价
	 */
	@ColumnWidth(20)
	@ExcelProperty("总价")
	private Long totalPrice;
	/**
	 * 备注
	 */
	@ColumnWidth(20)
	@ExcelProperty("备注")
	private String remark;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Long isDeleted;

}
