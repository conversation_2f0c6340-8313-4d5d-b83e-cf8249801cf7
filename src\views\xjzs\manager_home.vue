<template>
  <basic-container>
    <div class="home-container">
      <!-- 欢迎标题 -->
      <div class="welcome-section">
        <h2 class="welcome-title">欢迎使用最高限价编审助手</h2>
        <p class="welcome-subtitle">快速查看系统状态和最近项目</p>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-grid">
        <el-card class="statistics-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon blue">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="card-info">
              <p class="card-label">未开始项目</p>
              <div class="card-values">
                <p class="card-value-main">{{ statistics.notStartedProjectsCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="statistics-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon green">
              <el-icon><Check /></el-icon>
            </div>
            <div class="card-info">
              <p class="card-label">已完成项目</p>
              <div class="card-values">
                <p class="card-value-main">{{ statistics.completedProjectsCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="statistics-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon yellow">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="card-info">
              <p class="card-label">智能帮助</p>
              <div class="card-values">
                <p class="card-value-main">{{ statistics.companyUsageCount || 0 }}次</p>
              </div>
            </div>
          </div>
        </el-card>

<!--        <el-card class="statistics-card" shadow="hover">-->
<!--          <div class="card-content">-->
<!--            <div class="card-icon purple">-->
<!--              <el-icon><Money /></el-icon>-->
<!--            </div>-->
<!--            <div class="card-info">-->
<!--              <p class="card-label">已完成项目金额</p>-->
<!--              <div class="card-values">-->
<!--                <p class="card-value-main">{{ statistics.companyAmount || '¥0万' }}</p>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </el-card>-->
      </div>

      <!-- 最近项目 -->
      <el-card class="recent-projects-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3 class="card-title">项目列表</h3>
            <div class="search-controls">
              <el-select v-model="searchParams.year" placeholder="选择年份" style="width: 120px; margin-right: 10px;">
                <el-option label="2025年" value="2025"></el-option>
              </el-select>
              <el-input
                v-model="searchParams.name"
                placeholder="搜索项目..."
                style="width: 200px; margin-right: 10px;"
                clearable
              />
              <el-button type="primary" @click="searchProjects">查询</el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="projectList"
          v-loading="projectLoading"
          style="width: 100%"
        >
          <el-table-column label="项目名称" min-width="200">
            <template #default="scope">
              <div>
                <div class="project-name">{{ scope.row.name }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="reportCode" label="报告编号" width="150" ></el-table-column>
          <el-table-column prop="maxPriceLimit" label=" 最高限价金额" width="120" :formatter="formatPrice"></el-table-column>
          <el-table-column prop="dept" label="所属部门" width="120" />
          <el-table-column label="预算金额" width="120">
            <template #default="scope">
              {{ scope.row.budget ? scope.row.budget.toLocaleString() : 'N/A' }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="项目类型" width="120" />
          <el-table-column label="经办人" width="120">
            <template #default="scope">
              {{ scope.row.handlerUserName || '' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.projectStatus)">
                {{ scope.row.projectStatusName || '未开始' }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- 操作栏模块 -->
          <el-table-column prop="menu" label="操作"  align="center" width="100">
            <template  #default="scope">
              <el-button type="text" @click="viewProjectDetails(scope.row.id)" v-if="scope.row.reportCode">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="page.current"
            v-model:page-size="page.size"
            :page-sizes="[5, 10, 20, 50]"
            :total="page.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </basic-container>
</template>

<script setup name="Home">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Folder, Check, ChatDotRound, Money } from '@element-plus/icons-vue'
import { getProjectStatistics, getPage} from '@/api/xjzs/project'
import { useRouter } from 'vue-router'
const router = useRouter() // 获取 router 实例

// 响应式数据
const statistics = ref({})
const projectList = ref([])
const projectLoading = ref(false)

const searchParams = reactive({
  year: '2025',
  name: ''
})

const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 生命周期
onMounted(() => {
  loadStatistics()
  loadProjects()
})

// 加载统计数据
const loadStatistics = async () => {
  try {
    const res = await getProjectStatistics()
    if (res.data.success) {
      statistics.value = res.data.data
    } else {
      ElMessage.error(res.data.msg || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 加载最近项目
const loadProjects = async () => {
  projectLoading.value = true
  try {
    const res = await getPage(page.current, page.size, searchParams)
    if (res.data.success) {
      projectList.value = res.data.data.records
      page.total = res.data.data.total
    } else {
      ElMessage.error(res.data.msg || '获取项目数据失败')
    }
  } catch (error) {
    console.error('获取项目数据失败:', error)
    ElMessage.error('获取项目数据失败')
  } finally {
    projectLoading.value = false
  }
}

// 搜索项目
const searchProjects = () => {
  page.current = 1
  loadProjects()
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 0: // 未开始
      return 'info'
    case 1: // 询价中
      return 'warning'
    case 2: // 已完成
      return 'success'
    case 9: // 已终止
      return 'danger'
    default:
      return 'info'
  }
}

// 分页处理
const handleSizeChange = (val) => {
  page.size = val
  page.current = 1
  loadProjects()
}

const handleCurrentChange = (val) => {
  page.current = val
  loadProjects()
}

const viewProjectDetails = (id) => {
  router.push({
    path: `/project-assistant-waterfall`,
    query: {
      projectId: id,
      step: 3,
      mode: 'detail'
    }
  })
}

// 格式化价格
const formatPrice = (row, column, cellValue) => {
  if (!cellValue) return ''
  return cellValue.toLocaleString()
}


</script>

<style scoped>
.home-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.statistics-card {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.card-icon.blue {
  background-color: #dbeafe;
  color: #2563eb;
}

.card-icon.green {
  background-color: #dcfce7;
  color: #16a34a;
}

.card-icon.yellow {
  background-color: #fef3c7;
  color: #d97706;
}

.card-icon.purple {
  background-color: #f3e8ff;
  color: #9333ea;
}

.card-info {
  flex: 1;
}

.card-label {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-values {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-value-main {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.card-value-sub {
  font-size: 16px;
  font-weight: 500;
  color: #4b5563;
  margin: 0;
}

.recent-projects-card {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.search-controls {
  display: flex;
  align-items: center;
}

.project-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.project-number {
  font-size: 12px;
  color: #6b7280;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-controls {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>
