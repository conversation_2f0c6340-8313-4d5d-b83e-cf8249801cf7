import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/xjzs/pricing-rules/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getPage = (current, size, params) => {
  return request({
    url: '/xjzs/pricing-rules/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/xjzs/pricing-rules/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/xjzs/pricing-rules/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/xjzs/pricing-rules/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/xjzs/pricing-rules/update',
    method: 'post',
    data: row
  })
}

export const submit = (row) => {
  return request({
    url: '/xjzs/pricing-rules/submit',
    method: 'post',
    data: row
  })
}

export const getAllPricingRules = () => {
  return request({
    url: '/xjzs/pricing-rules/all',
    method: 'get'
  })
}

/**
 * 导出计价规则数据
 */
export const exportPricingRules = (params) => {
  return request({
    url: '/xjzs/pricing-rules/export-pricing-rules',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
