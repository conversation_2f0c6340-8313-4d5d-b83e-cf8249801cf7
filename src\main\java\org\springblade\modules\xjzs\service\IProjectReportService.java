package org.springblade.modules.xjzs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.xjzs.entity.ProjectReportEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectReportCheckResultVO;

import java.util.List;
import java.util.Map;
import reactor.core.publisher.Flux;
import org.springblade.modules.dify.resp.WorkflowStreamResponse;

/**
 * 项目报告服务接口
 */
public interface IProjectReportService extends IService<ProjectReportEntity> {
    /**
     * 保存项目报告
     * @param report 报告实体
     * @return 是否成功
     */
    boolean saveProjectReport(ProjectReportEntity report);
    
    /**
     * 检查项目是否已存在报告
     * @param projectId 项目ID
     * @return 是否存在
     */
    boolean checkReportExists(Long projectId);

    /**
     * 检查报告是否合规
     * @param projectId 项目ID
     * @return 是否存在
     */
    List<ProjectReportCheckResultVO> checkReport(Long projectId);
    void updateReportCheckContent(Long projectId,  String checkReportContent);
    /**
     * 获取项目报告
     * @param projectId 项目ID
     * @return 报告实体
     */
    ProjectReportEntity getProjectReport(Long projectId);


    /**
     * 使用Dify agent进行合规性检查（流式）
     * @param projectId 项目ID
     * @return Dify工作流响应流
     */
    Flux<WorkflowStreamResponse> checkComplianceWithDifyStream(Long projectId);

    /**
     * 工程类合规性检查接口 - 使用Dify agent（流式返回）
     * @param projectId 项目ID
     * @return Dify工作流响应流
     */
    Flux<WorkflowStreamResponse> checkEngineeringComplianceWithDifyStream(Long projectId);

    /**
     * 采购类合规性检查接口 - 使用Dify agent（流式返回）
     * @param projectId 项目ID
     * @return Dify工作流响应流
     */
    Flux<WorkflowStreamResponse> checkProcurementComplianceWithDifyStream(Long projectId);


}