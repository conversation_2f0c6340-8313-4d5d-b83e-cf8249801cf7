import request from '@/axios';

/**
 * 获取首页统计数据
 */
export const getHomeStatistics = () => {
  return request({
    url: '/xjzs/home/<USER>',
    method: 'get'
  })
}

/**
 * 获取最近项目列表
 */
export const getRecentProjects = (current, size, params) => {
  return request({
    url: '/xjzs/home/<USER>',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

/**
 * 查看项目详情
 */
export const viewProject = (id) => {
  return request({
    url: `/xjzs/home/<USER>/${id}`,
    method: 'get'
  })
}

/**
 * 编辑项目
 */
export const editProject = (id) => {
  return request({
    url: `/xjzs/home/<USER>/${id}/edit`,
    method: 'get'
  })
}
