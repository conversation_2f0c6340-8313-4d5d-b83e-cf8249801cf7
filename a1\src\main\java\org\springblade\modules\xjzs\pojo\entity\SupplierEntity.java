/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 供应商信息 实体类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@TableName("xjzs_supplier")
@Schema(description = "Supplier对象")
@EqualsAndHashCode(callSuper = true)
public class SupplierEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 供应商名
	 */
	@Schema(description = "供应商名")
	private String name;
	/**
	 * 联系人
	 */
	@Schema(description = "联系人")
	private String contactPerson;
	/**
	 * 电话
	 */
	@Schema(description = "电话")
	private String phone;
	/**
	 * 地址
	 */
	@Schema(description = "地址")
	private String address;
	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

}
