package org.springblade.modules.xjzs.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 竣工决算审计服务实现类
 */
@Service
@Slf4j
public class CompletionSettlementAuditServiceImpl implements IAuditService {
    
    @Override
    public String getServiceType() {
        return "竣工决算审计";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        List<String> calculationProcess = new ArrayList<>();
        
        try {
            calculationProcess.add("开始计算竣工决算审计费用：");
            
            // 解析表格数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> tableDataList = objectMapper.readValue(
                params.getTableData(), 
                new TypeReference<List<Map<String, Object>>>() {}
            );
            
            if (tableDataList.isEmpty()) {
                calculationProcess.add("错误：表格数据为空");
                return AuditResult.fail("表格数据不能为空");
            }
            
            Map<String, Object> row = tableDataList.get(0);
            
            // 获取建设项目总投资
            BigDecimal totalInvestment = getDecimalValue(row, "totalInvestment");
            if (totalInvestment == null || totalInvestment.compareTo(BigDecimal.ZERO) <= 0) {
                calculationProcess.add("错误：建设项目总投资无效或为空");
                return AuditResult.fail("建设项目总投资不能为空或小于等于0");
            }
            
            calculationProcess.add("建设项目总投资：" + totalInvestment.toPlainString() + " 万元");
            
            // 计算竣工决算审计费用
            BigDecimal auditFee = calculateCompletionSettlementAuditFee(totalInvestment, calculationProcess);
            
            // 最高限价 = 竣工决算审计费用
            BigDecimal totalCost = auditFee;
            
            calculationProcess.add("最高限价 = 竣工决算审计费用 = " + totalCost.toPlainString() + " 元");
            
            // 创建结果
            AuditResult result = AuditResult.success(totalCost);
            result.setCalculationProcess(calculationProcess);
            
            // 添加计算详情
            Map<String, Object> auditDetail = new HashMap<>();
            auditDetail.put("type", "竣工决算审计");
            auditDetail.put("totalInvestment", totalInvestment);
            auditDetail.put("fee", auditFee);
            auditDetail.put("description", String.format("竣工决算审计费用: %s万元 = %s元", totalInvestment, auditFee));
            result.addDetail(auditDetail);
            
            Map<String, Object> totalDetail = new HashMap<>();
            totalDetail.put("type", "最高限价");
            totalDetail.put("fee", totalCost);
            totalDetail.put("description", String.format("最高限价 = 竣工决算审计费用 = %s元", totalCost));
            result.addDetail(totalDetail);
            
            return result;
            
        } catch (Exception e) {
            log.error("计算竣工决算审计费用时发生错误", e);
            calculationProcess.add("计算过程中发生错误: " + e.getMessage());
            AuditResult errorResult = AuditResult.fail("计算错误: " + e.getMessage());
            errorResult.setCalculationProcess(calculationProcess);
            return errorResult;
        }
    }
    
    /**
     * 计算竣工决算审计费用
     * @param totalInvestment 建设项目总投资（万元）
     * @param calculationProcess 计算过程记录
     * @return 竣工决算审计费用（元）
     */
    private BigDecimal calculateCompletionSettlementAuditFee(BigDecimal totalInvestment, List<String> calculationProcess) {
        BigDecimal fee = BigDecimal.ZERO;
        
        if (totalInvestment.compareTo(new BigDecimal("500")) <= 0) {
            // ≤500万元：费率 = 0.2%
            fee = totalInvestment.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.002"));
            calculationProcess.add("建设项目总投资 ≤ 500万元，费率 = 0.2%");
            calculationProcess.add("计算：" + totalInvestment + " × 10000 × 0.002 = " + fee.toPlainString() + " 元");
        } else if (totalInvestment.compareTo(new BigDecimal("5000")) <= 0) {
            // 500~5000万元：分段计算
            BigDecimal baseFee = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.002"));
            BigDecimal excessAmount = totalInvestment.subtract(new BigDecimal("500"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0018"));
            fee = baseFee.add(excessFee);
            calculationProcess.add("500万元 < 建设项目总投资 ≤ 5000万元，分段计算：");
            calculationProcess.add("基础费用：500 × 10000 × 0.002 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.0018 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        } else if (totalInvestment.compareTo(new BigDecimal("10000")) <= 0) {
            // 5000万元~1亿元：分段计算
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.002"));
            BigDecimal baseFee2 = new BigDecimal("4500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0018"));
            BigDecimal excessAmount = totalInvestment.subtract(new BigDecimal("5000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0012"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            calculationProcess.add("5000万元 < 建设项目总投资 ≤ 10000万元，分段计算：");
            calculationProcess.add("第一段费用：500 × 10000 × 0.002 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段费用：4500 × 10000 × 0.0018 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.0012 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        } else {
            // >1亿元：需要具体规则
            calculationProcess.add("建设项目总投资 > 10000万元，暂无具体计算规则");
        }
        
        return fee.setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}