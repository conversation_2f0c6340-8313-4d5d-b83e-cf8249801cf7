/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.service.impl;

import org.springblade.modules.xjzs.pojo.entity.SupplierEntity;
import org.springblade.modules.xjzs.pojo.vo.SupplierVO;
import org.springblade.modules.xjzs.excel.SupplierExcel;
import org.springblade.modules.xjzs.mapper.SupplierMapper;
import org.springblade.modules.xjzs.service.ISupplierService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 供应商信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class SupplierServiceImpl extends BaseServiceImpl<SupplierMapper, SupplierEntity> implements ISupplierService {

	@Override
	public IPage<SupplierVO> selectSupplierPage(IPage<SupplierVO> page, SupplierVO supplier) {
		return page.setRecords(baseMapper.selectSupplierPage(page, supplier));
	}


	@Override
	public List<SupplierExcel> exportSupplier(Wrapper<SupplierEntity> queryWrapper) {
		List<SupplierExcel> supplierList = baseMapper.exportSupplier(queryWrapper);
		//supplierList.forEach(supplier -> {
		//	supplier.setTypeName(DictCache.getValue(DictEnum.YES_NO, Supplier.getType()));
		//});
		return supplierList;
	}

}
