<template>
  <basic-container>
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               :upload-before="uploadBefore"
               :upload-after="uploadAfter"
               :upload-error="uploadError"
               :http-request="handleCustomUpload"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.file_library_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      
      <!-- 状态列自定义显示 -->
      <template #fileStatus="scope">
        <el-tag
          :type="scope.row.fileStatus === '生效中' ? 'success' : 'danger'"
          size="small">
          {{ scope.row.fileStatus }}
        </el-tag>
      </template>

      <!-- 文件名称列自定义显示 -->
      <template #fileName="scope">
        <div>
          <div class="text-sm text-gray-900">{{ scope.row.fileName }}</div>
          <div class="text-sm text-gray-500" v-if="scope.row.fileNumber">{{ scope.row.fileNumber }}</div>
        </div>
      </template>

      <!-- 文件信息列自定义显示 -->
      <template #fileInfo="scope">
        <div v-if="scope.row.originalFileName">
          <div class="text-sm text-gray-900">{{ scope.row.originalFileName }}</div>
          <div class="text-sm text-gray-500">{{ formatFileSize(scope.row.fileSize) }}</div>
        </div>
        <div v-else class="text-sm text-gray-500">无文件</div>
      </template>

      <!-- 操作列自定义显示 -->
      <template #menu="scope">
        <el-button
          type="text"
          size="small"
          icon="el-icon-download"
          @click="handleDownload(scope.row)"
          v-if="scope.row.filePath">
          下载
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, exportFileLibrary, uploadAndSave, downloadFile, updateFileAndRecord} from "@/api/xjzs/fileLibrary";
  import option from "@/option/xjzs/fileLibrary";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';

  export default {
    name: 'FileLibrary',
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: this.getUploadOption(),
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.file_library_add, false),
          viewBtn: this.validData(this.permission.file_library_view, false),
          delBtn: this.validData(this.permission.file_library_delete, false),
          editBtn: this.validData(this.permission.file_library_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 获取上传配置
      getUploadOption() {
        const uploadOption = JSON.parse(JSON.stringify(option)); // 深拷贝

        // 配置新增时的文件上传
        const uploadColumn = uploadOption.column.find(col => col.prop === 'fileUpload');
        if (uploadColumn) {
          uploadColumn.action = '/api/xjzs/file-library/upload-and-save';
          uploadColumn.headers = {
            'Authorization': `bearer ${getToken()}`,
            'Blade-Auth': `bearer ${getToken()}`
          };
          // 禁用默认的上传行为，使用自定义上传
          uploadColumn.httpRequest = this.handleCustomUpload;
        }

        // 配置编辑时的文件更新上传
        const updateColumn = uploadOption.column.find(col => col.prop === 'fileUpdate');
        if (updateColumn) {
          updateColumn.action = '/api/xjzs/file-library/upload-and-update';
          updateColumn.headers = {
            'Authorization': `bearer ${getToken()}`,
            'Blade-Auth': `bearer ${getToken()}`
          };
          // 禁用默认的上传行为，使用自定义上传
          updateColumn.httpRequest = this.handleCustomUpdateUpload;
        }

        return uploadOption;
      },
      rowSave(row, done, loading) {
        // 如果有文件上传，文件上传成功后会自动调用 uploadAfter 方法处理保存
        // 这里只处理没有文件上传的情况
        if (!row.fileUpload || row.fileUpload.length === 0) {
          add(row).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            done();
          }, error => {
            loading();
            window.console.log(error);
          });
        } else {
          // 有文件上传时，等待文件上传完成
          done();
        }
      },
      rowUpdate(row, index, done, loading) {
        // 如果有文件上传，文件上传成功后会自动调用 handleCustomUpdateUpload 方法处理保存
        // 这里只处理没有文件上传的情况
        if (!row.fileUpdate || row.fileUpdate.length === 0) {
          update(row).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            done();
          }, error => {
            loading();
            window.console.log(error);
          });
        } else {
          // 有文件上传时，等待文件上传完成
          done();
        }
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        NProgress.start();
        exportFileLibrary(this.query).then(response => {
          downloadXls(response, `文件库数据_${dateNow()}.xlsx`);
          NProgress.done();
        }).catch(() => {
          NProgress.done();
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },

      // 文件上传前的处理
      uploadBefore(file, done, loading, column) {
        // 验证文件类型
        const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.zip', '.rar'];
        const fileName = file.name.toLowerCase();
        const isValidType = allowedTypes.some(type => fileName.endsWith(type));

        if (!isValidType) {
          this.$message.error('不支持的文件格式，请上传PDF、DOC、DOCX、XLS、XLSX、TXT、ZIP、RAR格式的文件');
          return false;
        }

        // 验证文件大小 (50MB)
        const isValidSize = file.size / 1024 / 1024 < 50;
        if (!isValidSize) {
          this.$message.error('文件大小不能超过50MB');
          return false;
        }

        // 检查必填字段
        if (!this.form.fileName) {
          this.$message.error('请先填写文件名称');
          return false;
        }
        if (!this.form.fileType) {
          this.$message.error('请先选择文件类型');
          return false;
        }

        loading();
        done();
      },

      // 文件上传成功后的处理
      uploadAfter(res, done, loading, column) {
        // 这个方法在使用自定义上传时不会被调用
        // 我们需要在 httpRequest 中处理上传逻辑
        done();
      },

      // 文件上传失败的处理
      uploadError(error, column) {
        this.$message.error('文件上传失败: ' + error.message);
      },

      // 自定义文件上传处理
      handleCustomUpload(uploadOption) {
        const { file, onProgress, onSuccess, onError } = uploadOption;

        // 验证文件类型
        const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.zip', '.rar'];
        const fileName = file.name.toLowerCase();
        const isValidType = allowedTypes.some(type => fileName.endsWith(type));

        if (!isValidType) {
          this.$message.error('不支持的文件格式，请上传PDF、DOC、DOCX、XLS、XLSX、TXT、ZIP、RAR格式的文件');
          onError(new Error('不支持的文件格式'));
          return;
        }

        // 验证文件大小 (50MB)
        const isValidSize = file.size / 1024 / 1024 < 50;
        if (!isValidSize) {
          this.$message.error('文件大小不能超过50MB');
          onError(new Error('文件大小超限'));
          return;
        }

        // 检查必填字段
        if (!this.form.fileName) {
          this.$message.error('请先填写文件名称');
          onError(new Error('请先填写文件名称'));
          return;
        }
        if (!this.form.fileType) {
          this.$message.error('请先选择文件类型');
          onError(new Error('请先选择文件类型'));
          return;
        }

        // 准备文件数据
        const fileData = {
          fileName: this.form.fileName,
          fileType: this.form.fileType,
          publishUnit: this.form.publishUnit,
          publishTime: this.form.publishTime,
          status: this.form.status || '生效中',
          remark: this.form.remark
        };

        // 调用上传并保存接口
        uploadAndSave(file, fileData).then(response => {
          if (response.data.success) {
            this.$message.success('文件上传并保存成功');
            // 更新表单数据
            Object.assign(this.form, response.data.data);
            this.onLoad(this.page);
            onSuccess(response.data.data);
          } else {
            this.$message.error('保存文件记录失败: ' + response.data.msg);
            onError(new Error(response.data.msg));
          }
        }).catch(error => {
          console.error('文件上传错误:', error);
          this.$message.error('文件上传失败: ' + (error.response?.data?.msg || error.message));
          onError(error);
        });
      },

      // 自定义文件更新上传处理（编辑时）
      handleCustomUpdateUpload(uploadOption) {
        const { file, onProgress, onSuccess, onError } = uploadOption;

        // 验证文件类型
        const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.zip', '.rar'];
        const fileName = file.name.toLowerCase();
        const isValidType = allowedTypes.some(type => fileName.endsWith(type));

        if (!isValidType) {
          this.$message.error('不支持的文件格式，请上传PDF、DOC、DOCX、XLS、XLSX、TXT、ZIP、RAR格式的文件');
          onError(new Error('不支持的文件格式'));
          return;
        }

        // 验证文件大小 (50MB)
        const isValidSize = file.size / 1024 / 1024 < 50;
        if (!isValidSize) {
          this.$message.error('文件大小不能超过50MB');
          onError(new Error('文件大小超限'));
          return;
        }

        // 检查是否有记录ID
        if (!this.form.id) {
          this.$message.error('无法获取记录ID，请刷新页面重试');
          onError(new Error('无法获取记录ID'));
          return;
        }

        // 准备更新数据
        const updateData = {
          id: this.form.id,
          fileName: this.form.fileName,
          fileType: this.form.fileType,
          publishUnit: this.form.publishUnit,
          publishTime: this.form.publishTime,
          status: this.form.status || '生效中',
          remark: this.form.remark
        };

        // 调用文件更新接口
        updateFileAndRecord(file, updateData).then(response => {
          if (response.data.success) {
            this.$message.success('文件更新成功');
            // 更新表单数据
            Object.assign(this.form, response.data.data);
            this.onLoad(this.page);
            onSuccess(response.data.data);
          } else {
            this.$message.error('文件更新失败: ' + response.data.msg);
            onError(new Error(response.data.msg));
          }
        }).catch(error => {
          console.error('文件更新错误:', error);
          this.$message.error('文件更新失败: ' + (error.response?.data?.msg || error.message));
          onError(error);
        });
      },

      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },

      // 格式化文件大小
      formatFileSize(size) {
        if (!size) return '0 B';
        const units = ['B', 'KB', 'MB', 'GB'];
        let index = 0;
        while (size >= 1024 && index < units.length - 1) {
          size /= 1024;
          index++;
        }
        return `${size.toFixed(2)} ${units[index]}`;
      },

      // 文件下载
      handleDownload(row) {
        if (!row.id) {
          this.$message.warning('文件ID不存在');
          return;
        }

        // 直接打开下载链接，后端会重定向到文件URL
        const downloadUrl = `/xjzs/file-library/download/${row.id}`;
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = row.originalFileName || row.fileName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$message.success('文件下载已开始');
      }
    }
  };
</script>

<style scoped>
.text-sm {
  font-size: 0.875rem;
}
.text-gray-900 {
  color: #111827;
}
.text-gray-500 {
  color: #6b7280;
}
</style>
