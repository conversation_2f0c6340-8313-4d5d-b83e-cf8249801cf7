/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.mapper;

import org.apache.ibatis.annotations.Insert;
import org.springblade.modules.xjzs.pojo.entity.ProductAttributesEntity;
import org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity;
import org.springblade.modules.xjzs.pojo.vo.TrainingFeeVO;
import org.springblade.modules.xjzs.excel.TrainingFeeExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 计价标准 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface TrainingFeeMapper extends BaseMapper<TrainingFeeEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param trainingFee 查询参数
	 * @return List<TrainingFeeVO>
	 */
	List<TrainingFeeVO> selectTrainingFeePage(IPage page, TrainingFeeVO trainingFee);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<TrainingFeeExcel>
	 */
	List<TrainingFeeExcel> exportTrainingFee(@Param("ew") Wrapper<TrainingFeeEntity> queryWrapper);


	/**
	 * 通过商品名称获取单位
	 * @param feeName
	 * @return
	 */
	List<TrainingFeeVO> getDisUnit(@Param("feeName") String feeName);


	/**
	 * 通过商品名称获取单位
	 * @param feeName
	 * @return
	 */
	List<TrainingFeeEntity> searchByKeywords(@Param("feeName") String feeName,@Param("unit") String unit,@Param("keyWords") List<String> keyWords);



	/**
	 * 插入商品属性
	 * 
	 * @param feeName 费用名称
	 * @param attributeName 属性名称
	 * @param attributeValue 属性值
	 * @return 影响的行数
	 */
	@Insert("INSERT INTO xjzs_product_attributes (fee_name, attribute_name, attribute_value) " +
			"VALUES (#{feeName}, #{attributeName}, #{attributeValue}) " +
			"ON CONFLICT (fee_name, attribute_name, attribute_value) DO NOTHING")
	int insertProductAttribute(@Param("feeName") String feeName, 
							  @Param("attributeName") String attributeName, 
							  @Param("attributeValue") String attributeValue);

}


