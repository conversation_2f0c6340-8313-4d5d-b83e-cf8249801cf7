/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.xjzs.enums.ProjectStatusEnum;
import org.springblade.modules.xjzs.excel.ProjectExcel;
import org.springblade.modules.xjzs.mapper.ProjectMapper;
import org.springblade.modules.xjzs.pojo.entity.ProjectEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectStatisticsVO;
import org.springblade.modules.xjzs.pojo.vo.ProjectVO;
import org.springblade.modules.xjzs.service.IProjectService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 项目信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class ProjectServiceImpl extends BaseServiceImpl<ProjectMapper, ProjectEntity> implements IProjectService {

    private static final Logger log = LoggerFactory.getLogger(ProjectServiceImpl.class);

    @Override
    public IPage<ProjectVO> selectProjectPage(IPage<ProjectVO> page, ProjectVO project) {
        BladeUser currentUser = AuthUtil.getUser();
        boolean isAdmin = isAdminRole(currentUser);
        // 权限控制：非管理员只能查看自己的项目
        if (!isAdmin) {
            project.setHandlerId(currentUser.getUserId());
        }

        return page.setRecords(baseMapper.selectProjectPage(page, project));
    }

    @Override
    public List<ProjectExcel> exportProject(Wrapper<ProjectEntity> queryWrapper) {
        List<ProjectExcel> projectList = baseMapper.exportProject(queryWrapper);
        // projectList.forEach(project -> {
        // project.setTypeName(DictCache.getValue(DictEnum.YES_NO, Project.getType()));
        // });
        return projectList;
    }

    @Override
    public void importProject(List<ProjectExcel> data, Boolean isCovered) {
        List<ProjectEntity> list = new ArrayList<>();

        // 遍历Excel数据
        data.forEach(projectExcel -> {
            // 检查项目名是否为空
            if (StringUtil.isBlank(projectExcel.getName())) {
                log.warn("跳过导入项目：项目名称为空");
                return; // 跳过此条记录
            }

            ProjectEntity project = Objects.requireNonNull(BeanUtil.copyProperties(projectExcel, ProjectEntity.class));
            list.add(project);
        });

        // 如果没有有效数据，直接返回
        if (list.isEmpty()) {
            log.info("没有有效的项目数据需要导入");
            return;
        }

        if (!isCovered) {
            // 覆盖模式：直接使用saveOrUpdateBatch
            this.saveOrUpdateBatch(list);
        } else {
            // 非覆盖模式：检查是否有同名项目，有则更新，无则新增
            List<ProjectEntity> toUpdateList = new ArrayList<>();
            List<ProjectEntity> toInsertList = new ArrayList<>();

            for (ProjectEntity project : list) {
                // 查询是否存在同名项目
                LambdaQueryWrapper<ProjectEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ProjectEntity::getName, project.getName());
                ProjectEntity existingProject = this.getOne(queryWrapper, false);

                if (existingProject != null) {
                    // 存在同名项目，执行更新操作
                    log.info("发现同名项目：{}，执行更新操作", project.getName());
                    project.setId(existingProject.getId()); // 设置ID以便更新
                    toUpdateList.add(project);
                } else {
                    // 不存在同名项目，执行新增操作
                    toInsertList.add(project);
                }
            }

            // 批量更新已存在的项目
            if (!toUpdateList.isEmpty()) {
                this.updateBatchById(toUpdateList);
            }

            // 批量插入新项目
            if (!toInsertList.isEmpty()) {
                this.saveBatch(toInsertList);
            }
        }
    }

    @Override
    public ProjectStatisticsVO getProjectStatistics() {
        ProjectStatisticsVO statistics = new ProjectStatisticsVO();
        BladeUser currentUser = AuthUtil.getUser();
        boolean isAdmin = isAdminRole(currentUser);

        // 未开始项目统计
        Integer notStarted = getProjectStatistics(ProjectStatusEnum.NOT_STARTED.getCode(), currentUser, isAdmin);
        statistics.setNotStartedProjectsCount(notStarted);

        // 已完成项目统计
        Integer completed = getProjectStatistics(ProjectStatusEnum.COMPLETED.getCode(), currentUser, isAdmin);
        statistics.setCompletedProjectsCount(completed);

        // 智能帮助统计（暂时使用模拟数据，后续可扩展）
        statistics.setCompanyUsageCount(0);

        // 已完成项目金额统计
        String amount = getProjectAmountStatistics(currentUser, isAdmin);
        statistics.setCompanyAmount(amount);

        return statistics;
    }


    /**
     * 判断是否为管理员角色
     */
    private boolean isAdminRole(BladeUser user) {
        if (user == null || Func.isBlank(user.getRoleName())) {
            return false;
        }
        String roleName = user.getRoleName();
        return "administrator".equals(roleName) || "admin".equals(roleName);
    }


    /**
     * 获取项目统计数据
     */
    private Integer getProjectStatistics(Integer status, BladeUser user, boolean isAdmin) {
        QueryWrapper<ProjectEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_status", status);
        if (!isAdmin) {
            queryWrapper.eq("handler_id", user.getUserId());
        }

        // 公司统计（所有项目）
        int companyCount = Math.toIntExact(baseMapper.selectCount(queryWrapper));
        return companyCount;
    }

    /**
     * 获取项目金额统计
     */
    private String getProjectAmountStatistics(BladeUser user, boolean isAdmin) {
        // 查询已完成项目的金额统计
        QueryWrapper<ProjectEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_status", ProjectStatusEnum.COMPLETED.getCode());
        if (!isAdmin) {
            queryWrapper.eq("handler_id", user.getUserId());
        }

        queryWrapper.select("SUM(budget) as total_budget");

        List<ProjectEntity> companyProjects = baseMapper.selectList(queryWrapper);
        BigDecimal companyAmount = BigDecimal.ZERO;
        if (!companyProjects.isEmpty() && companyProjects.get(0) != null && companyProjects.get(0).getBudget() != null) {
            companyAmount = BigDecimal.valueOf(companyProjects.get(0).getBudget());
        }

        // 转换为万元并格式化
        return "¥" + companyAmount.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP) + "万";
    }
}
