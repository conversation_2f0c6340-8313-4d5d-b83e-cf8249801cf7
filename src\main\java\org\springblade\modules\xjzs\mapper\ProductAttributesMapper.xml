<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.ProductAttributesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="productAttributesResultMap" type="org.springblade.modules.xjzs.pojo.entity.ProductAttributesEntity">
        <result column="id" property="id"/>
        <result column="fee_name" property="feeName"/>
        <result column="attribute_name" property="attributeName"/>
        <result column="attribute_value" property="attributeValue"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>


    <select id="selectProductAttributesPage" resultMap="productAttributesResultMap">
        select * from xjzs_product_attributes where is_deleted = 0
    </select>


    <select id="exportProductAttributes" resultType="org.springblade.modules.xjzs.excel.ProductAttributesExcel">
        SELECT * FROM xjzs_product_attributes ${ew.customSqlSegment}
    </select>
    <select id="getProductAttributesByUnit" resultType="org.springblade.modules.xjzs.pojo.entity.ProductAttributesEntity">
        WITH known_fees AS (
        SELECT specification
        FROM xjzs_training_fee
        where fee_name ='${feeName}'  and  unit ='${unit}'
        )
        SELECT a.*
        FROM xjzs_product_attributes a
        left join known_fees k ON POSITION(a.attribute_value IN k.specification) > 0 where k.specification is not null
    </select>

</mapper>
