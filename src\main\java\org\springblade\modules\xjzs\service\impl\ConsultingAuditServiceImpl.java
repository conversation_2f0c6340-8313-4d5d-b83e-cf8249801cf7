package org.springblade.modules.xjzs.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工程咨询类审计服务实现
 */
@Service
@Slf4j
public class ConsultingAuditServiceImpl implements IAuditService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public String getServiceType() {
        return "工程咨询类";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        try {
            // 解析标准表数据
            List<Map<String, Object>> tableData = null;
            if (StringUtil.isNotBlank(params.getTableData())) {
                tableData = objectMapper.readValue(params.getTableData(), new TypeReference<List<Map<String, Object>>>() {});
            }
            
            if (tableData == null || tableData.isEmpty()) {
                return AuditResult.fail("工程咨询类标准表数据不能为空");
            }
            
            // 创建计算结果
            AuditResult result = AuditResult.success(BigDecimal.ZERO);
            BigDecimal totalCost = BigDecimal.ZERO;
            
            // 遍历每一行标准表数据进行计算
            for (Map<String, Object> row : tableData) {
                // 获取工程类别
                String category = (String) row.get("category");
                // 获取工作成果
                String workResult = (String) row.get("workResult");
                // 获取复杂系数
                String complexityFactorStr = (String) row.get("complexityFactor");
                BigDecimal complexityFactor = new BigDecimal(complexityFactorStr != null ? complexityFactorStr : "1.0");
                
                // 计算基础费用
                BigDecimal baseFee = calculateBaseFee(category, workResult);
                
                // 计算调整系数
                BigDecimal adjustmentFactor = getAdjustmentFactor(row);
                
                // 计算该行的费用
                BigDecimal rowFee = baseFee.multiply(complexityFactor).multiply(adjustmentFactor).setScale(2, RoundingMode.HALF_UP);
                
                // 添加计算步骤
                Map<String, Object> detail = new HashMap<>();
                detail.put("category", category);
                detail.put("workResult", workResult);
                detail.put("baseFee", baseFee);
                detail.put("complexityFactor", complexityFactor);
                detail.put("adjustmentFactor", adjustmentFactor);
                detail.put("fee", rowFee);
                detail.put("description", category + " - " + workResult + " 基础费用: " + baseFee + "元 × 复杂系数: " + complexityFactor + " × 调整系数: " + adjustmentFactor + " = " + rowFee + "元");
                result.addDetail(detail);
                
                totalCost = totalCost.add(rowFee);
            }
            
            result.setTotalCost(totalCost);
            return result;
        } catch (Exception e) {
            log.error("计算工程咨询类审计费用时发生错误", e);
            return AuditResult.fail("计算错误: " + e.getMessage());
        }
    }
    
    /**
     * 计算基础费用
     */
    private BigDecimal calculateBaseFee(String category, String workResult) {
        // 根据工程类别和工作成果确定基础费用
        if ("building".equals(category)) {
            // 建筑工程咨询类别
            return new BigDecimal("50000");
        } else if ("municipal".equals(category)) {
            // 市政工程咨询类别
            return new BigDecimal("80000");
        } else {
            // 默认基础费用
            return new BigDecimal("30000");
        }
    }
    
    /**
     * 获取调整系数
     */
    private BigDecimal getAdjustmentFactor(Map<String, Object> row) {
        // 从标准表行中获取调整系数
        String adjustmentFactorStr = (String) row.get("adjustmentFactor");
        if (StringUtil.isNotBlank(adjustmentFactorStr)) {
            try {
                return new BigDecimal(adjustmentFactorStr);
            } catch (NumberFormatException e) {
                log.warn("调整系数格式不正确: {}", adjustmentFactorStr);
            }
        }
        
        // 默认调整系数
        return BigDecimal.ONE;
    }
}
