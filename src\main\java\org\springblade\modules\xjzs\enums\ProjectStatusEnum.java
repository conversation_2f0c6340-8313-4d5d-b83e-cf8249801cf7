/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.xjzs.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Getter
@AllArgsConstructor
public enum ProjectStatusEnum {

    /**
     * 未开始
     */
    NOT_STARTED(0, "未开始"),

    /**
     * 询价中
     */
    INQUIRING(1, "询价中"),

    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),

    /**
     * 已终止
     */
    TERMINATED(9, "已终止");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(Integer code) {
        if (code == null) {
            return NOT_STARTED.getName();
        }
        
        for (ProjectStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return NOT_STARTED.getName();
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static ProjectStatusEnum getByCode(Integer code) {
        if (code == null) {
            return NOT_STARTED;
        }
        
        for (ProjectStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return NOT_STARTED;
    }
}
