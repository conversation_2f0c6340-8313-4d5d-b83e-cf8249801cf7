/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.xjzs.excel.SupplierInquiryRecordExcel;
import org.springblade.modules.xjzs.pojo.entity.SupplierInquiryRecordEntity;
import org.springblade.modules.xjzs.pojo.vo.SupplierInquiryRecordVO;

import java.util.List;

/**
 * 供应商询价历史记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface ISupplierInquiryRecordService extends BaseService<SupplierInquiryRecordEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param supplierInquiryRecord 查询参数
	 * @return IPage<SupplierInquiryRecordVO>
	 */
	IPage<SupplierInquiryRecordVO> selectSupplierInquiryRecordPage(IPage<SupplierInquiryRecordVO> page, SupplierInquiryRecordVO supplierInquiryRecord);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<SupplierInquiryRecordExcel>
	 */
	List<SupplierInquiryRecordExcel> exportSupplierInquiryRecord(Wrapper<SupplierInquiryRecordEntity> queryWrapper);


	/**
	 * 获取供应商询价记录分页数据（带条件查询）
	 *
	 * @param page 分页参数
	 * @param supplierInquiryRecord 查询条件
	 * @return 分页数据
	 */
	IPage<SupplierInquiryRecordVO> getSupplierInquiryRecordPage(IPage<SupplierInquiryRecordVO> page, SupplierInquiryRecordVO supplierInquiryRecord);

}
