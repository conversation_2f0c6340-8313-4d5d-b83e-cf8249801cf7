/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springblade.modules.dify.resp.BlockResponse;
import org.springblade.modules.xjzs.mapper.ProductAttributesMapper;
import org.springblade.modules.xjzs.pojo.dto.ImportErrorInfo;
import org.springblade.modules.xjzs.pojo.dto.ImportResult;
import org.springblade.modules.xjzs.pojo.entity.ProductAttributesEntity;
import org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity;
import org.springblade.modules.xjzs.pojo.vo.TrainingFeeVO;
import org.springblade.modules.xjzs.excel.TrainingFeeExcel;
import org.springblade.modules.xjzs.mapper.TrainingFeeMapper;
import org.springblade.modules.xjzs.service.IProductAttributesService;
import org.springblade.modules.xjzs.service.ITrainingFeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.dify.service.DifyService;
import org.springblade.modules.dify.resp.BlockResponse;
import org.springframework.beans.factory.annotation.Value;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springblade.core.tool.utils.StringUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * 计价标准 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class TrainingFeeServiceImpl extends BaseServiceImpl<TrainingFeeMapper, TrainingFeeEntity> implements ITrainingFeeService {
	@Autowired
	private ProductAttributesMapper productAttributesMapper;

	@Override
	public IPage<TrainingFeeVO> selectTrainingFeePage(IPage<TrainingFeeVO> page, TrainingFeeVO trainingFee) {
		return page.setRecords(baseMapper.selectTrainingFeePage(page, trainingFee));
	}


	@Override
	public List<TrainingFeeExcel> exportTrainingFee(Wrapper<TrainingFeeEntity> queryWrapper) {
		List<TrainingFeeExcel> trainingFeeList = baseMapper.exportTrainingFee(queryWrapper);
		//trainingFeeList.forEach(trainingFee -> {
		//	trainingFee.setTypeName(DictCache.getValue(DictEnum.YES_NO, TrainingFee.getType()));
		//});
		return trainingFeeList;
	}

	@Autowired
	private PriceCalculationService priceCalculationService;

	@Autowired
	private DifyService difyService;

	@Value("${dify.key.jdhc}")
	private String testKey;
	@Value("${dify.key.jdhcUnit}")
	private String unit;
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ImportResult importTrainingFee(List<TrainingFeeExcel> data, Boolean isCovered) {
//		List<TrainingFeeEntity> list = new ArrayList<>();
//		List<ImportErrorInfo> errors = new ArrayList<>();
//		int successCount = 0;
//
//		// 初始化导入处理
//
//		// 遍历Excel数据
//		for (int i = 0; i < data.size(); i++) {
//			TrainingFeeExcel trainingFeeExcel = data.get(i);
//			int rowNum = i + 2; // Excel行号从1开始，加上表头行
//
//			try {
//				// 检查必要字段是否为空
//				if (StringUtil.isBlank(trainingFeeExcel.getFeeName())) {
//					errors.add(ImportErrorInfo.of(rowNum, "商品名称", "商品名称不能为空", ""));
//					continue; // 跳过此条记录
//				}
//
//				// 检查费用编码是否为空
//				if (StringUtil.isBlank(trainingFeeExcel.getFeeCode())) {
//					errors.add(ImportErrorInfo.of(rowNum, "商品sku", "商品sku不能为空", ""));
//					continue;
//				}
//
//				// 检查品牌是否为空
//				if (StringUtil.isBlank(trainingFeeExcel.getBrand())) {
//					errors.add(ImportErrorInfo.of(rowNum, "品牌", "品牌不能为空", ""));
//					continue;
//				}
//
//				// 检查一级分类是否为空
//				if (StringUtil.isBlank(trainingFeeExcel.getFirstCategory())) {
//					errors.add(ImportErrorInfo.of(rowNum, "一级分类", "一级分类不能为空", ""));
//					continue;
//				}
//
//				// 检查协议价格格式
//				if (StringUtil.isNotBlank(trainingFeeExcel.getAgreementPrice())) {
//					try {
//						Double.parseDouble(trainingFeeExcel.getAgreementPrice());
//					} catch (NumberFormatException e) {
//						errors.add(ImportErrorInfo.of(rowNum, "协议价", "协议价格格式不正确", trainingFeeExcel.getAgreementPrice()));
//						continue;
//					}
//				}
//
//				// 检查实时价格格式
//				if (StringUtil.isNotBlank(trainingFeeExcel.getRealTimePrice())) {
//					try {
//						Double.parseDouble(trainingFeeExcel.getRealTimePrice());
//					} catch (NumberFormatException e) {
//						errors.add(ImportErrorInfo.of(rowNum, "实时价", "实时价格格式不正确", trainingFeeExcel.getRealTimePrice()));
//						continue;
//					}
//				}
//
//				TrainingFeeEntity trainingFee = new TrainingFeeEntity();
//                trainingFee.setTenantId("000000");
//                trainingFee.setIsDeleted(0);
//
//				// 1. 费用编码 (feeCode)
//				trainingFee.setFeeCode(trainingFeeExcel.getFeeCode());
//
//				// 2. 费用名称 (feeName) - 使用商品名称
//				trainingFee.setSpecification(trainingFeeExcel.getFeeName());
//
//				// 3. 费用类型 (feeType) - 固定为京东慧采
//				trainingFee.setFeeType("京东慧采");
//
//				// 4. 品牌 (brand) - 直接映射
//				trainingFee.setBrand(trainingFeeExcel.getBrand());
//
//				// 5. 一级分类 (firstCategory) - 直接映射
//				trainingFee.setFirstCategory(trainingFeeExcel.getFirstCategory());
//
//				// 6. 二级分类 (secondCategory) - 直接映射
//				trainingFee.setSecondCategory(trainingFeeExcel.getSecondCategory());
//				trainingFee.setFeeName(trainingFeeExcel.getThirdCategory());
//                if(StringUtil.isNotBlank(trainingFeeExcel.getAgreementPrice())){
//                    trainingFee.setPrice(trainingFeeExcel.getAgreementPrice());
//                }else{
//                    trainingFee.setPrice(trainingFeeExcel.getRealTimePrice());
//                }
//				list.add(trainingFee);
//				successCount++;
//			} catch (Exception e) {
//				log.error("处理Excel第" + rowNum + "行数据时发生错误", e);
//				errors.add(ImportErrorInfo.of(rowNum, "全行", "数据处理异常: " + e.getMessage(), ""));
//			}
//		}
//
//		// 如果没有有效数据，直接返回
//		if (list.isEmpty()) {
//			if (errors.isEmpty()) {
//				return ImportResult.fail("没有有效的计价标准数据需要导入");
//			} else {
//				return ImportResult.fail("导入失败，所有数据均存在错误");
//			}
//		}
//
//		try {
//			// 处理数据覆盖
//			if (isCovered) {
//				// 根据费用编码查找并更新现有记录
//				for (TrainingFeeEntity trainingFee : list) {
//					TrainingFeeEntity existingEntity = this.getOne(new LambdaQueryWrapper<TrainingFeeEntity>()
//						.eq(TrainingFeeEntity::getFeeCode, trainingFee.getFeeCode())
//						.eq(TrainingFeeEntity::getIsDeleted, 0));
//
//					if (existingEntity != null) {
//						// 设置ID以更新现有记录
//						trainingFee.setId(existingEntity.getId());
//						this.updateById(trainingFee);
//					} else {
//						this.save(trainingFee);
//					}
//				}
//			} else {
//				// 批量保存新记录
//				this.saveBatch(list);
//			}
//
//			// 导入成功后，异步处理分类和属性
//			asyncProcessCategoriesAndAttributes();
//
//			// 返回导入结果
//			if (errors.isEmpty()) {
//				return ImportResult.success(successCount);
//			} else {
//				return ImportResult.partialSuccess(successCount, errors);
//			}
//		} catch (Exception e) {
//			log.error("保存数据时发生错误", e);
//			return ImportResult.fail("保存数据时发生错误: " + e.getMessage());
//		}


		// 导入成功后，异步处理分类和属性
		asyncProcessCategoriesAndAttributes();
		return null;
	}

	@Override
	public Double calculateMaximumPrice(String categoryId, String brandId) {
		// 构建查询条件
		LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
			.eq(TrainingFeeEntity::getFeeType, "京东慧采")
			.eq(StringUtil.isNotBlank(categoryId), TrainingFeeEntity::getFirstCategory, categoryId)
			.eq(StringUtil.isNotBlank(brandId), TrainingFeeEntity::getBrand, brandId)
			.eq(TrainingFeeEntity::getIsDeleted, 0);

		// 查询符合条件的价格数据
		List<TrainingFeeEntity> entities = this.list(queryWrapper);

		if (entities == null || entities.isEmpty()) {
			log.warn("未找到符合条件的价格数据: categoryId=" + categoryId + ", brandId=" + brandId);
			return 0.0;
		}

		// 提取价格数据
		List<Double> prices = entities.stream()
			.filter(entity -> StringUtil.isNotBlank(entity.getPrice()))
			.map(entity -> {
				try {
					return Double.parseDouble(entity.getPrice());
				} catch (NumberFormatException e) {
					return null;
				}
			})
			.filter(Objects::nonNull)
			.collect(Collectors.toList());

		if (prices.isEmpty()) {
			log.warn("未找到有效的价格数据: categoryId=" + categoryId + ", brandId=" + brandId);
			return 0.0;
		}

		// 使用箱线图分析计算最高限价
		return priceCalculationService.calculateMaximumPrice(prices);
	}

	@Override
	public List<TrainingFeeEntity> searchByKeywords(String feeName, String unit, List<String> keyWords) {
		return  baseMapper.searchByKeywords(feeName,unit,keyWords);
	}

	@Override
	public Map<String, List<Map<String, String>>> getJdCategories() {
		Map<String, List<Map<String, String>>> result = new HashMap<>();

		// 查询京东慧采类型的数据
		LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
			.eq(TrainingFeeEntity::getFeeType, "京东慧采")
			.eq(TrainingFeeEntity::getIsDeleted, 0);

		List<TrainingFeeEntity> entities = this.list(queryWrapper);

		// 构建按费用名称分组的分类数据，建立对应关系
		Map<String, Map<String, Set<String>>> categoryMappings = new HashMap<>();
		for (TrainingFeeEntity entity : entities) {
			String feeName = entity.getFeeName();
			String firstCategory = entity.getFirstCategory();
			String secondCategory = entity.getSecondCategory();

			if (StringUtil.isNotBlank(feeName)) {
				categoryMappings.computeIfAbsent(feeName, k -> new HashMap<>());
				
				if (StringUtil.isNotBlank(firstCategory)) {
					categoryMappings.get(feeName)
						.computeIfAbsent("firstCategories", k -> new HashSet<>())
						.add(firstCategory);
				}
				
				if (StringUtil.isNotBlank(secondCategory)) {
					categoryMappings.get(feeName)
						.computeIfAbsent("secondCategories", k -> new HashSet<>())
						.add(secondCategory);
				}
			}
		}

		// 构建带有对应关系的费用名称列表
		List<Map<String, String>> feeNames = categoryMappings.entrySet().stream()
			.map(entry -> {
				Map<String, String> map = new HashMap<>();
				map.put("label", entry.getKey());
				map.put("value", entry.getKey());
				
				Map<String, Set<String>> categories = entry.getValue();
				if (categories.containsKey("firstCategories")) {
					map.put("firstCategories", String.join(",", categories.get("firstCategories")));
				}
				
				if (categories.containsKey("secondCategories")) {
					map.put("secondCategories", String.join(",", categories.get("secondCategories")));
				}
				
				return map;
			})
			.collect(Collectors.toList());

		// 一级分类列表（去重）
		List<Map<String, String>> firstCategories = entities.stream()
			.map(TrainingFeeEntity::getFirstCategory)
			.filter(StringUtil::isNotBlank)
			.distinct()
			.map(category -> {
				Map<String, String> map = new HashMap<>();
				map.put("label", category);
				map.put("value", category);
				return map;
			})
			.collect(Collectors.toList());

		// 二级分类列表（去重）
		List<Map<String, String>> secondCategories = entities.stream()
			.map(TrainingFeeEntity::getSecondCategory)
			.filter(StringUtil::isNotBlank)
			.distinct()
			.map(category -> {
				Map<String, String> map = new HashMap<>();
				map.put("label", category);
				map.put("value", category);
				return map;
			})
			.collect(Collectors.toList());

		result.put("feeNames", feeNames);
		result.put("firstCategories", firstCategories);
		result.put("secondCategories", secondCategories);
		
		return result;
	}

	@Override
	public  void getProductAttributes(String feeName) {
		// 根据费用名称查询对应的规格信息
		LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
				.eq(TrainingFeeEntity::getFeeName, feeName)
				.eq(TrainingFeeEntity::getFeeType, "京东慧采")
				.eq(TrainingFeeEntity::getIsDeleted, 0);

		List<TrainingFeeEntity> entities = this.list(queryWrapper);

		// 构建属性分组
		Map<String, List<String>> attributeGroups = new HashMap<>();

		int batchSize = 10; // 每批最多10条
		int totalSize = entities.size();
		log.trace("开始分析产品规格集合，共 {} 条数据，分成 {} 批"+ totalSize+ (int) Math.ceil((double) totalSize / batchSize));

		for (int i = 0; i < totalSize; i += batchSize) {
			int end = Math.min(i + batchSize, totalSize);
			List<TrainingFeeEntity> batch = entities.subList(i, end);

			// 拼接当前批次的 specification
			StringBuilder specificationsBatch = new StringBuilder();
			for (TrainingFeeEntity entity : batch) {
				if (StringUtil.isNotBlank(entity.getSpecification())) {
					specificationsBatch.append(entity.getSpecification()).append("\n");
				}
			}

			if (specificationsBatch.length() == 0) {
				log.warn("第 {} 批次无有效规格内容"+ i / batchSize + 1);
				continue;
			}

			log.trace("正在发送第 {} 批次规格信息，内容长度: {}"+ i / batchSize + 1+ specificationsBatch.length());

			try {
				// 调用Dify API进行规格分析
				BlockResponse response = difyService.blockingMessage(
						specificationsBatch.toString(),
						0L,
						testKey,
						null
				);

				if (response != null && StringUtil.isNotBlank(response.getAnswer())) {
					String jsonStr = response.getAnswer();

					// 移除思考标签
					if (jsonStr.contains("</think>")) {
						int thinkStart = jsonStr.indexOf("</think>");
						if (thinkStart >= 0) {
							int startIndex = jsonStr.indexOf("{", thinkStart);
							int endIndex = jsonStr.lastIndexOf("}") + 1;
							if (startIndex >= 0 && endIndex > startIndex) {
								jsonStr = jsonStr.substring(startIndex, endIndex);
							}
						}
					} else if (!jsonStr.startsWith("{")) {
						int startIndex = jsonStr.indexOf("{");
						int endIndex = jsonStr.lastIndexOf("}") + 1;
						if (startIndex >= 0 && endIndex > startIndex) {
							jsonStr = jsonStr.substring(startIndex, endIndex);
						}
					}

					JSONObject jsonObject = JSON.parseObject(jsonStr);

					for (String key : jsonObject.keySet()) {
						List<String> values = new ArrayList<>();
						Object valuesObj = jsonObject.get(key);

						if (valuesObj instanceof List) {
							((List<?>) valuesObj).forEach(v -> values.add(v.toString()));
						} else if (valuesObj instanceof String) {
							String[] valueArray = valuesObj.toString().split(",");
							for (String value : valueArray) {
								values.add(value.trim());
							}
						}

						attributeGroups.computeIfAbsent(key, k -> new ArrayList<>()).addAll(values);
					}
					// 保存到数据库

				} else {
					log.trace("Dify API返回结果为空或无效，批次: {}"+ i / batchSize + 1);
				}

			} catch (Exception e) {
				log.error("调用Dify API失败，批次: {}"+ i / batchSize + 1, e);
			}
		}

		// 去重并整理结果
		List<Map<String, Object>> result = new ArrayList<>();

		for (Map.Entry<String, List<String>> entry : attributeGroups.entrySet()) {
			String key = entry.getKey();
			List<String> values = entry.getValue().stream().distinct().toList();

			Map<String, Object> group = new HashMap<>();
			group.put("name", key);
			group.put("expanded", false);

			List<Map<String, Object>> options = values.stream()
					.map(value -> {
						Map<String, Object> option = new HashMap<>();
						option.put("label", value);
						option.put("value", value);
						return option;
					})
					.toList();

			group.put("options", options);
			result.add(group);
		}
		saveProductAttributes(feeName, result);
	}


	/**
	 * 异步处理分类和属性
	 * 在导入数据成功后，启动新线程统计分类并获取商品属性
	 */
	private void asyncProcessCategoriesAndAttributes() {
		Thread thread = new Thread(() -> {
			try {
				log.debug("开始异步处理商品分类和属性");
				// 获取所有京东慧采分类
//				Map<String, List<Map<String, String>>> categories = getJdCategories();
//
//				// 获取所有费用名称
//				List<Map<String, String>> feeNames = categories.get("feeNames");
//				if (feeNames != null && !feeNames.isEmpty()) {
//					for (Map<String, String> feeNameMap : feeNames) {
//						String feeName = feeNameMap.get("value");
//						if (StringUtil.isNotBlank(feeName)) {
//							try {
//								log.debug("开始处理费用名称: {}"+ feeName);
//								// 获取商品属性
//								getProductAttributes(feeName);
//
//							} catch (Exception e) {
//								log.error("处理费用名称 {} 时发生错误"+ feeName+ e);
//							}
//						}
//					}
//				}
				log.debug("异步处理商品分类和属性完成");
				//更新商品单位
				getProductUnit();
				log.debug("异步处理商品单位完成");
			} catch (Exception e) {
				log.error("异步处理商品分类和属性时发生错误", e);
			}
		});

		thread.setName("ProcessCategoriesAndAttributes");
		thread.start();
	}

	/**
	 * 保存商品属性到数据库
	 *
	 * @param feeName 费用名称
	 * @param attributeGroups 属性组列表
	 */
	private void saveProductAttributes(String feeName, List<Map<String, Object>> attributeGroups) {
		if (attributeGroups == null || attributeGroups.isEmpty()) {
			log.warn("费用名称 {} 没有属性数据"+feeName);
			return;
		}

		try {
			for (Map<String, Object> group : attributeGroups) {
				String attributeName = (String) group.get("name");
				if (StringUtil.isBlank(attributeName)) {
					continue;
				}
				List<Map<String, Object>> options = (List<Map<String, Object>>) group.get("options");
				if (options != null && !options.isEmpty()) {
					for (Map<String, Object> option : options) {
						String attributeValue = (String) option.get("value");
						if (StringUtil.isNotBlank(attributeValue)) {
							ProductAttributesEntity  entity=new  ProductAttributesEntity();
							entity.setFeeName(feeName);
							entity.setAttributeName(attributeName);
							entity.setAttributeValue(attributeValue);
							entity.setIsDeleted(0);
							entity.setTenantId("000000");
							productAttributesMapper.insert(entity);
						}
					}
				}
			}
			log.debug("费用名称 {} 的属性数据保存成功"+ feeName);
		} catch (Exception e) {
			log.error("保存费用名称 {} 的属性数据时发生错误"+ feeName, e);
		}
	}

	@Override
	public Map<String, List<Map<String, String>>> getTrainingCategories() {
		Map<String, List<Map<String, String>>> result = new HashMap<>();

		// 查询培训类型的数据
		LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
			.eq(TrainingFeeEntity::getFeeType, "培训类")
			.eq(TrainingFeeEntity::getIsDeleted, 0);

		List<TrainingFeeEntity> entities = this.list(queryWrapper);

		// 费用名称列表（去重）
		List<Map<String, String>> feeNames = entities.stream()
			.map(TrainingFeeEntity::getFeeName)
			.distinct()
			.filter(StringUtil::isNotBlank)
			.map(name -> {
				Map<String, String> map = new HashMap<>();
				map.put("label", name);
				map.put("value", name);
				return map;
			})
			.collect(Collectors.toList());
		result.put("feeNames", feeNames);

		// 一级分类列表（去重）
		List<Map<String, String>> firstCategories = entities.stream()
			.map(TrainingFeeEntity::getFirstCategory)
			.distinct()
			.filter(StringUtil::isNotBlank)
			.map(category -> {
				Map<String, String> map = new HashMap<>();
				map.put("label", category);
				map.put("value", category);
				return map;
			})
			.collect(Collectors.toList());
		result.put("firstCategories", firstCategories);

		// 二级分类列表（去重）
		List<Map<String, String>> secondCategories = entities.stream()
			.map(TrainingFeeEntity::getSecondCategory)
			.distinct()
			.filter(StringUtil::isNotBlank)
			.map(category -> {
				Map<String, String> map = new HashMap<>();
				map.put("label", category);
				map.put("value", category);
				return map;
			})
			.collect(Collectors.toList());
		result.put("secondCategories", secondCategories);

		// 师资职称列表（从费用名称中提取，因为在培训类中，费用名称通常是职称）
		List<Map<String, String>> teacherTitles = feeNames;
		result.put("teacherTitles", teacherTitles);

		return result;
	}

	@Override
	public Map<String, Object> getTrainingAttributes(String feeName) {
		Map<String, Object> result = new HashMap<>();

		// 查询指定费用名称的培训类数据
		LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
			.eq(TrainingFeeEntity::getFeeType, "培训类")
			.eq(TrainingFeeEntity::getFeeName, feeName)
			.eq(TrainingFeeEntity::getIsDeleted, 0);

		TrainingFeeEntity entity = this.getOne(queryWrapper);

		if (entity != null) {
			// 设置师资职称（在培训类中，费用名称通常是职称）
			result.put("teacherTitle", entity.getFeeName());

			// 设置价格
			result.put("price", entity.getPrice());

			// 设置一级分类和二级分类
			if (StringUtil.isNotBlank(entity.getFirstCategory())) {
				result.put("firstCategory", entity.getFirstCategory());
			}

			if (StringUtil.isNotBlank(entity.getSecondCategory())) {
				result.put("secondCategory", entity.getSecondCategory());
			}
		}

		return result;
	}
	private void updateBatchWithUnitFromList(List<TrainingFeeEntity> list, List<TrainingFeeEntity> batch) {
		// 将 list 转换为以 id 为键的 Map，便于快速查找
		Map<Long, TrainingFeeEntity> listMap = list.stream()
				.collect(Collectors.toMap(TrainingFeeEntity::getId, entity -> entity));

		// 遍历 batch，查找 id 相同的对象并更新 unit
		for (TrainingFeeEntity batchEntity : batch) {
			TrainingFeeEntity listEntity = listMap.get(batchEntity.getId());
			if (listEntity != null) {
				batchEntity.setUnit(listEntity.getUnit()); // 更新 unit 属性
			}
		}
	}
	/**
	 * 通过调用大模型接口，获取商品单位
	 */
	public  void getProductUnit() {
		// 根据费用名称查询对应的规格信息
		LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
				.eq(TrainingFeeEntity::getFeeType, "京东慧采")
				.isNull(TrainingFeeEntity::getUnit)
				.eq(TrainingFeeEntity::getIsDeleted, 0);

		List<TrainingFeeEntity> entities = this.list(queryWrapper);

		// 构建属性分组
		Map<String, List<String>> attributeGroups = new HashMap<>();

		int batchSize = 10; // 每批最多10条
		int totalSize = entities.size();
		log.trace("开始分析产品规格集合，共 {} 条数据，分成 {} 批"+ totalSize+ (int) Math.ceil((double) totalSize / batchSize));

		for (int i = 0; i < totalSize; i += batchSize) {
			int end = Math.min(i + batchSize, totalSize);
			List<TrainingFeeEntity> batch = entities.subList(i, end);

			// 拼接当前批次的 specification
			StringBuilder specificationsBatch = new StringBuilder();
			for (TrainingFeeEntity entity : batch) {
				if (StringUtil.isNotBlank(entity.getSpecification())) {
					specificationsBatch.append(entity.getId()).append(",");
					specificationsBatch.append(entity.getSpecification()).append(";\n");
				}
			}

			if (specificationsBatch.length() == 0) {
				log.warn("第 {} 批次无有效规格内容"+ i / batchSize + 1);
				continue;
			}

			log.trace("正在发送第 {} 批次规格信息，内容长度: {}"+ i / batchSize + 1+ specificationsBatch.length());

			try {
				// 调用Dify API进行规格分析
				BlockResponse response = difyService.blockingMessage(
						specificationsBatch.toString(),
						0L,
						unit,
						null
				);

				if (response != null && StringUtil.isNotBlank(response.getAnswer())) {
					String jsonStr = response.getAnswer();

					// 移除思考标签
					if (jsonStr.contains("</think>")) {
						int thinkStart = jsonStr.indexOf("</think>");
						if (thinkStart >= 0) {
							int startIndex = jsonStr.indexOf("[", thinkStart);
							int endIndex = jsonStr.lastIndexOf("]") + 1;
							if (startIndex >= 0 && endIndex > startIndex) {
								jsonStr = jsonStr.substring(startIndex, endIndex);
							}
						}
					} else if (!jsonStr.startsWith("[")) {
						int startIndex = jsonStr.indexOf("[");
						int endIndex = jsonStr.lastIndexOf("]") + 1;
						if (startIndex >= 0 && endIndex > startIndex) {
							jsonStr = jsonStr.substring(startIndex, endIndex);
						}
					}

					List<TrainingFeeEntity> list = JSONArray.parseArray(jsonStr, TrainingFeeEntity.class);
					updateBatchWithUnitFromList(list, batch);
					this.updateBatchById(batch);
					// 保存到数据库

				} else {
					log.trace("Dify API返回结果为空或无效，批次: {}"+ i / batchSize + 1);
				}

			} catch (Exception e) {
				log.error("调用Dify API失败，批次: {}"+ i / batchSize + 1, e);
			}
		}



	}

	public Map<String, List<Map<String, String>>> getJdUnit(String feeName) {
		Map<String, List<Map<String, String>>> result = new HashMap<>();

		// 查询京东慧采类型的数据
		LambdaQueryWrapper<TrainingFeeEntity> queryWrapper = new LambdaQueryWrapper<TrainingFeeEntity>()
				.eq(TrainingFeeEntity::getFeeType, "京东慧采")
				.eq(TrainingFeeEntity::getFeeName, feeName)
				.eq(TrainingFeeEntity::getIsDeleted, 0);

		List<TrainingFeeEntity> entities = this.list(queryWrapper);

		// 费用名称列表（去重）
		List<Map<String, String>> feeNames = entities.stream()
				.map(TrainingFeeEntity::getFeeName)
				.distinct()
				.map(name -> {
					Map<String, String> map = new HashMap<>();
					map.put("label", name);
					map.put("value", name);
					return map;
				})
				.collect(Collectors.toList());


		result.put("feeNames", feeNames);
		return result;
	}
	@Override
	public List<TrainingFeeVO> getDisUnit(String feeName) {
		return baseMapper.getDisUnit(feeName);
	}

}
