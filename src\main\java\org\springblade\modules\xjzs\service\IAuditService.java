package org.springblade.modules.xjzs.service;

import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;

/**
 * 审计服务接口
 */
public interface IAuditService {
    
    /**
     * 执行审计计算
     * 
     * @param params 审计参数
     * @return 审计结果
     */
    AuditResult calculate(AuditParams params);
    
    /**
     * 获取服务类型
     * 
     * @return 服务类型名称
     */
    String getServiceType();
}
