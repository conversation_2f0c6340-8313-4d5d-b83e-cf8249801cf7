<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.TrainingFeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="trainingFeeResultMap" type="org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="fee_code" property="feeCode"/>
        <result column="fee_name" property="feeName"/>
        <result column="fee_type" property="feeType"/>
        <result column="brand" property="brand"/>
        <result column="first_category" property="firstCategory"/>
        <result column="second_category" property="secondCategory"/>
        <result column="third_category" property="thirdCategory"/>
        <result column="specification" property="specification"/>
        <result column="agreement_price" property="agreementPrice"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="real_time_price" property="realTimePrice"/>
        <result column="unit" property="unit"/>

    </resultMap>


    <select id="selectTrainingFeePage" resultMap="trainingFeeResultMap">
        select * from xjzs_training_fee where is_deleted = 0
    </select>


    <select id="exportTrainingFee" resultType="org.springblade.modules.xjzs.excel.TrainingFeeExcel">
        SELECT * FROM xjzs_training_fee ${ew.customSqlSegment}
    </select>
    <select id="getDisUnit" resultType="org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity">
        select distinct  unit from xjzs_training_fee where  fee_name= '${feeName}' and  unit is not null
        and fee_type = '京东慧采'
        group by unit
    </select>

    <select id="getProductAttributes" resultType="org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity">
        select distinct  unit from xjzs_training_fee where  fee_name= '${feeName}' and  unit is not null
        and fee_type = '京东慧采'
        group by unit
    </select>

    <select id="searchByKeywords"  resultType="org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity">
        SELECT *
        FROM xjzs_training_fee
        WHERE   fee_name= '${feeName}' AND  unit= '${unit}'  AND
        <foreach collection="keyWords" item="keyWord" separator=" AND ">
            specification LIKE CONCAT('%', #{keyWord}, '%')
        </foreach>
    </select>


</mapper>

