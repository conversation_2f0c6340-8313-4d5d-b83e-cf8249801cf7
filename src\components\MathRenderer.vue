<template>
  <div class="math-renderer" v-html="renderedContent"></div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import katex from 'katex';
import 'katex/dist/katex.min.css';

const props = defineProps({
  content: {
    type: String,
    default: ''
  }
});

const renderedContent = computed(() => {
  if (!props.content) return '';
  
  let content = props.content;
  
  try {
    // 渲染块级数学公式 \[ ... \]
    content = content.replace(/\\\[(.*?)\\\]/gs, (match, formula) => {
      try {
        return katex.renderToString(formula, {
          displayMode: true,
          throwOnError: false,
          strict: false,
          trust: true
        });
      } catch (e) {
        console.warn('块级公式渲染失败:', e.message);
        return match; // 如果渲染失败，返回原文
      }
    });

    // 渲染行内数学公式 \( ... \) 或 $ ... $
    content = content.replace(/\\\((.*?)\\\)/gs, (match, formula) => {
      try {
        return katex.renderToString(formula, {
          displayMode: false,
          throwOnError: false,
          strict: false,
          trust: true
        });
      } catch (e) {
        console.warn('行内公式渲染失败:', e.message);
        return match;
      }
    });
    
    // 简单的LaTeX符号替换（作为备选方案）
    content = content.replace(/\\times/g, '×');
    content = content.replace(/\\,/g, ' ');
    content = content.replace(/\\cdot/g, '·');
    content = content.replace(/\\div/g, '÷');
    content = content.replace(/\\pm/g, '±');
    content = content.replace(/\\leq/g, '≤');
    content = content.replace(/\\geq/g, '≥');
    content = content.replace(/\\neq/g, '≠');
    content = content.replace(/\\approx/g, '≈');
    
    // 处理换行
    content = content.replace(/\n/g, '<br>');
    
    return content;
  } catch (error) {
    console.error('数学公式渲染失败:', error);
    return props.content;
  }
});
</script>

<style scoped>
.math-renderer {
  line-height: 1.6;
  word-wrap: break-word;
}

/* KaTeX 样式调整 */
.math-renderer :deep(.katex-display) {
  margin: 1em 0;
  text-align: center;
}

.math-renderer :deep(.katex) {
  font-size: 1.1em;
}
</style>
