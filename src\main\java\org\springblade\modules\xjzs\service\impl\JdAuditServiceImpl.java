package org.springblade.modules.xjzs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.pojo.entity.TrainingFeeEntity;
import org.springblade.modules.xjzs.pojo.vo.TrainingFeeVO;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springblade.modules.xjzs.service.ITrainingFeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 京东慧采类审计服务实现
 */
@Service
@Slf4j
public class JdAuditServiceImpl implements IAuditService {
    
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ITrainingFeeService trainingFeeService;
    @Autowired
    private PriceCalculationService priceCalculationService;
    @Override
    public String getServiceType() {
        return "京东慧采";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        try {
            log.info("开始计算京东慧采类审计费用: {}", params);
            
            // 创建计算过程记录列表
            List<String> calculationProcess = new ArrayList<>();
            calculationProcess.add("开始计算京东慧采类最高限价");
            
            // 解析标准表数据
            List<Map<String, Object>> tableData = null;
            if (StringUtil.isNotBlank(params.getTableData())) {
                tableData = objectMapper.readValue(params.getTableData(), new TypeReference<List<Map<String, Object>>>() {});
                calculationProcess.add("成功解析标准表数据，共" + tableData.size() + "条记录");
            }
            
            if (tableData == null || tableData.isEmpty()) {
                log.warn("京东慧采类标准表数据为空");
                return AuditResult.fail("京东慧采类标准表数据不能为空");
            }
            
            // 创建计算结果
            AuditResult result = AuditResult.success(BigDecimal.ZERO);
            BigDecimal totalCost = BigDecimal.ZERO;
            
            // 遍历每一行标准表数据进行计算
            for (int i = 0; i < tableData.size(); i++) {
                Map<String, Object> row = tableData.get(i);
                calculationProcess.add("开始计算第" + (i + 1) + "行数据");
                
                // 获取费用名称
                String feeName = (String) row.get("feeName");
                calculationProcess.add("商品名称: " + feeName);
                
                // 单位
                String unit = (String) row.get("unit");
                calculationProcess.add("单位: " + unit);
                
                // 获取商品属性
                List<List<String>> keyStr = (List<List<String>>)row.get("attributes");
                if(keyStr.isEmpty()){
                    String errorMsg = "请输入商品属性";
                    log.error(errorMsg);
                    calculationProcess.add("错误: " + errorMsg);
                    throw new Exception(errorMsg);
                }
                
                // 存储结果的列表
                List<String> keyWords = new ArrayList<>();

                // 遍历每个子列表并获取第二个元素
                for (List<String> subList : keyStr) {
                    if (subList.size() >= 2) {
                        keyWords.add(subList.get(1));
                    }
                }
                
                calculationProcess.add("商品属性关键词: " + String.join(", ", keyWords));
                
                // 获取数量
                Integer quantity = getIntValue(row, "quantity", 1);
                calculationProcess.add("数量: " + quantity);

                // 计算单价
                calculationProcess.add("开始计算最高限价...");
                double maxPrice = calculateMaximumPrice(feeName, unit, keyWords);
                BigDecimal unitPrice = BigDecimal.valueOf(maxPrice).setScale(2, RoundingMode.HALF_UP);
                calculationProcess.add("计算得出最高限价: " + unitPrice + "元/" + unit);

                // 计算该行的费用
                BigDecimal rowFee = unitPrice.multiply(new BigDecimal(quantity)).setScale(2, RoundingMode.HALF_UP);
                calculationProcess.add("该行总费用 = 单价 × 数量 = " + unitPrice + " × " + quantity + " = " + rowFee + "元");
                
                // 添加计算步骤
                Map<String, Object> detail = new HashMap<>();
                detail.put("feeName", feeName);
                detail.put("quantity", quantity);
                detail.put("unitPrice", unitPrice);
                detail.put("amount", rowFee);
                detail.put("description", feeName + ": " + unitPrice + "元/" + unit + " × " + quantity + " = " + rowFee + "元");
                result.addDetail(detail);
                
                totalCost = totalCost.add(rowFee);
            }
            
            calculationProcess.add("所有商品费用合计: " + totalCost + "元");
            
            // 应用成本核算法的调整系数
            if ("cost".equals(params.getCalculationMethod())) {
                // 成本核算法调整系数

                // 添加调整步骤
                Map<String, Object> adjustmentDetail = new HashMap<>();
                adjustmentDetail.put("description", "成本核算法调整");
                adjustmentDetail.put("originalCost", totalCost);
                result.addDetail(adjustmentDetail);
            }
            
            calculationProcess.add("最终计算结果: " + totalCost + "元");
            log.info("京东慧采类审计费用计算完成，总费用: {}", totalCost);
            
            result.setTotalCost(totalCost);
            result.setCalculationProcess(calculationProcess);
            return result;
        } catch (Exception e) {
            log.error("计算京东慧采类审计费用时发生错误", e);
            return AuditResult.fail("计算错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取整数值
     */
    private Integer getIntValue(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value == null) {
            return defaultValue;
        }
        
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        return defaultValue;
    }
    
    /**
     * 获取商品属性
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getAttributes(Map<String, Object> row) {
        Object attributes = row.get("attributes");
        if (attributes == null) {
            return new ArrayList<>();
        }
        
        if (attributes instanceof List) {
            return (List<Map<String, Object>>) attributes;
        } else if (attributes instanceof String) {
            try {
                return objectMapper.readValue((String) attributes, new TypeReference<List<Map<String, Object>>>() {});
            } catch (Exception e) {
                log.warn("解析商品属性失败: {}", attributes);
                return new ArrayList<>();
            }
        }
        
        return new ArrayList<>();
    }
    
    public Double calculateMaximumPrice(String feeName, String unit, List<String> keyWord) {
        // 构建查询条件
        log.info("查询价格数据: feeName={}, unit={}, keyWords={}", feeName, unit, keyWord);

        // 查询符合条件的价格数据
        List<TrainingFeeEntity> entities = trainingFeeService.searchByKeywords(feeName, unit, keyWord);

        if (entities == null || entities.isEmpty()) {
            log.warn("未找到符合条件的价格数据: feeName=" + feeName + ", unit=" + unit);
            return 0.0;
        }

        log.info("找到{}条符合条件的价格数据", entities.size());

        // 提取价格数据
        List<Double> prices = entities.stream()
                .filter(Objects::nonNull)  // 先过滤null对象
                .filter(entity -> {
                    try {
                        String price = entity.getPrice();
                        return price != null && !price.trim().isEmpty();
                    } catch (Exception e) {
                        return false;
                    }
                })
                .map(entity -> {
                    try {
                        return Double.valueOf(entity.getPrice().trim());
                    } catch (NumberFormatException e) {
                        return Double.NaN;  // 使用NaN代替null
                    }
                })
                .filter(d -> !Double.isNaN(d))  // 过滤掉NaN值
                .collect(Collectors.toList());

        if (prices.isEmpty()) {
            log.warn("未找到有效的价格数据: feeName=" + feeName + ", unit=" + unit);
            return 0.0;
        }

        log.info("提取到{}个有效价格数据", prices.size());

        // 使用箱线图分析计算最高限价
        return priceCalculationService.calculateMaximumPrice(prices);
    }
}
