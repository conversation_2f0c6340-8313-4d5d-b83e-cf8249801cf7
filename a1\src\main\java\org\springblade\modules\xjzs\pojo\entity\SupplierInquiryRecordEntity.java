/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 供应商询价历史记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@TableName("xjzs_supplier_inquiry_record")
@Schema(description = "SupplierInquiryRecord对象")
@EqualsAndHashCode(callSuper = true)
public class SupplierInquiryRecordEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 项目编码
	 */
	@Schema(description = "项目编码")
	private String projectId;
	/**
	 * 询价时间
	 */
	@Schema(description = "询价时间")
	private Date inquiryTime;
	/**
	 * 名称
	 */
	@Schema(description = "名称")
	private String name;
	/**
	 * 内容
	 */
	@Schema(description = "内容")
	private String content;
	/**
	 * 数量
	 */
	@Schema(description = "数量")
	private Long quantity;
	/**
	 * 询价供应商
	 */
	@Schema(description = "询价供应商")
	private String supplier;
	/**
	 * 询价渠道
	 */
	@Schema(description = "询价渠道")
	private String inquiryChannel;
	/**
	 * 单价
	 */
	@Schema(description = "单价")
	private Long unitPrice;
	/**
	 * 总价
	 */
	@Schema(description = "总价")
	private Long totalPrice;
	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

}
