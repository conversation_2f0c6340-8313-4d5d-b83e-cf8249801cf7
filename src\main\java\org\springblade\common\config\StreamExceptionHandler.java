package org.springblade.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.dify.resp.StreamResponse;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Flux;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 流式API专用异常处理器
 * 用于处理SSE流式响应中的异常，避免返回R类型导致的消息转换错误
 */
@Slf4j
@RestControllerAdvice
@Order(1) // 优先级高于全局异常处理器
public class StreamExceptionHandler {

    /**
     * 处理流式API中的异常
     * 只处理Content-Type为text/event-stream的请求
     */
    @ExceptionHandler(Exception.class)
    public Flux<StreamResponse> handleStreamException(Exception e, HttpServletRequest request) {
        // 只处理SSE流式请求
        String contentType = request.getHeader("Accept");
        if (contentType != null && contentType.contains(MediaType.TEXT_EVENT_STREAM_VALUE)) {
            log.error("流式API异常: {}", e.getMessage(), e);
            
            // 返回错误格式的StreamResponse
            StreamResponse errorResponse = new StreamResponse();
            errorResponse.setEvent("error");
            errorResponse.setAnswer("服务暂时不可用，请稍后重试。错误信息: " + e.getMessage());
            errorResponse.setCreated_at(System.currentTimeMillis());
            
            return Flux.just(errorResponse);
        }
        
        // 非流式请求，抛出异常让其他异常处理器处理
        throw new RuntimeException(e);
    }
}
