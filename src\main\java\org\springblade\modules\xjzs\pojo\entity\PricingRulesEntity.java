/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serial;

/**
 * 计价规则表 实体类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@TableName("xjzs_pricing_rules")
@Schema(description = "PricingRules对象")
@EqualsAndHashCode(callSuper = true)
public class PricingRulesEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 规则名称
	 */
	@Schema(description = "规则名称")
	private String ruleName;

	/**
	 * 规则类型
	 */
	@Schema(description = "规则类型")
	private String ruleType;

	/**
	 * 计算公式
	 */
	@Schema(description = "计算公式")
	private String calculationFormula;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

}
