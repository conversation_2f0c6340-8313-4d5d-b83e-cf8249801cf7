<template>
  <basic-container>
    <div class="split-layout-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>编制项目最高限价</h2>
      </div>

      <!-- 左右分屏布局 -->
      <div class="split-layout">
        <!-- 左侧操作区域 -->
        <div class="left-panel">
          <div class="panel-content">
            <!-- 项目选择 -->
            <div class="step-section">
              <h3 class="step-title">
                <i class="el-icon-folder-opened"></i>
                项目信息
                <el-tag v-if="formData.selectedProject" type="success" size="mini">已完成</el-tag>
                <el-tag v-else type="warning" size="mini">必须选择</el-tag>
              </h3>
              <p class="step-description">请选择需要编制最高限价的项目，此步骤为必选项</p>
              <ProjectSelectStepTabs
                :formData="formData"
                :isReportGenerated="isReportGenerated"
                @next-step="handleNextStep(0)" />
            </div>

            <!-- 待办清单 -->
            <div class="step-section" :class="{ 'disabled': !formData.selectedProject }">
              <h3 class="step-title">
                <i class="el-icon-document-checked"></i>
                待办清单
                <el-tag v-if="formData.selectedProject" type="success" size="mini">已准备</el-tag>
                <el-tag v-else type="info" size="mini">等待中</el-tag>
              </h3>
              <p class="step-description">项目信息和配置准备</p>
              <div v-if="formData.selectedProject">
                <TodoListStep
                  :formData="formData"
                  @next-step="handleNextStep(1)"
                  @prev-step="handlePrevStep(1)"
                />
              </div>
              <div v-else>
                <p class="placeholder-text">请先完成项目选择</p>
              </div>
            </div>

            <!-- 引擎计算 - 直接追加内容 -->
            <div v-if="currentStep === 'step3' && formData.selectedProject">
              <EngineCalculationStepTabs
                :formData="formData"
                @next-step="handleNextStep(2)"
                @calculation-complete="handleCalculationComplete"
              />
            </div>
            <div v-else-if="formData.selectedProject">
              <p>项目已选择，请点击"开始生成"按钮开始计算</p>
            </div>
            <div v-else>
              <p>请先完成项目选择</p>
            </div>


          </div>
        </div>

        <!-- 右侧报告展示区域 -->
        <div class="right-panel">


          <div class="panel-content">
            <!-- 报告内容区域 -->
            <div v-if="isReportGenerated" class="report-preview">
              <!-- 使用 GenerateReportStep 组件来显示完整报告 -->
              <GenerateReportStep
                :formData="formData"
                :isDetailMode="isDetailMode"
                :showOnlyReport="true"
                @prev-step="handlePrevStep"
                @finish="finishProcess"
                @report-generated="handleReportGenerated"
              />
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-report">
              <el-empty description="暂无报告数据">
                <template #image>
                  <i class="el-icon-document" style="font-size: 60px; color: #c0c4cc;"></i>
                </template>
                <template #description>
                  <p>请完成左侧所有步骤后生成报告</p>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </basic-container>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import ProjectSelectStep from './components/ProjectSelectStep.vue';
import ProjectSelectStepTabs from './components/ProjectSelectStepTabs.vue';
import TodoListStep from './components/TodoListStep.vue';
import EngineCalculationStepTabs from './components/EngineCalculationStepTabs.vue';
import GenerateReportStep from './components/GenerateReportStep.vue';
import { getDetail } from '@/api/xjzs/project';
import { getProjectReport } from '@/api/xjzs/projectReport';

export default {
  name: 'ProjectAssistantTabs',
  components: {
    ProjectSelectStep,
    ProjectSelectStepTabs,
    TodoListStep,
    EngineCalculationStepTabs,
    GenerateReportStep
  },
  setup() {
    const currentStep = ref('step1');
    const route = useRoute();
    const store = useStore();
    const isDetailMode = ref(false);
    const isReportGenerated = ref(false);
    const isGeneratingReport = ref(false);

    const formData = reactive({
      selectedProject: '',
      projectName: '',
      id: '',
      projectCode: '',
      projectType: '',
      projectCategory: '',
      projectDescription: '',
      projectFiles: [],
      procurementMethod: '',
      algorithmCategory: '',
      calculationMethod: '',
      standardTableRows: [],
      engineeringTableRows: [],
      trainingTableRows: [],
      calculationResult: null,
      reportData: null
    });



    // 格式化日期
    const formatDate = (date) => {
      return new Date(date).toLocaleString('zh-CN');
    };

    // 格式化价格
    const formatPrice = (price) => {
      if (!price) return '0.00';
      return parseFloat(price).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    };

    // 格式化计算过程
    const formatCalculationProcess = (process) => {
      if (!process) return '';

      let content = '';
      if (Array.isArray(process)) {
        content = process.join('\n');
      } else {
        content = process.toString();
      }

      // 简单的格式化，保持AI风格
      return content
        .replace(/\*\*(.*?)\*\*/g, '<strong style="color: #409EFF;">$1</strong>')
        .replace(/\n/g, '<br>')
        .replace(/(\d+\.?\d*)\s*元/g, '<span style="color: #E6A23C; font-weight: 600;">$1元</span>')
        .replace(/(计算|分析|结果)/g, '<span style="color: #67C23A;">$1</span>');
    };

    // 生成报告
    const handleGenerateReport = async () => {
      if (!formData.calculationResult) {
        ElMessage.warning('请先完成引擎计算');
        return;
      }

      isGeneratingReport.value = true;
      try {
        // 模拟报告生成过程
        await new Promise(resolve => setTimeout(resolve, 2000));
        isReportGenerated.value = true;
        ElMessage.success('报告生成成功！');
      } catch (error) {
        ElMessage.error('报告生成失败：' + error.message);
      } finally {
        isGeneratingReport.value = false;
      }
    };

    // 导出报告
    const exportReport = () => {
      ElMessage.success('报告导出功能开发中...');
    };

    // 刷新报告
    const refreshReport = () => {
      ElMessage.info('报告已刷新');
    };

    // 打印报告
    const printReport = () => {
      window.print();
    };

    // 处理下一步
    const handleNextStep = async (stepIndex) => {
      if (stepIndex === 0 && !formData.selectedProject) {
        ElMessage.warning('请选择一个项目');
        return;
      }

      if (stepIndex === 0) {
        // 第一步：开始生成 - 自动执行完整流程
        console.log('🎯 开始第一步：开始生成');
        setCalculationMethod();
        currentStep.value = 'step2';
        ElMessage.success('开始生成，正在准备计算...');

        // 短暂延迟后自动进入计算步骤
        setTimeout(async () => {
          console.log('🎯 进入第三步：引擎计算');
          currentStep.value = 'step3';
          ElMessage.success('正在进行AI智能计算...');

          // 设置自动启动标志，等待组件渲染后自动开始计算
          setTimeout(() => {
            console.log('🎯 设置自动启动标志');
            formData.autoStartCalculation = true;
            console.log('📊 autoStartCalculation 已设置为:', formData.autoStartCalculation);
          }, 500);
        }, 1000);

      } else if (stepIndex === 1) {
        currentStep.value = 'step3';
        ElMessage.success('已进入引擎计算步骤');
      } else if (stepIndex === 2) {
        // 引擎计算完成，自动生成报告
        ElMessage.success('引擎计算完成，正在生成报告...');
        await handleGenerateReport();
      }
    };

    // 处理上一步
    const handlePrevStep = () => {
      if (currentStep.value === 'step3') {
        currentStep.value = 'step2';
      } else if (currentStep.value === 'step2') {
        currentStep.value = 'step1';
      }
    };

    const finishProcess = () => {
      ElMessage.success('项目最高限价编制完成！');
      currentStep.value = 'step1';
    };

    // 处理报告生成完成事件
    const handleReportGenerated = () => {
      isReportGenerated.value = true;
    };

    // 处理计算完成事件
    const handleCalculationComplete = (result) => {
      console.log('计算完成:', result);
      formData.calculationResult = result;
    };



    // 根据算法类型自动设置计算方式
    const setCalculationMethod = () => {
      const algorithmCategory = formData.algorithmCategory;

      if (algorithmCategory === '培训类' || algorithmCategory === '工程咨询类') {
        formData.calculationMethod = 'government';
        ElMessage.success(`已根据项目类型"${algorithmCategory}"自动选择"政府、行业指导价格法"`);
      } else if (algorithmCategory) {
        formData.calculationMethod = 'cost';
        ElMessage.success(`已根据项目类型自动选择"成本核算法"`);
      }
    };

    // 监听算法类型变化，自动设置计算方式
    watch(() => formData.algorithmCategory, (newVal) => {
      if (newVal && !formData.calculationMethod) {
        setCalculationMethod();
      }
    });

    onMounted(async () => {
      // 进入页面时自动收起左侧菜单
      const isCollapse = store.getters.isCollapse;
      if (!isCollapse) {
        store.commit('SET_COLLAPSE');
      }

      // 检查URL参数
      const { projectId, step, mode } = route.query;
      
      if (projectId) {
        formData.id = projectId;
        formData.selectedProject = projectId;
        
        if (mode === 'detail') {
          isDetailMode.value = true;
          
          try {
            const projectRes = await getDetail(projectId);
            if (projectRes.data && projectRes.data.success) {
              const projectData = projectRes.data.data;
              
              formData.projectName = projectData.name;
              formData.projectCode = projectData.code || `P${projectData.id}`;
              formData.projectType = projectData.type || '';
              formData.projectCategory = projectData.category || '';
              formData.projectDescription = projectData.content || '';
              formData.procurementMethod = projectData.procurementMethod || '';
              formData.algorithmCategory = projectData.algorithmCategory || '';
              formData.id = projectData.id;
              
              const reportRes = await getProjectReport(projectId);
              if (reportRes.data && reportRes.data.success) {
                formData.reportData = reportRes.data.data;
                isReportGenerated.value = true;
                
                if (formData.reportData && formData.reportData.reportContent) {
                  try {
                    const reportContent = JSON.parse(formData.reportData.reportContent);
                    
                    if (reportContent.formData) {
                      formData.calculationMethod = reportContent.formData.calculationMethod || formData.calculationMethod;
                      formData.calculationResult = reportContent.formData.calculationResult || null;
                      
                      if (formData.algorithmCategory === '培训类' && reportContent.formData.trainingTableRows) {
                        formData.trainingTableRows = reportContent.formData.trainingTableRows;
                      } else if (formData.algorithmCategory === '工程咨询类' && reportContent.formData.engineeringTableRows) {
                        formData.engineeringTableRows = reportContent.formData.engineeringTableRows;
                      } else if (reportContent.formData.standardTableRows) {
                        formData.standardTableRows = reportContent.formData.standardTableRows;
                      }
                    }
                  } catch (error) {
                    console.error('解析报告内容失败:', error);
                  }
                }
              }
            }
          } catch (error) {
            console.error('获取项目详情失败:', error);
            ElMessage.error('获取项目详情失败: ' + (error.message || '未知错误'));
          }
        }
        
        if (step && !isNaN(parseInt(step))) {
          const stepNum = parseInt(step);
          if (stepNum === 1) currentStep.value = 'step1';
          else if (stepNum === 2) currentStep.value = 'step2';
          else if (stepNum === 3) currentStep.value = 'step3';
        }
      }
    });

    return {
      currentStep,
      formData,
      isDetailMode,
      isReportGenerated,
      isGeneratingReport,
      formatDate,
      formatPrice,
      formatCalculationProcess,
      handleGenerateReport,
      exportReport,
      refreshReport,
      printReport,
      handleNextStep,
      handlePrevStep,
      finishProcess,
      setCalculationMethod,
      handleReportGenerated,
      handleCalculationComplete
    };
  }
}
</script>

<style lang="scss" scoped>
.split-layout-container {
  padding: 20px;
  background-color: #ffffff;
  min-height: calc(100vh - 120px);
}

.page-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    font-size: 28px;
    color: #303133;
    margin-bottom: 8px;
    font-weight: 600;
  }

  .subtitle {
    color: #606266;
    font-size: 16px;
    margin: 0;
  }
}

.split-layout {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
  min-height: 600px;
}

.left-panel {
  width: 50%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  overflow: hidden;
}

.right-panel {
  width: 50%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}



.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.step-section {
  margin-bottom: 30px;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.step-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;

  i {
    font-size: 20px;
    color: #606266;
  }

  .el-tag {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 500;
    margin-left: auto;
  }
}

.step-description {
  color: #64748b;
  font-size: 14px;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.placeholder-text {
  text-align: center;
  padding: 40px 20px;
  color: #94a3b8;
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #e2e8f0;
  margin: 0;
  font-size: 14px;
}

.calculation-steps,
.report-steps {
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #73a9ff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-right: 12px;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .step-text {
    flex: 1;

    .step-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 6px;
    }

    .step-list {
      margin: 0;
      padding-left: 0;
      list-style: none;

      li {
        position: relative;
        padding-left: 16px;
        margin-bottom: 4px;
        font-size: 13px;
        color: #606266;
        line-height: 1.4;

        &:before {
          content: '•';
          position: absolute;
          left: 0;
          color: #c0c4cc;
          font-weight: bold;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.card-body {
  margin-top: 16px;
  padding-top: 16px;
}

.generate-actions {
  text-align: center;
  padding: 20px 0;

  .el-button {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;

    &.el-button--primary {
      background: #73a9ff;
      border: none;
      box-shadow: 0 2px 4px rgba(115, 169, 255, 0.2);

      &:hover {
        background: #8bb5ff;
        box-shadow: 0 3px 6px rgba(115, 169, 255, 0.3);
      }

      &:focus {
        box-shadow: 0 0 0 2px rgba(115, 169, 255, 0.3);
      }
    }
  }
}

.disabled-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;

  .disabled-text {
    color: #c0c4cc;
    font-size: 14px;
    margin: 0;
  }
}





/* 右侧报告预览样式 */
.report-preview {
  height: 100%;

  :deep(.generate-report-step) {
    height: 100%;

    .report-container {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.empty-report {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  :deep(.el-empty) {
    .el-empty__description {
      color: #909399;
      font-size: 14px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .split-layout {
    flex-direction: column;
    height: auto;
    min-height: auto;
  }

  .left-panel,
  .right-panel {
    width: 100%;
    min-height: 400px;
  }


}

@media (max-width: 768px) {
  .split-layout-container {
    padding: 15px;
  }



  .step-section {
    margin-bottom: 20px;
  }

  .step-title {
    font-size: 16px;

    i {
      font-size: 18px;
    }
  }

  .step-item {
    margin-bottom: 12px;

    .step-number {
      width: 20px;
      height: 20px;
      font-size: 11px;
      margin-right: 10px;
    }

    .step-text {
      .step-title {
        font-size: 13px;
      }

      .step-list li {
        font-size: 12px;
      }
    }
  }


}

/* 动画效果 */
.step-section {
  animation: fadeInUp 0.5s ease-out;
}

.report-preview {
  animation: slideInRight 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* AI计算结果展示样式 */
.ai-result-display {
  margin-top: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.ai-result-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.ai-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.ai-icon {
  font-size: 24px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.ai-title {
  font-size: 18px;
  font-weight: 600;
  flex: 1;
}

.ai-status {
  font-size: 14px;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.result-summary {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.price-highlight {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-label {
  font-size: 16px;
  opacity: 0.9;
}

.price-value {
  font-size: 28px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.calculation-process {
  position: relative;
  z-index: 1;
}

.process-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.process-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  line-height: 1.6;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.process-content::-webkit-scrollbar {
  width: 6px;
}

.process-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.process-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.process-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 统一表单元素高度 */
:deep(.el-input__inner),
:deep(.el-select .el-input__inner),
:deep(.el-cascader .el-input__inner),
:deep(.el-input-number .el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-select),
:deep(.el-cascader),
:deep(.el-input-number) {
  height: 32px;
}
</style>
