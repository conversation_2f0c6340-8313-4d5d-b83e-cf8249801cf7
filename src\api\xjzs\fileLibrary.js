import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/xjzs/file-library/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/xjzs/file-library/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/xjzs/file-library/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/xjzs/file-library/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/xjzs/file-library/submit',
    method: 'post',
    data: row
  })
}

export const exportFileLibrary = (params) => {
  return request({
    url: '/xjzs/file-library/export-file-library',
    method: 'post',
    params: params,
    responseType: 'blob'
  })
}

// 文件上传
export const uploadFile = (file) => {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/xjzs/file-library/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 文件上传并保存文件库记录
export const uploadAndSave = (file, fileData) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('fileName', fileData.fileName);
  formData.append('fileType', fileData.fileType);
  if (fileData.publishUnit) formData.append('publishUnit', fileData.publishUnit);
  if (fileData.publishTime) formData.append('publishTime', fileData.publishTime);
  if (fileData.status) formData.append('status', fileData.status);
  if (fileData.remark) formData.append('remark', fileData.remark);

  return request({
    url: '/xjzs/file-library/upload-and-save',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 文件下载
export const downloadFile = (id) => {
  return request({
    url: `/xjzs/file-library/download/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}



// 文件更新（编辑时上传新文件）
export const updateFileAndRecord = (file, updateData) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('id', updateData.id);
  formData.append('fileName', updateData.fileName);
  formData.append('fileType', updateData.fileType);
  if (updateData.publishUnit) formData.append('publishUnit', updateData.publishUnit);
  if (updateData.publishTime) formData.append('publishTime', updateData.publishTime);
  if (updateData.status) formData.append('status', updateData.status);
  if (updateData.remark) formData.append('remark', updateData.remark);

  return request({
    url: '/xjzs/file-library/update-file',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}
