/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.xjzs.pojo.entity.SupplierEntity;
import org.springblade.modules.xjzs.pojo.vo.SupplierVO;
import org.springblade.modules.xjzs.excel.SupplierExcel;
import org.springblade.modules.xjzs.wrapper.SupplierWrapper;
import org.springblade.modules.xjzs.service.ISupplierService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 供应商信息 控制器
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/supplier")
@Tag(name = "供应商信息", description = "供应商信息接口")
public class SupplierController extends BladeController {

	private final ISupplierService supplierService;

	/**
	 * 供应商信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入supplier")
	public R<SupplierVO> detail(SupplierEntity supplier) {
		SupplierEntity detail = supplierService.getOne(Condition.getQueryWrapper(supplier));
		return R.data(SupplierWrapper.build().entityVO(detail));
	}
	/**
	 * 供应商信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入supplier")
	public R<IPage<SupplierVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> supplier, Query query) {
		IPage<SupplierEntity> pages = supplierService.page(Condition.getPage(query), Condition.getQueryWrapper(supplier, SupplierEntity.class));
		return R.data(SupplierWrapper.build().pageVO(pages));
	}

	/**
	 * 供应商信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入supplier")
	public R<IPage<SupplierVO>> page(SupplierVO supplier, Query query) {
		IPage<SupplierVO> pages = supplierService.selectSupplierPage(Condition.getPage(query), supplier);
		return R.data(pages);
	}

	/**
	 * 供应商信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入supplier")
	public R save(@Valid @RequestBody SupplierEntity supplier) {
		return R.status(supplierService.save(supplier));
	}

	/**
	 * 供应商信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入supplier")
	public R update(@Valid @RequestBody SupplierEntity supplier) {
		return R.status(supplierService.updateById(supplier));
	}

	/**
	 * 供应商信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入supplier")
	public R submit(@Valid @RequestBody SupplierEntity supplier) {
		return R.status(supplierService.saveOrUpdate(supplier));
	}

	/**
	 * 供应商信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(supplierService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-supplier")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入supplier")
	public void exportSupplier(@Parameter(hidden = true) @RequestParam Map<String, Object> supplier, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SupplierEntity> queryWrapper = Condition.getQueryWrapper(supplier, SupplierEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Supplier::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SupplierEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SupplierExcel> list = supplierService.exportSupplier(queryWrapper);
		ExcelUtil.export(response, "供应商信息数据" + DateUtil.time(), "供应商信息数据表", list, SupplierExcel.class);
	}

}
