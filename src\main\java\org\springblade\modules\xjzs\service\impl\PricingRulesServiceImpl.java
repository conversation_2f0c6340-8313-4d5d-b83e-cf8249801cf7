/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.service.impl;

import org.springblade.modules.xjzs.pojo.entity.PricingRulesEntity;
import org.springblade.modules.xjzs.pojo.vo.PricingRulesVO;
import org.springblade.modules.xjzs.excel.PricingRulesExcel;
import org.springblade.modules.xjzs.mapper.PricingRulesMapper;
import org.springblade.modules.xjzs.service.IPricingRulesService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import java.util.List;

/**
 * 计价规则表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class PricingRulesServiceImpl extends BaseServiceImpl<PricingRulesMapper, PricingRulesEntity> implements IPricingRulesService {

	@Override
	public IPage<PricingRulesVO> selectPricingRulesPage(IPage<PricingRulesVO> page, PricingRulesVO pricingRules) {
		return page.setRecords(baseMapper.selectPricingRulesPage(page, pricingRules));
	}

	@Override
	public List<PricingRulesExcel> exportPricingRules(Wrapper<PricingRulesEntity> queryWrapper) {
		return baseMapper.exportPricingRules(queryWrapper);
	}

}
