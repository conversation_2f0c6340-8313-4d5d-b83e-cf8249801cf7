<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.SupplierInquiryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="supplierInquiryRecordResultMap" type="org.springblade.modules.xjzs.pojo.entity.SupplierInquiryRecordEntity">
        <result column="id" property="id"/>
        <result column="project_inquiry_id" property="projectInquiryId"/>
        <result column="project_id" property="projectId"/>
        <result column="inquiry_time" property="inquiryTime"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="quantity" property="quantity"/>
        <result column="supplier" property="supplier"/>
        <result column="inquiry_channel" property="inquiryChannel"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="total_price" property="totalPrice"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 项目询价记录VO映射结果 -->
    <resultMap id="supplierInquiryRecordVOResultMap" type="org.springblade.modules.xjzs.pojo.vo.SupplierInquiryRecordVO">
        <result column="project_name" property="projectName"/>
        <result column="project_budget" property="projectBudget"/>
        <result column="dept_name" property="deptName"/>
        <result column="user_name" property="userName"/>
        <result column="inquiry_time" property="inquiryTime"/>
        <result column="service_name" property="serviceName"/>
        <result column="service_content" property="serviceContent"/>
        <result column="supplier" property="supplier"/>
        <result column="quantity" property="quantity"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="total_price" property="totalPrice"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <select id="selectSupplierInquiryRecordPage" resultMap="supplierInquiryRecordVOResultMap">
        SELECT
            p.name as project_name,
            pir.project_budget,
            d.dept_name,
            u.real_name as user_name,
            sir.inquiry_time,
            CASE WHEN pir.project_type = '服务类' THEN pir.service_name ELSE pir.product_name END AS service_name,
            CASE WHEN pir.project_type = '服务类' THEN pir.service_content ELSE pir.specifications END AS service_content,
            pir.quantity,
            sir.supplier,
            sir.inquiry_channel,
            sir.unit_price,
            sir.total_price,
            sir.remark
        FROM
            xjzs_supplier_inquiry_record sir
        LEFT JOIN xjzs_project_inquiry_record pir on sir.project_inquiry_id = pir.id
        LEFT JOIN xjzs_project p ON pir.project_id = p.id
        LEFT JOIN blade_dept d ON pir.create_dept::bigint = d.id
        LEFT JOIN blade_user u ON pir.create_user = u.id
        WHERE
            sir.is_deleted = 0
        AND pir.is_deleted = 0
        <if test="supplierInquiryRecord.projectName != null and supplierInquiryRecord.projectName != ''">
            AND p.name LIKE CONCAT('%', #{supplierInquiryRecord.projectName}, '%')
        </if>
        <if test="supplierInquiryRecord.deptName != null and supplierInquiryRecord.deptName != ''">
            AND d.dept_name LIKE CONCAT('%', #{supplierInquiryRecord.deptName}, '%')
        </if>
        <if test="supplierInquiryRecord.userName != null and supplierInquiryRecord.userName != ''">
            AND u.real_name LIKE CONCAT('%', #{supplierInquiryRecord.userName}, '%')
        </if>
        ORDER BY pir.create_time DESC
    </select>


    <select id="selectSupplierInquiryRecordPage" resultMap="supplierInquiryRecordResultMap">
        select * from xjzs_supplier_inquiry_record where is_deleted = 0
    </select>


    <select id="exportSupplierInquiryRecord" resultType="org.springblade.modules.xjzs.excel.SupplierInquiryRecordExcel">
        SELECT * FROM xjzs_supplier_inquiry_record ${ew.customSqlSegment}
    </select>

</mapper>
