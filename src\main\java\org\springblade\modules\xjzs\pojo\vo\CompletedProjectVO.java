package org.springblade.modules.xjzs.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

    /**
     * 已完成项目视图对象
     */
    @Data
    @Schema(description = "已完成项目信息")
    public class CompletedProjectVO {

        /**
         * 项目ID
         */
        @Schema(description = "项目ID")
        private String id;

        /**
         * 项目名称
         */
        @Schema(description = "项目名称")
        private String name;

        /**
         * 项目编号
         */
        @Schema(description = "项目编号")
        private String code;

        /**
         * 项目价格
         */
        @Schema(description = "项目价格")
        private String price;

        /**
         * 状态（已完成）
         */
        @Schema(description = "状态")
        private String status;
        /**
         * 状态（已完成）
         */
        @Schema(description = "报告id")
        private String reportId;
    }
