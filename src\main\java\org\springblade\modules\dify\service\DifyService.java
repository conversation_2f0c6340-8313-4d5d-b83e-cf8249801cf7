package org.springblade.modules.dify.service;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.dify.req.DifyRequestBody;
import org.springblade.modules.dify.req.DifyWorkflowRequestBody;
import org.springblade.modules.dify.resp.BlockResponse;
import org.springblade.modules.dify.resp.StreamResponse;
import org.springblade.modules.dify.resp.WorkflowStreamResponse;
import org.springblade.modules.dify.util.FileProcessUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import java.time.Duration;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



@Service
@RequiredArgsConstructor
@Slf4j
public class DifyService {

    @Value("${dify.url}")
    private String url;
    @Autowired
    private final WebClient webClient;

    private final HashMap<String,String> fileContentMaps=new HashMap<>();

    /**
     * 流式调用dify.
     *
     * @param query  查询文本
     * @param userId 用户id
     * @param apiKey apiKey 通过 apiKey 获取权限并区分不同的 dify 应用
     * @return Flux 响应流
     */
    public Flux<StreamResponse> streamingMessage(String query, Long userId, String apiKey, String conversationId, String fileId) {


        //1.设置请求体
        DifyRequestBody body = new DifyRequestBody();
        String fileContent=fileContentMaps.get(fileId);
        if(fileContent==null||fileContent.isEmpty()||fileContent.equals("")) {
            HashMap<String,String> map=new HashMap<>();
            map.put("content","1" );
            body.setInputs(new HashMap<>());
        }else{
            HashMap<String,String> map=new HashMap<>();
            map.put("content",fileContent );
            body.setInputs(map);
        }
        body.setQuery(query);
        body.setResponseMode("streaming");
        if(conversationId==null||conversationId.equals("null")){
            conversationId="";
        }
        body.setConversationId(conversationId);
        body.setUser(userId.toString());
        //2.使用webclient发送post请求
        return webClient.post()
                .uri(url+"/chat-messages")
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    httpHeaders.setBearerAuth(apiKey);
                })
                .bodyValue(JSON.toJSONString(body))
                .retrieve()
                .bodyToFlux(StreamResponse.class)
                .onErrorResume(error -> {
                    log.error("Dify API调用失败: {}", error.getMessage(), error);
                    // 返回错误格式的StreamResponse
                    StreamResponse errorResponse = new StreamResponse();
                    errorResponse.setEvent("error");
                    errorResponse.setAnswer("Dify服务连接失败: " + error.getMessage());
                    errorResponse.setCreated_at(System.currentTimeMillis());
                    return Flux.just(errorResponse);
                });
    }


    /**
     * 阻塞式调用dify.
     *
     * @param query 查询文本
     * @param userId 用户id
     * @param apiKey apiKey 通过 apiKey 获取权限并区分不同的 dify 应用
     * @return BlockResponse
     */
    public BlockResponse blockingMessage(String query, Long userId, String apiKey, String conversationId) {
        //1.设置请求体
        DifyRequestBody body = new DifyRequestBody();
        body.setInputs(new HashMap<>());
        body.setQuery(query);
        body.setResponseMode("blocking");
        body.setConversationId(conversationId);
        body.setUser(userId.toString());

        //2.使用webclient发送post请求
        return webClient.post()
                .uri(url + "/chat-messages")
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    httpHeaders.setBearerAuth(apiKey);
                })
                .bodyValue(JSON.toJSONString(body))
                .retrieve()
                .bodyToMono(BlockResponse.class)
                .block(); // 转换为阻塞调用
    }

    /**
     * 阻塞式调用dify - 使用完整请求体格式.
     *
     * @param requestBody 完整的请求体，包含inputs、response_mode、user等
     * @param apiKey apiKey
     * @return BlockResponse
     */
    public BlockResponse blockingMessageWithRequestBody(Map<String, Object> requestBody, String apiKey) {
        //1.设置请求体
        DifyRequestBody body = new DifyRequestBody();

        // 从前端传来的完整请求体中提取数据
        if (requestBody.containsKey("inputs")) {
            Object inputsObj = requestBody.get("inputs");
            if (inputsObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, String> inputs = (Map<String, String>) inputsObj;
                body.setInputs(inputs);
            }
        }

        body.setQuery(""); // 使用inputs时query可以为空
        body.setResponseMode((String) requestBody.getOrDefault("response_mode", "streaming"));
        body.setUser((String) requestBody.getOrDefault("user", "0"));

        String conversationId = (String) requestBody.get("conversation_id");
        if(conversationId==null||conversationId.equals("null")){
            conversationId="";
        }
        body.setConversationId(conversationId);

        //2.使用webclient发送post请求
        return webClient.post()
                .uri(url + "/chat-messages")
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    httpHeaders.setBearerAuth(apiKey);
                })
                .bodyValue(JSON.toJSONString(body))
                .retrieve()
                .bodyToMono(BlockResponse.class)
                .block(); // 转换为阻塞调用
    }

    public String upload(String fileId, MultipartFile file) {
        try {
            List<String> list= FileProcessUtils.processFile(file);
            String content=String.join("\n", list) + "\n";
            fileContentMaps.put(fileId,content);
            return "success";
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return "fail";

    }

    /**
     * 流式调用dify - 支持复杂对象类型的inputs（专为京东慧采等需要传递对象的场景）.
     *
     * @param requestBody 完整的请求体，包含inputs、response_mode、user等
     * @param apiKey apiKey
     * @return Flux 响应流
     */
    public Flux<WorkflowStreamResponse> streamingMessageWithObjectInputsWorkflow(Map<String, Object> requestBody, String apiKey) {
        //1.设置请求体
        DifyWorkflowRequestBody body = new DifyWorkflowRequestBody();

        // 从前端传来的完整请求体中提取数据，支持Object类型
        if (requestBody.containsKey("inputs")) {
            Object inputsObj = requestBody.get("inputs");
            if (inputsObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> originalInputs = (Map<String, Object>) inputsObj;
                
                // 将所有Object类型的值安全地转换为字符串
                Map<String, String> stringInputs = new HashMap<>();
                for (Map.Entry<String, Object> entry : originalInputs.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    String jsonValue;
                    if (value == null) {
                        jsonValue = "";
                    } else if (value instanceof String) {
                        // 如果已经是字符串，直接使用
                        jsonValue = (String) value;
                    } else {
                        jsonValue = value.toString();
                    }
                    stringInputs.put(key, jsonValue);
                }
                
                body.setInputs(stringInputs);
            }
        }

        body.setQuery(""); // 使用inputs时query可以为空
        body.setResponseMode((String) requestBody.getOrDefault("response_mode", "streaming"));
        body.setUser((String) requestBody.getOrDefault("user", "0"));

        String conversationId = (String) requestBody.get("conversation_id");
        if(conversationId==null||conversationId.equals("null")){
            conversationId="";
        }
        body.setConversationId(conversationId);

        // 添加调试日志
        String jsonBody = JSON.toJSONString(body);
        log.info("发送给Dify Workflow的请求体: {}", jsonBody);

        //2.使用webclient发送post请求，返回WorkflowStreamResponse
        return webClient.post()
                .uri(url + "/workflows/run")
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    httpHeaders.setBearerAuth(apiKey);
                })
                .bodyValue(jsonBody)
                .retrieve()
                .bodyToFlux(WorkflowStreamResponse.class)
                .onErrorResume(error -> {
                    log.error("Dify Workflow API调用失败: {}", error.getMessage(), error);
                    WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                    errorResponse.setErrorInfo("Dify服务连接失败: " + error.getMessage());
                    return Flux.just(errorResponse);
                });
    }

    /**
     * 流式调用dify - 使用完整请求体格式（返回WorkflowStreamResponse）.
     *
     * @param requestBody 完整的请求体，包含inputs、response_mode、user等
     * @param apiKey apiKey
     * @return Flux 响应流
     */
    public Flux<WorkflowStreamResponse> streamingMessageWithRequestBodyWorkflow(Map<String, Object> requestBody, String apiKey) {
        //1.设置请求体
        DifyRequestBody body = new DifyRequestBody();

        // 从前端传来的完整请求体中提取数据
        if (requestBody.containsKey("inputs")) {
            Object inputsObj = requestBody.get("inputs");
            if (inputsObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, String> inputs = (Map<String, String>) inputsObj;
                body.setInputs(inputs);
            }
        }

        body.setQuery(""); // 使用inputs时query可以为空
        body.setResponseMode((String) requestBody.getOrDefault("response_mode", "streaming"));
        body.setUser((String) requestBody.getOrDefault("user", "0"));

        String conversationId = (String) requestBody.get("conversation_id");
        if(conversationId==null||conversationId.equals("null")){
            conversationId="";
        }
        body.setConversationId(conversationId);

        //2.使用webclient发送post请求，返回WorkflowStreamResponse
        return webClient.post()
                .uri(url+"/workflows/run")
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    httpHeaders.setBearerAuth(apiKey);
                })
                .bodyValue(JSON.toJSONString(body))
                .retrieve()
                .bodyToFlux(WorkflowStreamResponse.class)
                .onErrorResume(error -> {
                    log.error("Dify API调用失败: {}", error.getMessage(), error);
                    // 返回错误格式的WorkflowStreamResponse
                    WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                    errorResponse.setErrorInfo("Dify服务连接失败: " + error.getMessage());
                    return Flux.just(errorResponse);
                });
    }

    public String suggested(String messageId,String apiKey) {
        try {
            return webClient.get()
                    .uri(url + "/messages/" + messageId + "/suggested?user=0")
                    .headers(httpHeaders -> {
                        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                        httpHeaders.setBearerAuth(apiKey);
                    })
                    .retrieve()
                    .bodyToMono(String.class)
                    .block(); // 转换为阻塞调用
        } catch (Exception e) {
            log.error(e.getMessage());
            return "";
        }

    }
}
