<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.FileLibraryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fileLibraryResultMap" type="org.springblade.modules.xjzs.pojo.entity.FileLibraryEntity">
        <result column="id" property="id"/>
        <result column="file_name" property="fileName"/>
        <result column="file_type" property="fileType"/>
        <result column="file_path" property="filePath"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_size" property="fileSize"/>
        <result column="original_file_name" property="originalFileName"/>
        <result column="publish_unit" property="publishUnit"/>
        <result column="publish_time" property="publishTime"/>
        <result column="file_status" property="fileStatus"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="selectFileLibraryPage" resultMap="fileLibraryResultMap">
        SELECT * FROM xjzs_file_library
        WHERE is_deleted = 0
        <if test="fileLibrary.fileName != null and fileLibrary.fileName != ''">
            AND file_name LIKE CONCAT('%', #{fileLibrary.fileName}, '%')
        </if>
        <if test="fileLibrary.fileType != null and fileLibrary.fileType != ''">
            AND file_type = #{fileLibrary.fileType}
        </if>
        <if test="fileLibrary.publishUnit != null and fileLibrary.publishUnit != ''">
            AND publish_unit LIKE CONCAT('%', #{fileLibrary.publishUnit}, '%')
        </if>
        <if test="fileLibrary.publishTime != null">
            AND publish_time = #{fileLibrary.publishTime}
        </if>
        <if test="fileLibrary.fileStatus != null and fileLibrary.fileStatus != ''">
            AND file_status = #{fileLibrary.fileStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="exportFileLibrary" resultType="org.springblade.modules.xjzs.excel.FileLibraryExcel">
        SELECT
            id,
            file_name as fileName,
            file_type as fileType,
            file_size as fileSize,
            original_file_name as originalFileName,
            publish_unit as publishUnit,
            publish_time as publishTime,
            file_status as fileStatus,
            remark,
            is_deleted as isDeleted
        FROM xjzs_file_library ${ew.customSqlSegment}
    </select>

</mapper>
