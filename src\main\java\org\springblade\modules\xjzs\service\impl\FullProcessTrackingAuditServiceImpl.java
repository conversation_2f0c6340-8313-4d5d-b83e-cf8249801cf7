package org.springblade.modules.xjzs.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 全过程跟踪审计服务实现类
 */
@Service
@Slf4j
public class FullProcessTrackingAuditServiceImpl implements IAuditService {
    
    @Override
    public String getServiceType() {
        return "全过程跟踪审计";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        List<String> calculationProcess = new ArrayList<>();
        
        try {
            calculationProcess.add("开始计算全过程跟踪审计费用：");
            
            // 解析表格数据
            ObjectMapper objectMapper = new ObjectMapper();
            List<Map<String, Object>> tableDataList = objectMapper.readValue(
                params.getTableData(), 
                new TypeReference<List<Map<String, Object>>>() {}
            );
            
            if (tableDataList.isEmpty()) {
                calculationProcess.add("错误：表格数据为空");
                return AuditResult.fail("表格数据不能为空");
            }
            
            Map<String, Object> row = tableDataList.get(0);
            
            // 获取建筑安装工程造价
            BigDecimal constructionCost = getDecimalValue(row, "constructionCost");
            if (constructionCost == null || constructionCost.compareTo(BigDecimal.ZERO) <= 0) {
                calculationProcess.add("错误：建筑安装工程造价无效或为空");
                return AuditResult.fail("建筑安装工程造价不能为空或小于等于0");
            }
            
            calculationProcess.add("建筑安装工程造价：" + constructionCost.toPlainString() + " 万元");
            
            // 获取建设项目总投资
            BigDecimal totalInvestment = getDecimalValue(row, "totalInvestment");
            if (totalInvestment == null || totalInvestment.compareTo(BigDecimal.ZERO) <= 0) {
                calculationProcess.add("错误：建设项目总投资无效或为空");
                return AuditResult.fail("建设项目总投资不能为空或小于等于0");
            }
            
            calculationProcess.add("建设项目总投资：" + totalInvestment.toPlainString() + " 万元");
            
            // 获取土地及人民政府规费，默认为0
            BigDecimal landAndGovFees = getDecimalValue(row, "landAndGovFees");
            if (landAndGovFees == null) {
                landAndGovFees = BigDecimal.ZERO;
            }
            
            calculationProcess.add("土地及人民政府规费：" + landAndGovFees.toPlainString() + " 万元");
            
            // 计算工程概算审核费用
            BigDecimal budgetAuditFee = calculateBudgetAuditFee(constructionCost, totalInvestment, calculationProcess);
            
            // 计算过程跟踪审计费用
            BigDecimal processTrackingFee = calculateProcessTrackingFee(totalInvestment, landAndGovFees, calculationProcess);
            
            // 计算工程结算审查效益收费
            BigDecimal settlementBenefitFee = calculateSettlementBenefitFee(constructionCost, calculationProcess);
            
            // 计算最高限价
            BigDecimal totalCost = budgetAuditFee.add(processTrackingFee).add(settlementBenefitFee);
            
            calculationProcess.add("最高限价 = 工程概算审核费用(" + budgetAuditFee.toPlainString() + ") + 过程跟踪审计费用(" + 
                processTrackingFee.toPlainString() + ") + 工程结算审查效益收费(" + settlementBenefitFee.toPlainString() + ") = " + 
                totalCost.toPlainString() + " 元");
            
            // 创建结果
            AuditResult result = AuditResult.success(totalCost);
            result.setCalculationProcess(calculationProcess);
            
            // 添加计算详情
            Map<String, Object> budgetDetail = new HashMap<>();
            budgetDetail.put("type", "工程概算审核");
            budgetDetail.put("constructionCost", constructionCost);
            budgetDetail.put("totalInvestment", totalInvestment);
            budgetDetail.put("fee", budgetAuditFee);
            budgetDetail.put("description", String.format("工程概算审核费用: %s元", budgetAuditFee));
            result.addDetail(budgetDetail);
            
            Map<String, Object> trackingDetail = new HashMap<>();
            trackingDetail.put("type", "过程跟踪审计");
            trackingDetail.put("totalInvestment", totalInvestment);
            trackingDetail.put("landAndGovFees", landAndGovFees);
            trackingDetail.put("netInvestment", totalInvestment.subtract(landAndGovFees));
            trackingDetail.put("fee", processTrackingFee);
            trackingDetail.put("description", String.format("过程跟踪审计费用: %s元", processTrackingFee));
            result.addDetail(trackingDetail);
            
            Map<String, Object> benefitDetail = new HashMap<>();
            benefitDetail.put("type", "工程结算审查效益收费");
            benefitDetail.put("constructionCost", constructionCost);
            benefitDetail.put("fee", settlementBenefitFee);
            benefitDetail.put("description", String.format("工程结算审查效益收费: %s元", settlementBenefitFee));
            result.addDetail(benefitDetail);
            
            Map<String, Object> totalDetail = new HashMap<>();
            totalDetail.put("type", "最高限价");
            totalDetail.put("fee", totalCost);
            totalDetail.put("description", String.format("最高限价 = 工程概算审核(%s元) + 过程跟踪审计(%s元) + 工程结算审查效益收费(%s元) = %s元", 
                budgetAuditFee, processTrackingFee, settlementBenefitFee, totalCost));
            result.addDetail(totalDetail);
            
            return result;
            
        } catch (Exception e) {
            log.error("计算全过程跟踪审计费用时发生错误", e);
            calculationProcess.add("计算过程中发生错误: " + e.getMessage());
            AuditResult errorResult = AuditResult.fail("计算错误: " + e.getMessage());
            errorResult.setCalculationProcess(calculationProcess);
            return errorResult;
        }
    }
    
    /**
     * 计算工程概算审核费用
     * @param constructionCost 建筑安装工程造价（万元）
     * @param totalInvestment 建设项目总投资（万元）
     * @param calculationProcess 计算过程记录
     * @return 工程概算审核费用（元）
     */
    private BigDecimal calculateBudgetAuditFee(BigDecimal constructionCost, BigDecimal totalInvestment, List<String> calculationProcess) {
        calculationProcess.add("计算工程概算审核费用：");
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if (totalInvestment.compareTo(new BigDecimal("500")) <= 0) {
            // ≤500万元：费率 = 0.3%
            fee = totalInvestment.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            calculationProcess.add("建设项目总投资 ≤ 500万元，费率 = 0.3%");
            calculationProcess.add("计算：" + totalInvestment + " × 10000 × 0.003 = " + fee.toPlainString() + " 元");
        } else if (totalInvestment.compareTo(new BigDecimal("5000")) <= 0) {
            // 500~5000万元：分段计算
            BigDecimal baseFee = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            BigDecimal excessAmount = totalInvestment.subtract(new BigDecimal("500"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0022"));
            fee = baseFee.add(excessFee);
            calculationProcess.add("500万元 < 建设项目总投资 ≤ 5000万元，分段计算：");
            calculationProcess.add("基础费用：500 × 10000 × 0.003 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.0022 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("小计：" + fee.toPlainString() + " 元");
        } else {
            // >5000万元：分段计算
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            BigDecimal baseFee2 = new BigDecimal("4500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0022"));
            BigDecimal excessAmount = totalInvestment.subtract(new BigDecimal("5000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.002"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            calculationProcess.add("建设项目总投资 > 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.003 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：4500 × 10000 × 0.0022 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：" + excessAmount + " × 10000 × 0.002 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("小计：" + fee.toPlainString() + " 元");
        }
        
        return fee.setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算过程跟踪审计费用
     * @param totalInvestment 建设项目总投资（万元）
     * @param landAndGovFees 土地及人民政府规费（万元）
     * @param calculationProcess 计算过程记录
     * @return 过程跟踪审计费用（元）
     */
    private BigDecimal calculateProcessTrackingFee(BigDecimal totalInvestment, BigDecimal landAndGovFees, List<String> calculationProcess) {
        calculationProcess.add("计算过程跟踪审计费用：");
        
        // 净投资 = 总投资 - 土地及政府规费
        BigDecimal netInvestment = totalInvestment.subtract(landAndGovFees);
        calculationProcess.add("净投资 = 总投资 - 土地及政府规费 = " + totalInvestment + " - " + landAndGovFees + " = " + netInvestment + " 万元");
        
        // 过程跟踪审计费 = 净投资 × 0.15%
        BigDecimal fee = netInvestment.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0015"));
        calculationProcess.add("过程跟踪审计费率 = 0.15%");
        calculationProcess.add("计算：" + netInvestment + " × 10000 × 0.0015 = " + fee.toPlainString() + " 元");
        
        return fee.setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算工程结算审查效益收费
     * @param constructionCost 建筑安装工程造价（万元）
     * @param calculationProcess 计算过程记录
     * @return 工程结算审查效益收费（元）
     */
    private BigDecimal calculateSettlementBenefitFee(BigDecimal constructionCost, List<String> calculationProcess) {
        calculationProcess.add("计算工程结算审查效益收费：");
        
        // 工程结算审查效益收费 = 建筑安装工程造价 × 1% × 5%
        BigDecimal fee = constructionCost.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.01")).multiply(new BigDecimal("0.05"));
        calculationProcess.add("工程结算审查效益收费 = 建筑安装工程造价 × 1% × 5%");
        calculationProcess.add("计算：" + constructionCost + " × 10000 × 0.01 × 0.05 = " + fee.toPlainString() + " 元");
        
        return fee.setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}