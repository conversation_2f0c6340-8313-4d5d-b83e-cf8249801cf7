/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 计算系数 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CalculationCoefficientExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键ID")
	private Long id;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private Long tenantId;
	/**
	 * 类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("类型")
	private String type;
	/**
	 * 系数名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("系数名称")
	private String coefficientName;
	/**
	 * 系数类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("系数类型")
	private String coefficientType;
	/**
	 * 系数值
	 */
	@ColumnWidth(20)
	@ExcelProperty("系数值")
	private BigDecimal coefficientValue;
	/**
	 * 系数规则
	 */
	@ColumnWidth(20)
	@ExcelProperty("系数规则")
	private String coefficientRule;
	/**
	 * 描述
	 */
	@ColumnWidth(20)
	@ExcelProperty("描述")
	private String description;
	/**
	 * 逻辑删除标志（0:未删除；1:已删除)
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除标志（0:未删除；1:已删除)")
	private Short isDeleted;

}
