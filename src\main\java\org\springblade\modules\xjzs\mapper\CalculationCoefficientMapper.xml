<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.CalculationCoefficientMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="calculationCoefficientResultMap" type="org.springblade.modules.xjzs.pojo.entity.CalculationCoefficientEntity">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="type" property="type"/>
        <result column="coefficient_name" property="coefficientName"/>
        <result column="coefficient_type" property="coefficientType"/>
        <result column="coefficient_value" property="coefficientValue"/>
        <result column="coefficient_rule" property="coefficientRule"/>
        <result column="description" property="description"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectCalculationCoefficientPage" resultMap="calculationCoefficientResultMap">
        select * from xjzs_calculation_coefficient where is_deleted = 0
    </select>


    <select id="exportCalculationCoefficient" resultType="org.springblade.modules.xjzs.excel.CalculationCoefficientExcel">
        SELECT * FROM xjzs_calculation_coefficient ${ew.customSqlSegment}
    </select>

</mapper>
