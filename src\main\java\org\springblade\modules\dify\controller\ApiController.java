package org.springblade.modules.dify.controller;

import lombok.RequiredArgsConstructor;
import org.springblade.modules.dify.resp.BlockResponse;
import org.springblade.modules.dify.resp.StreamResponse;
import org.springblade.modules.dify.service.DifyService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/dify")
@RequiredArgsConstructor
public class ApiController {

    @Value("${dify.key.jdhc}")
    private String testKey;

    private final DifyService difyService;

    @GetMapping("/block")
    public String blockApi(@RequestParam String query,@RequestParam String key,@RequestParam String conversationId) {
        if(query==null||key==null){
            query = "鲁迅和周树人什么关系？";
            key=testKey;
        }
        BlockResponse blockResponse = difyService.blockingMessage(query, 0L, testKey,conversationId);
        return blockResponse.getAnswer();
    }

    @GetMapping("/stream")
    public Flux<StreamResponse> testSteamApi(@RequestParam String query,
                                             @RequestParam String key,
                                             @RequestParam(value = "conversationId", required = false) String conversationId,
                                             @RequestParam(value = "fileId", required = false) String fileId) {
        if(query==null||key==null){
            query = "鲁迅和周树人什么关系？";
            key=testKey;
        }
        return difyService.streamingMessage(query, 0L, key,conversationId,fileId);
    }

    @PostMapping ("/upload")
    public String streamapi(@RequestParam MultipartFile file,
                                          @RequestParam String fileId ) {
        return difyService.upload(fileId,file);
    }

    @GetMapping("/suggested")
    public String suggested(@RequestParam String messageId,@RequestParam String key) {

        return difyService.suggested(messageId,key);
    }

}
