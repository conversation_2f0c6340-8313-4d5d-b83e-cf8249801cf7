/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.xjzs.pojo.entity.CalculationCoefficientEntity;
import org.springblade.modules.xjzs.pojo.vo.CalculationCoefficientVO;
import org.springblade.modules.xjzs.excel.CalculationCoefficientExcel;
import org.springblade.modules.xjzs.wrapper.CalculationCoefficientWrapper;
import org.springblade.modules.xjzs.service.ICalculationCoefficientService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 计算系数 控制器
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/calculationCoefficient")
@Tag(name = "计算系数", description = "计算系数接口")
public class CalculationCoefficientController extends BladeController {

	private final ICalculationCoefficientService calculationCoefficientService;

	/**
	 * 计算系数 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入calculationCoefficient")
	public R<CalculationCoefficientVO> detail(CalculationCoefficientEntity calculationCoefficient) {
		CalculationCoefficientEntity detail = calculationCoefficientService.getOne(Condition.getQueryWrapper(calculationCoefficient));
		return R.data(CalculationCoefficientWrapper.build().entityVO(detail));
	}
	/**
	 * 计算系数 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入calculationCoefficient")
	public R<IPage<CalculationCoefficientVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> calculationCoefficient, Query query) {
		IPage<CalculationCoefficientEntity> pages = calculationCoefficientService.page(Condition.getPage(query), Condition.getQueryWrapper(calculationCoefficient, CalculationCoefficientEntity.class));
		return R.data(CalculationCoefficientWrapper.build().pageVO(pages));
	}

	/**
	 * 计算系数 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入calculationCoefficient")
	public R<IPage<CalculationCoefficientVO>> page(CalculationCoefficientVO calculationCoefficient, Query query) {
		IPage<CalculationCoefficientVO> pages = calculationCoefficientService.selectCalculationCoefficientPage(Condition.getPage(query), calculationCoefficient);
		return R.data(pages);
	}

	/**
	 * 计算系数 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入calculationCoefficient")
	public R save(@Valid @RequestBody CalculationCoefficientEntity calculationCoefficient) {
		return R.status(calculationCoefficientService.save(calculationCoefficient));
	}

	/**
	 * 计算系数 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入calculationCoefficient")
	public R update(@Valid @RequestBody CalculationCoefficientEntity calculationCoefficient) {
		return R.status(calculationCoefficientService.updateById(calculationCoefficient));
	}

	/**
	 * 计算系数 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入calculationCoefficient")
	public R submit(@Valid @RequestBody CalculationCoefficientEntity calculationCoefficient) {
		return R.status(calculationCoefficientService.saveOrUpdate(calculationCoefficient));
	}

	/**
	 * 计算系数 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(calculationCoefficientService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-calculationCoefficient")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入calculationCoefficient")
	public void exportCalculationCoefficient(@Parameter(hidden = true) @RequestParam Map<String, Object> calculationCoefficient, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<CalculationCoefficientEntity> queryWrapper = Condition.getQueryWrapper(calculationCoefficient, CalculationCoefficientEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(CalculationCoefficient::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(CalculationCoefficientEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<CalculationCoefficientExcel> list = calculationCoefficientService.exportCalculationCoefficient(queryWrapper);
		ExcelUtil.export(response, "计算系数数据" + DateUtil.time(), "计算系数数据表", list, CalculationCoefficientExcel.class);
	}

}
