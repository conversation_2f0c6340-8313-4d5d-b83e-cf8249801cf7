/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;

import lombok.Data;
import java.time.LocalDate;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;

/**
 * 文件库 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class FileLibraryExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * id;主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("id;主键")
	private Long id;

	/**
	 * 文件名称
	 */
	@ColumnWidth(30)
	@ExcelProperty("文件名称")
	private String fileName;

	/**
	 * 文件类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("文件类型")
	private String fileType;

	/**
	 * 文件大小
	 */
	@ColumnWidth(15)
	@ExcelProperty("文件大小(字节)")
	private Long fileSize;

	/**
	 * 原始文件名
	 */
	@ColumnWidth(30)
	@ExcelProperty("原始文件名")
	private String originalFileName;

	/**
	 * 发布单位
	 */
	@ColumnWidth(25)
	@ExcelProperty("发布单位")
	private String publishUnit;

	/**
	 * 发布时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("发布时间")
	private LocalDate publishTime;

	/**
	 * 状态
	 */
	@ColumnWidth(15)
	@ExcelProperty("状态")
	private String fileStatus;

	/**
	 * 备注
	 */
	@ColumnWidth(30)
	@ExcelProperty("备注")
	private String remark;

	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Long isDeleted;

}
