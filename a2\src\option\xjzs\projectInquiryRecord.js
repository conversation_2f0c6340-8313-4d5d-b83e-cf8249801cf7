export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "id;主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "项目编码",
      prop: "projectId",
      type: "input",
    },
    {
      label: "产品名称",
      prop: "productName",
      type: "input",
    },
    {
      label: "技术规格",
      prop: "specifications",
      type: "input",
    },
    {
      label: "数量",
      prop: "quantity",
      type: "input",
    },
    {
      label: "质量标准",
      prop: "standard",
      type: "input",
    },
    {
      label: "包装要求",
      prop: "packageRequirements",
      type: "input",
    },
    {
      label: "交货期",
      prop: "deliveryDate",
      type: "input",
    },
    {
      label: "服务内容",
      prop: "serviceContent",
      type: "input",
    },
    {
      label: "服务期限",
      prop: "servicePeriod",
      type: "input",
    },
    {
      label: "服务要求",
      prop: "serviceRequirements",
      type: "input",
    },
    {
      label: "其他说明",
      prop: "otherInstructions",
      type: "input",
    },
    {
      label: "交货地点",
      prop: "deliveryAddress",
      type: "input",
    },
    {
      label: "售后服务",
      prop: "aftersalesContent",
      type: "input",
    },
    {
      label: "需附文件",
      prop: "attachment",
      type: "input",
    },
    {
      label: "截止时间",
      prop: "deadline",
      type: "input",
    },
    {
      label: "联系人姓名",
      prop: "contactName",
      type: "input",
    },
    {
      label: "联系人电话",
      prop: "contactPhone",
      type: "input",
    },
    {
      label: "报价总金额",
      prop: "quotationTotalAmount",
      type: "number",
    },
    {
      label: "报价时间",
      prop: "quotationTime",
      type: "datetime",
    },
    {
      label: "报价公司",
      prop: "quotationCompany",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否已删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
