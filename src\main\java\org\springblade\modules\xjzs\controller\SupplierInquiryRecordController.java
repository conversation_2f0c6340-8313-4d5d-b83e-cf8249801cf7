/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.xjzs.excel.SupplierInquiryRecordExcel;
import org.springblade.modules.xjzs.pojo.entity.SupplierInquiryRecordEntity;
import org.springblade.modules.xjzs.pojo.vo.SupplierInquiryRecordVO;
import org.springblade.modules.xjzs.service.ISupplierInquiryRecordService;
import org.springblade.modules.xjzs.wrapper.SupplierInquiryRecordWrapper;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 供应商询价历史记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/supplierInquiryRecord")
@Tag(name = "供应商询价历史记录表", description = "供应商询价历史记录表接口")
public class SupplierInquiryRecordController extends BladeController {

	private final ISupplierInquiryRecordService supplierInquiryRecordService;

	/**
	 * 供应商询价历史记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入supplierInquiryRecord")
	public R<SupplierInquiryRecordVO> detail(SupplierInquiryRecordEntity supplierInquiryRecord) {
		SupplierInquiryRecordEntity detail = supplierInquiryRecordService.getOne(Condition.getQueryWrapper(supplierInquiryRecord));
		return R.data(SupplierInquiryRecordWrapper.build().entityVO(detail));
	}
	/**
	 * 供应商询价历史记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入supplierInquiryRecord")
	public R<IPage<SupplierInquiryRecordVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> supplierInquiryRecord, Query query) {
		IPage<SupplierInquiryRecordEntity> pages = supplierInquiryRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(supplierInquiryRecord, SupplierInquiryRecordEntity.class));
		return R.data(SupplierInquiryRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 供应商询价历史记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入supplierInquiryRecord")
	public R<IPage<SupplierInquiryRecordVO>> page(SupplierInquiryRecordVO supplierInquiryRecord, Query query) {
		IPage<SupplierInquiryRecordVO> pages = supplierInquiryRecordService.selectSupplierInquiryRecordPage(Condition.getPage(query), supplierInquiryRecord);
		return R.data(pages);
	}

	/**
	 * 供应商询价历史记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入supplierInquiryRecord")
	public R save(@Valid @RequestBody SupplierInquiryRecordEntity supplierInquiryRecord) {
		return R.status(supplierInquiryRecordService.save(supplierInquiryRecord));
	}

	/**
	 * 供应商询价历史记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入supplierInquiryRecord")
	public R update(@Valid @RequestBody SupplierInquiryRecordEntity supplierInquiryRecord) {
		return R.status(supplierInquiryRecordService.updateById(supplierInquiryRecord));
	}

	/**
	 * 供应商询价历史记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入supplierInquiryRecord")
	public R submit(@Valid @RequestBody SupplierInquiryRecordEntity supplierInquiryRecord) {
		return R.status(supplierInquiryRecordService.saveOrUpdate(supplierInquiryRecord));
	}

	/**
	 * 供应商询价历史记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(supplierInquiryRecordService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-supplierInquiryRecord")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入supplierInquiryRecord")
	public void exportSupplierInquiryRecord(@Parameter(hidden = true) @RequestParam Map<String, Object> supplierInquiryRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SupplierInquiryRecordEntity> queryWrapper = Condition.getQueryWrapper(supplierInquiryRecord, SupplierInquiryRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SupplierInquiryRecord::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SupplierInquiryRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SupplierInquiryRecordExcel> list = supplierInquiryRecordService.exportSupplierInquiryRecord(queryWrapper);
		ExcelUtil.export(response, "供应商询价历史记录表数据" + DateUtil.time(), "供应商询价历史记录表数据表", list, SupplierInquiryRecordExcel.class);
	}


	/**
	 * 供应商询价记录表 条件查询分页
	 */
	@GetMapping("/query-page")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "条件查询分页", description = "传入查询条件")
	public R<IPage<SupplierInquiryRecordVO>> queryPage(SupplierInquiryRecordVO supplierInquiryRecord, Query query) {
		IPage<SupplierInquiryRecordVO> pages = supplierInquiryRecordService.getSupplierInquiryRecordPage(Condition.getPage(query), supplierInquiryRecord);
		return R.data(pages);
	}
}
