package org.springblade.modules.xjzs.util;
import java.awt.Font;
import java.awt.GraphicsEnvironment;
import java.io.InputStream;
public class FontUtils {
    // 注册项目内嵌字体
    public static void registerFont(String fontPath) {
        try (InputStream is = FontUtils.class.getResourceAsStream(fontPath)) {
            Font font = Font.createFont(Font.TRUETYPE_FONT, is);
            GraphicsEnvironment.getLocalGraphicsEnvironment().registerFont(font);
        } catch (Exception e) {
            throw new RuntimeException("字体加载失败: " + fontPath, e);
        }
    }
}
