/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.xjzs.excel.ProjectExcel;
import org.springblade.modules.xjzs.excel.ProjectImporter;
import org.springblade.modules.xjzs.pojo.entity.ProjectEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectStatisticsVO;
import org.springblade.modules.xjzs.pojo.vo.ProjectVO;
import org.springblade.modules.xjzs.service.IProjectService;
import org.springblade.modules.xjzs.wrapper.ProjectWrapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 项目信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/project")
@Tag(name = "项目信息表", description = "项目信息表接口")
@Slf4j
public class ProjectController extends BladeController {

    private final IProjectService projectService;

    /**
     * 获取首页统计数据
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取首页统计数据", description = "获取首页统计卡片数据")
    public R<ProjectStatisticsVO> getStatistics() {
        ProjectStatisticsVO statistics = projectService.getProjectStatistics();
        return R.data(statistics);
    }

    /**
     * 项目信息表 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入project")
    public R<ProjectVO> detail(ProjectEntity project) {
        ProjectEntity detail = projectService.getOne(Condition.getQueryWrapper(project));
        return R.data(ProjectWrapper.build().entityVO(detail));
    }

    /**
     * 项目信息表 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入project")
    public R<IPage<ProjectVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> project, Query query) {
        IPage<ProjectEntity> pages = projectService.page(Condition.getPage(query),
                Condition.getQueryWrapper(project, ProjectEntity.class));
        return R.data(ProjectWrapper.build().pageVO(pages));
    }

    /**
     * 项目信息表 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入project")
    public R<IPage<ProjectVO>> page(ProjectVO project, Query query) {
        IPage<ProjectVO> pages = projectService.selectProjectPage(Condition.getPage(query), project);
        return R.data(pages);
    }

    /**
     * 项目信息表 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入project")
    public R save(@Valid @RequestBody ProjectEntity project) {
        return R.status(projectService.save(project));
    }

    /**
     * 项目信息表 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入project")
    public R update(@Valid @RequestBody ProjectEntity project) {
        return R.status(projectService.updateById(project));
    }

    /**
     * 项目信息表 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入project")
    public R submit(@Valid @RequestBody ProjectEntity project) {
        return R.status(projectService.saveOrUpdate(project));
    }

    /**
     * 项目信息表 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(projectService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 获取所有项目列表（不分页）
     */
    @GetMapping("/all")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "获取所有项目", description = "获取所有项目列表，用于下拉选择")
    public R<List<ProjectVO>> getAllProjects() {
        List<ProjectEntity> list = projectService.list();
        return R.data(ProjectWrapper.build().listVO(list));
    }

    /**
     * 导出数据
     */
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-project")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "导出数据", description = "传入project")
    public void exportProject(@Parameter(hidden = true) @RequestParam Map<String, Object> project, BladeUser bladeUser,
            HttpServletResponse response) {
        QueryWrapper<ProjectEntity> queryWrapper = Condition.getQueryWrapper(project, ProjectEntity.class);
        // if (!AuthUtil.isAdministrator()) {
        // queryWrapper.lambda().eq(Project::getTenantId, bladeUser.getTenantId());
        // }
        // queryWrapper.lambda().eq(ProjectEntity::getIsDeleted,
        // BladeConstant.DB_NOT_DELETED);
        List<ProjectExcel> list = projectService.exportProject(queryWrapper);
        ExcelUtil.export(response, "项目信息表数据" + DateUtil.time(), "项目信息表数据表", list, ProjectExcel.class);
    }

    /**
     * 导入项目数据
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "导入数据", description = "传入excel")
    public R<String> importProject(@RequestParam(value = "file", required = false) MultipartFile file, @RequestParam("isCovered") Integer isCovered) {
        try {
            ExcelUtil.save(file, new ProjectImporter(projectService, isCovered != null && isCovered == 1), ProjectExcel.class);
            return R.success("导入成功");
        } catch (Exception e) {
            log.error("导入项目数据失败", e);
            return R.fail("导入失败: " + e.getMessage());
        }
    }

}
