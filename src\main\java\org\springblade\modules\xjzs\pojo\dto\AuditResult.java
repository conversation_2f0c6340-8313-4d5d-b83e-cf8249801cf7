package org.springblade.modules.xjzs.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审计计算结果
 */
@Data
public class AuditResult {
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 总费用
     */
    private BigDecimal totalCost;
    
    /**
     * 计算详情
     */
    private List<Map<String, Object>> details = new ArrayList<>();
    
    /**
     * 计算过程描述（新增）
     */
    private List<String> calculationProcess = new ArrayList<>();
    
    /**
     * 添加计算详情
     */
    public void addDetail(Map<String, Object> detail) {
        this.details.add(detail);
    }
    
    /**
     * 创建成功结果
     */
    public static AuditResult success(BigDecimal totalCost) {
        AuditResult result = new AuditResult();
        result.setSuccess(true);
        result.setTotalCost(totalCost);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static AuditResult fail(String errorMessage) {
        AuditResult result = new AuditResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
