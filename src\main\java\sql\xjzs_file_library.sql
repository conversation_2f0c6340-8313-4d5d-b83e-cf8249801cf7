-- 创建表
CREATE TABLE xjzs_file_library (
  id bigint NOT NULL PRIMARY KEY,
  file_name varchar(255) NOT NULL,
  file_type varchar(50) DEFAULT NULL,
  file_path varchar(500) DEFAULT NULL,
  file_url varchar(1000) DEFAULT NULL,
  file_size bigint DEFAULT NULL,
  original_file_name varchar(255) DEFAULT NULL,
  publish_unit varchar(255) DEFAULT NULL,
  publish_time date DEFAULT NULL,
  file_status varchar(20) DEFAULT '生效中',
  remark text,
  create_user bigint,
  create_dept bigint,
  create_time timestamp(6),
  update_user bigint,
  update_time timestamp(6),
  status integer DEFAULT 1,
  is_deleted integer DEFAULT 0,
  tenant_id varchar(12) DEFAULT NULL
);

-- 添加表注释
COMMENT ON TABLE xjzs_file_library IS '文件库表';

-- 添加列注释
COMMENT ON COLUMN xjzs_file_library.id IS '主键';
COMMENT ON COLUMN xjzs_file_library.file_name IS '文件名称';
COMMENT ON COLUMN xjzs_file_library.file_type IS '文件类型：国家标准、行业标准、地方标准';
COMMENT ON COLUMN xjzs_file_library.file_path IS '文件路径(MinIO存储路径)';
COMMENT ON COLUMN xjzs_file_library.file_url IS '文件URL(MinIO访问URL)';
COMMENT ON COLUMN xjzs_file_library.file_size IS '文件大小(字节)';
COMMENT ON COLUMN xjzs_file_library.original_file_name IS '原始文件名';
COMMENT ON COLUMN xjzs_file_library.publish_unit IS '发布单位';
COMMENT ON COLUMN xjzs_file_library.publish_time IS '发布时间';
COMMENT ON COLUMN xjzs_file_library.file_status IS '状态：生效中、已失效';
COMMENT ON COLUMN xjzs_file_library.remark IS '备注';
COMMENT ON COLUMN xjzs_file_library.status IS '状态标志';
COMMENT ON COLUMN xjzs_file_library.is_deleted IS '删除标志';

-- 插入示例数据
INSERT INTO xjzs_file_library (
  id, file_name, file_type, publish_unit, publish_time,
  file_status, remark, create_user, create_dept, create_time,
  update_user, update_time, is_deleted, tenant_id
) VALUES
(1, '广东烟草商业系统直属单位公务用车制度改革实施意见(2018)12号', '地方标准', '粤烟办', '2018-12-01',
 '生效中', '', 1, 1, NOW(),
 1, NOW(), 0, '000000'),
(2, '广东烟草商业系统车辆配备使用管理暂行办法(2013)63号', '地方标准', '粤烟办', '2013-12-01',
 '生效中', '', 1, 1, NOW(),
 1, NOW(), 0, '000000'),
(3, '国家烟草专卖局关于印发烟草行业直属单位公务用车制度改革实施意见的通知(2017)254号','国家标准', '国烟办', '2017-06-01',
 '生效中', '', 1, 1, NOW(),
 1, NOW(), 0, '000000');