<template>
  <div>
    <el-row>
      <el-col :span="24">
        <basic-container>
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-800">欢迎使用最高限价编审助手</h2>
            <p class="text-gray-600 mt-2">简化采购限价编制，提高工作效率，让采购决策更加智能化</p>
          </div>

          <!-- 功能卡片区 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- 发起询价卡片 -->
            <el-card class="feature-card hover:border-blue-400" shadow="hover" @click="toStandardInquiry">
              <div class="p-6">
                <div class="bg-blue-100 p-3 rounded-lg mb-4 icon-container">
                  <img src="/img/inquiry-icon.svg" alt="发起询价" class="icon-size">
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">发起询价</h3>
                <p class="text-gray-600 mb-4">使用标准化询价流程，从供应商库或外部询价，生成可追溯的电子记录</p>
                <div class="flex items-center text-blue-600">
                  <span class="font-medium">立即使用</span>
                  <i class="el-icon-arrow-right ml-2"></i>
                </div>
              </div>
            </el-card>

            <!--  智能编审卡片 -->
            <el-card class="feature-card hover:border-green-400" shadow="hover" @click.stop="toIntelligentReviewWaterfall">
              <div class="p-6">
                <div class="bg-green-100 p-3 rounded-lg mb-4 icon-container">
                  <img src="/img/review-icon.svg" alt="智能编审" class="icon-size">
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">智能编审</h3>
                <p class="text-gray-600 mb-4">流程化引导设计，提供多种计算方式，预设标准化选项，减少手动输入</p>
                <div class="flex items-center justify-between">
<!--                  <div class="flex items-center text-green-600 cursor-pointer" @click="toIntelligentReview">-->
<!--                    <span class="font-medium">分步模式</span>-->
<!--                    <i class="el-icon-arrow-right ml-2"></i>-->
<!--                  </div>-->
<!--                  <el-button-->
<!--                    type="primary"-->
<!--                    size="small"-->
<!--                    plain-->
<!--                    @click.stop="toIntelligentReviewWaterfall"-->
<!--                    class="waterfall-btn">-->
<!--                    瀑布流模式-->
<!--                  </el-button>-->
                  <div class="flex items-center text-blue-600">
                    <span class="font-medium">立即使用</span>
                    <i class="el-icon-arrow-right ml-2"></i>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 智能问答卡片 -->
            <el-card class="feature-card hover:border-purple-400" shadow="hover" @click="toIntelligentQA">
              <div class="p-6">
                <div class="bg-purple-100 p-3 rounded-lg mb-4 icon-container">
                  <img src="/img/qa-icon.svg" alt="智能问答" class="icon-size">
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">智能问答</h3>
                <p class="text-gray-600 mb-4">获取采购政策解读、法规咨询、历史案例参考，智能解答您的各类采购问题</p>
                <div class="flex items-center text-purple-600">
                  <span class="font-medium">立即使用</span>
                  <i class="el-icon-arrow-right ml-2"></i>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 项目列表 -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">项目列表</h3>
            <div class="avue-crud">
              <el-row  style="padding:6px 18px 20px 0px; display: flex; justify-content: flex-end; align-items: center;">
                <div class="search-controls">
                  <el-select v-model="query.year" placeholder="选择年份" style="width: 120px; margin-right: 10px;">
                    <el-option label="2025年" value="2025"></el-option>
                  </el-select>
                  <el-input
                          v-model="query.name"
                          placeholder="搜索项目..."
                          style="width: 200px; margin-right: 10px;"
                          clearable
                  />
                  <el-button type="primary" @click="searchProjects">查询</el-button>
                </div>
              </el-row>
              <el-row>
                <!-- 列表模块 -->
                <el-table ref="table" v-loading="loading" :data="data" :height="tableHeight" style="width: 100%">
                  <el-table-column prop="name" label="项目名称" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="reportCode" label="报告编号" width="150" ></el-table-column>
                  <el-table-column prop="maxPriceLimit" label=" 最高限价金额" width="120" :formatter="formatPrice"></el-table-column>
                  <el-table-column prop="dept" label="所属部门" width="120" />
                  <el-table-column label="预算金额" width="120">
                    <template #default="scope">
                      {{ scope.row.budget ? scope.row.budget.toLocaleString() : 'N/A' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="项目类型" width="100" />
                  <el-table-column label="经办人" width="120">
                    <template #default="scope">
                      {{ scope.row.handlerUserName || '' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="projectStatusName" label="状态"  align="center" width="100">
                    <template #default="scope">
                      <el-tag :type="getStatusType(scope.row.projectStatus)">
                        {{ scope.row.projectStatusName || '未开始' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <!-- 操作栏模块 -->
                  <el-table-column prop="menu" label="操作"  align="center" width="100">
                    <template  #default="scope">
                      <el-button type="text" @click="viewProjectDetails(scope.row.id)" v-if="scope.row.reportCode">查看详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-row>
              <el-row>
                <div class="avue-crud__pagination" style="width:100%">
                  <!-- 分页模块 -->
                  <el-pagination align="right" background @size-change="sizeChange" @current-change="currentChange"
                    :current-page="page.currentPage" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="page.total">
                  </el-pagination>
                </div>
              </el-row>
            </div>
          </div>
        </basic-container>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getPage} from '@/api/xjzs/project';
import { getAllProjects } from '@/api/xjzs/project';
export default {
  name: "Wel",
  data() {
    return {
     projectOptions :[],
      query: {
        year: '2025',
        name: ''
      },
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      data: [],
      currentPage: 1,
      pageSize: 10
    };
  },
  mounted() {
    this.onLoad(this.page);
  },
  computed: {},
  methods: {
    formatPrice(row, column, cellValue) {
      if (!cellValue) return ''
      return cellValue.toLocaleString()
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    toStandardInquiry() {
      // 跳转到标准化询价页面
      this.$router.push({ path: '/xjzs/inquiry' });
    },
    toPriceStandard() {
      // 跳转到计价标准库页面
      this.$router.push({ path: '/xjzs/priceStandard' });
    },
    toIntelligentReview() {
      // 跳转到智能编审页面
      this.$router.push({ path: '/xjzs/project-assistant' });
    },
    toIntelligentReviewWaterfall() {
      // 跳转到智能编审页面(瀑布流版本)
      this.$router.push({ path: '/project-assistant-waterfall' });
    },
    toIntelligentQA() {
      // 跳转到智能问答页面
      this.$router.push({ path: '/intelligent-qa' });
    },
    viewProjectDetails(id) {
      this.$router.push({ 
        path: `/project-assistant-waterfall`,
        query: { 
          projectId: id,
          step: 3,
          mode: 'detail' // 添加详情模式标识
        } 
      });
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.table.clearSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case 0: // 未开始
          return 'info'
        case 1: // 询价中
          return 'warning'
        case 2: // 已完成
          return 'success'
        case 9: // 已终止
          return 'danger'
        default:
          return 'info'
      }
    },
    // 搜索项目
    searchProjects() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    }
  }
};
</script>

<style lang="scss" scoped>
.feature-card {
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

.el-card {
  border-radius: 0.75rem;
  overflow: hidden;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.gap-6 {
  gap: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-600 {
  color: #4b5563;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #059669;
}

.text-purple-600 {
  color: #7c3aed;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.bg-green-100 {
  background-color: #d1fae5;
}

.bg-purple-100 {
  background-color: #ede9fe;
}

.bg-white {
  background-color: #ffffff;
}

.inline-flex {
  display: inline-flex;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.hover\:border-blue-400:hover {
  border-color: #60a5fa;
}

.hover\:border-green-400:hover {
  border-color: #34d399;
}

.hover\:border-purple-400:hover {
  border-color: #a78bfa;
}
/* 统一输入框宽度 */
.uniform-input {
  width: 200px !important;
}

/* 图标容器样式 */
.icon-container {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图标大小 */
.icon-size {
  width: 28px;
  height: 28px;
}

/* 瀑布流按钮样式 */
.waterfall-btn {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.waterfall-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.search-controls {
  display: flex;
  align-items: center;
}

</style>
