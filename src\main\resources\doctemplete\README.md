# Word模板使用说明

## 概述
本目录用于存放Word文档模板，这些模板用于生成各种业务文档，如询价函、合同等。模板中使用占位符标记需要动态替换的内容。

## 模板文件
当前目录包含以下模板文件：
- `inquiry_template.docx` - 询价函模板

## 占位符格式
模板中的占位符使用`${placeholder}`格式，例如`${projectName}`表示项目名称。

## 询价函模板占位符说明
询价函模板中使用了以下占位符：

| 占位符 | 说明 | 示例值 |
|--------|------|--------|
| ${projectName} | 项目名称 | 湛江市政府办公楼装修工程 |
| ${currentDate} | 当前日期 | 2025年04月18日 |
| ${productName} | 产品名称/型号 | 办公家具套装A型 |
| ${specifications} | 技术规格 | 符合国家标准GB/T 3324-2017 |
| ${quantity} | 数量 | 100套 |
| ${standard} | 质量标准 | 符合ISO 9001质量管理体系要求 |
| ${packageRequirements} | 包装要求 | 防潮、防震、防尘包装 |
| ${deliveryDate} | 交货期 | 合同签订后30天内 |
| ${serviceContent} | 服务内容 | 包含安装、调试和培训 |
| ${servicePeriod} | 服务期限 | 1年 |
| ${serviceRequirements} | 服务要求 | 24小时响应，48小时内解决问题 |
| ${otherInstructions} | 其他说明 | 需提供产品质保证明和检测报告 |
| ${deadline} | 报价截止时间 | 2025年05月01日 |

## 使用方法
1. 将模板文件放置在此目录下
2. 在代码中使用`WordTemplateUtil.loadTemplate("doctemplete/模板文件名.docx")`加载模板
3. 使用`WordTemplateUtil.replacePlaceholders(document, placeholders)`替换占位符
4. 使用`WordTemplateUtil.writeDocument(document, outputStream)`输出文档

## 注意事项
1. 模板文件必须是`.docx`格式（Office 2007及以上版本）
2. 占位符区分大小写
3. 如果模板中包含表格，占位符可以放在表格单元格中
4. 如果模板加载失败，系统会自动使用代码生成文档
