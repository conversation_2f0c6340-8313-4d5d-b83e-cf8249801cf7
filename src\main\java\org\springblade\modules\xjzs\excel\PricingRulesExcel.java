/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 计价规则表 Excel 实体类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
public class PricingRulesExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * id;主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("id;主键")
	private String id;

	/**
	 * 规则名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("规则名称")
	private String ruleName;

	/**
	 * 规则类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("规则类型")
	private String ruleType;

	/**
	 * 计算公式
	 */
	@ColumnWidth(30)
	@ExcelProperty("计算公式")
	private String calculationFormula;

	/**
	 * 备注
	 */
	@ColumnWidth(20)
	@ExcelProperty("备注")
	private String remark;

	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Long isDeleted;

}
