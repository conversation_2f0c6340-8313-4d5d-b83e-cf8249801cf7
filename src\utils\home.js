import website from '@/config/website';
import store from '@/store';

/**
 * 根据用户角色获取首页路径
 * @param {Object} userInfo 用户信息
 * @returns {String} 首页路径
 */
export function getHomePageByRole(userInfo) {
  console.info(userInfo);
  if (!userInfo || !userInfo.role_name) {
    return '/xjzs/user_home'; // 默认用户首页
  }
  
  const roleName = userInfo.role_name;
  
  // 管理员角色跳转到管理端首页
  if (roleName === 'administrator' || roleName === 'admin') {
    return '/xjzs/manager_home';
  }
  
  // 其他角色跳转到用户端首页
  return '/xjzs/user_home';
}

/**
 * 跳转到用户对应的首页
 * @param {Object} router Vue Router实例
 * @param {Object} userInfo 用户信息（可选，不传则从store获取）
 */
export function navigateToHomePage(router, userInfo = null) {
  const user = userInfo || store.getters.userInfo;
  const homePage = getHomePageByRole(user);
  
  if (router && router.push) {
    router.push(homePage);
  }
  
  return homePage;
}

/**
 * 检查当前用户是否为管理员
 * @param {Object} userInfo 用户信息（可选，不传则从store获取）
 * @returns {Boolean} 是否为管理员
 */
export function isAdmin(userInfo = null) {
  const user = userInfo || store.getters.userInfo;
  
  if (!user || !user.role_name) {
    return false;
  }
  
  const roleName = user.role_name;
  return roleName === 'administrator' || roleName === 'admin';
}

/**
 * 获取用户角色显示名称
 * @param {Object} userInfo 用户信息（可选，不传则从store获取）
 * @returns {String} 角色显示名称
 */
export function getUserRoleDisplayName(userInfo = null) {
  const user = userInfo || store.getters.userInfo;
  
  if (!user || !user.role_name) {
    return '普通用户';
  }
  
  return user.role_name;
}
