package org.springblade.modules.xjzs.service.impl;

import org.springblade.modules.xjzs.service.IAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审计服务工厂
 */
@Component
public class AuditServiceFactory {
    
    private final Map<String, IAuditService> serviceMap = new HashMap<>();
    
    @Autowired
    public AuditServiceFactory(List<IAuditService> services) {
        for (IAuditService service : services) {
            serviceMap.put(service.getServiceType(), service);
        }
    }
    
    /**
     * 根据服务类型获取对应的审计服务
     * 
     * @param serviceType 服务类型
     * @return 审计服务实现
     */
    public IAuditService getAuditService(String serviceType) {
        return serviceMap.get(serviceType);
    }
}
