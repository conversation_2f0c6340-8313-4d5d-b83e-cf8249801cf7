/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入结果类
 * 用于返回Excel导入的结果信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 成功导入的记录数
     */
    private int successCount;
    
    /**
     * 失败的记录数
     */
    private int failCount;
    
    /**
     * 错误信息列表
     */
    private List<ImportErrorInfo> errors = new ArrayList<>();
    
    /**
     * 总消息
     */
    private String message;
    
    /**
     * 添加错误信息
     * 
     * @param errorInfo 错误信息
     */
    public void addError(ImportErrorInfo errorInfo) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(errorInfo);
        this.failCount++;
    }
    
    /**
     * 创建一个成功的导入结果
     * 
     * @param successCount 成功导入的记录数
     * @return ImportResult 导入结果
     */
    public static ImportResult success(int successCount) {
        ImportResult result = new ImportResult();
        result.setSuccess(true);
        result.setSuccessCount(successCount);
        result.setFailCount(0);
        result.setMessage("导入成功，共导入 " + successCount + " 条记录");
        return result;
    }
    
    /**
     * 创建一个失败的导入结果
     * 
     * @param message 错误消息
     * @return ImportResult 导入结果
     */
    public static ImportResult fail(String message) {
        ImportResult result = new ImportResult();
        result.setSuccess(false);
        result.setSuccessCount(0);
        result.setFailCount(0);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 创建一个部分成功的导入结果
     * 
     * @param successCount 成功导入的记录数
     * @param errors 错误信息列表
     * @return ImportResult 导入结果
     */
    public static ImportResult partialSuccess(int successCount, List<ImportErrorInfo> errors) {
        ImportResult result = new ImportResult();
        result.setSuccess(true);
        result.setSuccessCount(successCount);
        result.setFailCount(errors.size());
        result.setErrors(errors);
        result.setMessage("部分导入成功，成功导入 " + successCount + " 条记录，失败 " + errors.size() + " 条记录");
        return result;
    }
}
