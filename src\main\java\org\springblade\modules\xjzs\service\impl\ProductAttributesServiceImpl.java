/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.service.impl;

import org.springblade.modules.xjzs.pojo.entity.ProductAttributesEntity;
import org.springblade.modules.xjzs.pojo.vo.ProductAttributesVO;
import org.springblade.modules.xjzs.excel.ProductAttributesExcel;
import org.springblade.modules.xjzs.mapper.ProductAttributesMapper;
import org.springblade.modules.xjzs.service.IProductAttributesService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 商品属性 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Service
public class ProductAttributesServiceImpl extends BaseServiceImpl<ProductAttributesMapper, ProductAttributesEntity> implements IProductAttributesService {

	@Override
	public IPage<ProductAttributesVO> selectProductAttributesPage(IPage<ProductAttributesVO> page, ProductAttributesVO productAttributes) {
		return page.setRecords(baseMapper.selectProductAttributesPage(page, productAttributes));
	}


	@Override
	public List<ProductAttributesExcel> exportProductAttributes(Wrapper<ProductAttributesEntity> queryWrapper) {
		List<ProductAttributesExcel> productAttributesList = baseMapper.exportProductAttributes(queryWrapper);
		//productAttributesList.forEach(productAttributes -> {
		//	productAttributes.setTypeName(DictCache.getValue(DictEnum.YES_NO, ProductAttributes.getType()));
		//});
		return productAttributesList;
	}

	@Override
	public List<ProductAttributesVO> getProductAttributesByUnit(String feeName, String unit) {
		List<ProductAttributesVO> list=baseMapper.getProductAttributesByUnit(feeName,unit);
		return list;
	}

}
