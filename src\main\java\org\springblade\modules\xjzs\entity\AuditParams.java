package org.springblade.modules.xjzs.entity;


import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 审计参数
 */
@Data
public class AuditParams {
    
    /**
     * 服务类型
     */
    private String serviceType;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 计算方法
     */
    private String calculationMethod;
    
    /**
     * 标准表数据（JSON格式）
     */
    private String tableData;
    
    /**
     * 培训数据（新格式）
     */
    private List<Map<String, Object>> trainingData;
    
    /**
     * 培训地点（线下培训）
     */
    private String trainingLocation;
    
    /**
     * 培训天数
     */
    private Integer trainingDays;
    
    /**
     * 培训人数
     */
    private Integer trainingPeople;
    
    /**
     * 师资职称列表
     */
    private List<Map<String, Object>> teacherTitles;
}
