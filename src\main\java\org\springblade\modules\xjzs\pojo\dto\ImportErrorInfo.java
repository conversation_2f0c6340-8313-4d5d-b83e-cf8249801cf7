/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入错误信息类
 * 用于记录Excel导入过程中的错误信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportErrorInfo {
    
    /**
     * 行号（从1开始，包含表头）
     */
    private int rowNum;
    
    /**
     * 列名
     */
    private String columnName;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 单元格内容
     */
    private String cellContent;
    
    /**
     * 创建一个错误信息对象
     * 
     * @param rowNum 行号
     * @param columnName 列名
     * @param errorMessage 错误信息
     * @param cellContent 单元格内容
     * @return ImportErrorInfo 错误信息对象
     */
    public static ImportErrorInfo of(int rowNum, String columnName, String errorMessage, String cellContent) {
        return new ImportErrorInfo(rowNum, columnName, errorMessage, cellContent);
    }
}
