<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.SupplierInquiryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="supplierInquiryRecordResultMap" type="org.springblade.modules.xjzs.pojo.entity.SupplierInquiryRecordEntity">
        <result column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="inquiry_time" property="inquiryTime"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="quantity" property="quantity"/>
        <result column="supplier" property="supplier"/>
        <result column="inquiry_channel" property="inquiryChannel"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="total_price" property="totalPrice"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectSupplierInquiryRecordPage" resultMap="supplierInquiryRecordResultMap">
        select * from xjzs_supplier_inquiry_record where is_deleted = 0
    </select>


    <select id="exportSupplierInquiryRecord" resultType="org.springblade.modules.xjzs.excel.SupplierInquiryRecordExcel">
        SELECT * FROM xjzs_supplier_inquiry_record ${ew.customSqlSegment}
    </select>

</mapper>
