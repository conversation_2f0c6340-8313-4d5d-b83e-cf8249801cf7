/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.xjzs.pojo.entity.SupplierEntity;
import org.springblade.modules.xjzs.pojo.vo.SupplierVO;
import java.util.Objects;

/**
 * 供应商信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public class SupplierWrapper extends BaseEntityWrapper<SupplierEntity, SupplierVO>  {

	public static SupplierWrapper build() {
		return new SupplierWrapper();
 	}

	@Override
	public SupplierVO entityVO(SupplierEntity supplier) {
		SupplierVO supplierVO = Objects.requireNonNull(BeanUtil.copyProperties(supplier, SupplierVO.class));

		//User createUser = UserCache.getUser(supplier.getCreateUser());
		//User updateUser = UserCache.getUser(supplier.getUpdateUser());
		//supplierVO.setCreateUserName(createUser.getName());
		//supplierVO.setUpdateUserName(updateUser.getName());

		return supplierVO;
	}


}
