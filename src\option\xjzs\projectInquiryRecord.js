export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  delBtn: false,
  editBtn: false,
  addBtn: false,
  menu: false,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "项目编码",
      prop: "projectId",
      type: "input",
      hide: true
    },
    {
      label: "项目名称",
      prop: "projectName",
      type: "input",
      search: true,
      width: 300
    },
    {
      label: "预算金额（万元）",
      prop: "inquiryTime",
      type: "input",
    },
    {
      label: "询价部门",
      prop: "deptName",
      type: "input",
      search: true
    },
    {
      label: "经办人",
      prop: "userName",
      type: "input",
      search: true
    },
    {
      label: "询价时间",
      prop: "inquiryTime",
      type: "datetime"
    },
    {
      label: "名称",
      prop: "productName",
      type: "input"
    },
    {
      label: "技术参数或服务内容",
      prop: "specifications",
      type: "input",
      width: 200
    },
    {
      label: "数量",
      prop: "quantity",
      type: "input"
    },
    {
      label: "询价对象",
      prop: "supplier",
      type: "input"
    },
    {
      label: "询价渠道",
      prop: "inquiryChannel",
      type: "input"
    },
    {
      label: "单价",
      prop: "unitPrice",
      type: "input"
    },
    {
      label: "总价",
      prop: "totalPrice",
      type: "input"
    },
    {
      label: "备注",
      prop: "remark",
      type: "input"
    }
  ]
}