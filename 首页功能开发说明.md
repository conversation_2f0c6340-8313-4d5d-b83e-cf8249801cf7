# 最高限价编审助手 - 首页功能开发说明

## 功能概述

基于原型页面设计，开发了完整的首页功能，包括后端接口和前端页面，实现了统计数据展示和最近项目列表功能。

## 后端开发

### 1. 创建的文件

#### VO类
- `src/main/java/org/springblade/modules/xjzs/pojo/vo/HomeStatisticsVO.java` - 首页统计数据VO
- `src/main/java/org/springblade/modules/xjzs/pojo/vo/RecentProjectVO.java` - 最近项目VO

#### 服务层
- `src/main/java/org/springblade/modules/xjzs/service/IHomeService.java` - 首页服务接口
- `src/main/java/org/springblade/modules/xjzs/service/impl/HomeServiceImpl.java` - 首页服务实现

#### 控制器
- `src/main/java/org/springblade/modules/xjzs/controller/HomeController.java` - 首页控制器

#### 测试类
- `src/test/java/org/springblade/modules/xjzs/controller/HomeControllerTest.java` - 首页控制器测试

### 2. API接口

#### 获取首页统计数据
```
GET /api/xjzs/home/<USER>
```

返回数据结构：
```json
{
  "success": true,
  "data": {
    "notStartedProjects": {
      "companyCount": 124,
      "departmentCount": 24
    },
    "completedProjects": {
      "companyCount": 582,
      "departmentCount": 182
    },
    "intelligentHelp": {
      "companyUsageCount": 15000,
      "departmentUsageCount": 5600
    },
    "completedProjectAmount": {
      "companyAmount": "¥2645万",
      "departmentAmount": "¥645万"
    }
  }
}
```

#### 获取最近项目列表
```
GET /api/xjzs/home/<USER>
```

参数：
- `year`: 年份过滤（可选）
- `keyword`: 搜索关键词（可选）
- `current`: 当前页码
- `size`: 每页大小

返回数据结构：
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "name": "2025年机动车辆采购项目",
        "projectNumber": "XW-2025-0432",
        "dept": "行政部",
        "budget": 1000000,
        "type": "办公类",
        "status": "未开始",
        "createTime": "2025-01-19T10:00:00",
        "updateTime": "2025-01-19T10:00:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10
  }
}
```

#### 查看项目详情
```
GET /api/xjzs/home/<USER>/{id}
```

#### 编辑项目
```
GET /api/xjzs/home/<USER>/{id}/edit
```

## 前端开发

### 1. 创建的文件

#### API文件
- `src/api/xjzs/home.js` - 首页API调用

#### 页面组件
- `src/views/xjzs/home.vue` - 首页Vue组件

#### 路由配置
- 更新了 `src/router/views/index.js`，添加了首页路由

### 2. 技术栈

- Vue 3 + Composition API
- Element Plus UI组件库
- Avue框架
- Axios HTTP客户端

### 3. 功能特性

#### 统计卡片
- 未开始项目统计
- 已完成项目统计  
- 智能帮助使用统计
- 已完成项目金额统计

#### 最近项目列表
- 支持年份筛选
- 支持关键词搜索
- 分页显示
- 项目操作（查看、编辑）

#### 响应式设计
- 适配移动端和桌面端
- 统计卡片自适应布局

## 使用说明

### 1. 启动后端服务

```bash
cd zjyc-zgxjbszs-backend
mvn spring-boot:run
```

### 2. 启动前端服务

```bash
cd zjyc-zgxjbszs-frontend
npm run dev
```

### 3. 访问首页

访问 `http://localhost:3000/xjzs/home` 即可查看首页

## 数据说明

目前使用的是模拟数据，实际部署时需要：

1. 配置数据库连接
2. 根据实际业务需求调整统计逻辑
3. 完善项目状态管理
4. 添加用户权限控制

## 扩展功能

可以进一步扩展的功能：

1. 实时数据刷新
2. 图表展示（柱状图、饼图等）
3. 数据导出功能
4. 更多筛选条件
5. 项目状态流转
6. 消息通知功能

## 注意事项

1. 确保后端服务正常运行
2. 检查API接口路径配置
3. 验证数据库连接
4. 测试各项功能是否正常

## 测试建议

1. 运行后端单元测试
2. 测试API接口响应
3. 验证前端页面显示
4. 测试搜索和分页功能
5. 检查响应式布局
