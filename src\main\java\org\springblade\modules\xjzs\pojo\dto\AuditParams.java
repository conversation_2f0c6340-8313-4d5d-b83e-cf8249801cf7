package org.springblade.modules.xjzs.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 审计计算参数
 */
@Data
@Schema(description = "审计计算参数")
public class AuditParams {
    
    @Schema(description = "服务类型（培训类/工程咨询类/货物类/劳务类/信息类/办公类/综合类）")
    private String serviceType;
    
    @Schema(description = "项目ID")
    private Long projectId;
    
    @Schema(description = "项目名称")
    private String projectName;
    
    @Schema(description = "计算方法（government:政府指导价格法, cost:成本核算法）")
    private String calculationMethod;
    
    @Schema(description = "标准表数据（JSON字符串）")
    private String tableData;
    
    // 培训类特有参数
    @Schema(description = "培训天数")
    private Integer trainingDays;
    
    @Schema(description = "培训人数")
    private Integer trainingPeople;
    
    @Schema(description = "培训地点")
    private String trainingLocation;
    
    @Schema(description = "师资职称列表")
    private List<Map<String, Object>> teacherTitles;
}
