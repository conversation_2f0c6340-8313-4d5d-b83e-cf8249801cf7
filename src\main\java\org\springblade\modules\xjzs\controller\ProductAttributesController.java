/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.xjzs.pojo.entity.ProductAttributesEntity;
import org.springblade.modules.xjzs.pojo.vo.ProductAttributesVO;
import org.springblade.modules.xjzs.excel.ProductAttributesExcel;
import org.springblade.modules.xjzs.wrapper.ProductAttributesWrapper;
import org.springblade.modules.xjzs.service.IProductAttributesService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 商品属性 控制器
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/productAttributes")
@Tag(name = "商品属性", description = "商品属性接口")
public class ProductAttributesController extends BladeController {

	private final IProductAttributesService productAttributesService;

	/**
	 * 商品属性 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入productAttributes")
	public R<ProductAttributesVO> detail(ProductAttributesEntity productAttributes) {
		ProductAttributesEntity detail = productAttributesService.getOne(Condition.getQueryWrapper(productAttributes));
		return R.data(ProductAttributesWrapper.build().entityVO(detail));
	}
	/**
	 * 商品属性 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入productAttributes")
	public R<IPage<ProductAttributesVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> productAttributes, Query query) {
		IPage<ProductAttributesEntity> pages = productAttributesService.page(Condition.getPage(query), Condition.getQueryWrapper(productAttributes, ProductAttributesEntity.class));
		return R.data(ProductAttributesWrapper.build().pageVO(pages));
	}

	/**
	 * 商品属性 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入productAttributes")
	public R<IPage<ProductAttributesVO>> page(ProductAttributesVO productAttributes, Query query) {
		IPage<ProductAttributesVO> pages = productAttributesService.selectProductAttributesPage(Condition.getPage(query), productAttributes);
		return R.data(pages);
	}

	/**
	 * 商品属性 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入productAttributes")
	public R save(@Valid @RequestBody ProductAttributesEntity productAttributes) {
		return R.status(productAttributesService.save(productAttributes));
	}

	/**
	 * 商品属性 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入productAttributes")
	public R update(@Valid @RequestBody ProductAttributesEntity productAttributes) {
		return R.status(productAttributesService.updateById(productAttributes));
	}

	/**
	 * 商品属性 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入productAttributes")
	public R submit(@Valid @RequestBody ProductAttributesEntity productAttributes) {
		return R.status(productAttributesService.saveOrUpdate(productAttributes));
	}

	/**
	 * 商品属性 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(productAttributesService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-productAttributes")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入productAttributes")
	public void exportProductAttributes(@Parameter(hidden = true) @RequestParam Map<String, Object> productAttributes, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ProductAttributesEntity> queryWrapper = Condition.getQueryWrapper(productAttributes, ProductAttributesEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ProductAttributes::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ProductAttributesEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ProductAttributesExcel> list = productAttributesService.exportProductAttributes(queryWrapper);
		ExcelUtil.export(response, "商品属性数据" + DateUtil.time(), "商品属性数据表", list, ProductAttributesExcel.class);
	}

}
