# 导出Word文档功能说明

## 功能概述
本功能实现了使用Word模板导出询价函的功能。系统会根据询价记录的信息，将数据填充到预设的Word模板中，生成最终的询价函文档供用户下载。

## 技术实现
1. 使用Apache POI库处理Word文档（XWPF）
2. 创建了`WordTemplateUtil`工具类，提供模板加载、占位符替换和文档输出功能
3. 在控制器中实现了导出询价函的接口

## 实现流程
1. 用户点击"导出询价函"按钮，前端调用`/xjzs/projectInquiryRecord/export`接口
2. 后端根据询价记录ID获取询价记录和项目信息
3. 加载Word模板文件（如果模板加载失败，则使用代码生成文档）
4. 准备替换的占位符数据
5. 替换模板中的占位符
6. 将生成的Word文档返回给前端

## 容错处理
如果模板文件不存在或加载失败，系统会自动使用代码生成Word文档，确保功能正常运行。

## 扩展性
1. 可以轻松添加新的Word模板
2. 可以扩展`WordTemplateUtil`工具类，支持更多的占位符格式和替换规则
3. 可以添加新的导出接口，支持导出不同类型的文档

## 使用说明
1. 将Word模板文件放置在`resources/doctemplete`目录下
2. 在模板中使用`${placeholder}`格式的占位符
3. 调用导出接口，传入询价记录ID
4. 系统会自动生成Word文档并提供下载
