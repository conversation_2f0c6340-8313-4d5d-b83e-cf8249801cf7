<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.ProjectReportMapper">

    <!-- 检查项目是否已存在报告 -->
    <select id="checkReportExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM xjzs_project_report 
        WHERE project_id = #{projectId} AND is_deleted = 0
    </select>

</mapper>