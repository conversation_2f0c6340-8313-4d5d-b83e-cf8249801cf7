package org.springblade.modules.xjzs.excel;

import lombok.RequiredArgsConstructor;
import org.springblade.core.excel.support.ExcelImporter;
import org.springblade.modules.xjzs.service.ITrainingFeeService;
import java.util.List;

/**
 * 计价标准数据导入类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class TrainingFeeImporter implements ExcelImporter<TrainingFeeExcel> {

    private final ITrainingFeeService service;
    private final Boolean isCovered;

    @Override
    public void save(List<TrainingFeeExcel> data) {
        service.importTrainingFee(data, isCovered);
    }
}