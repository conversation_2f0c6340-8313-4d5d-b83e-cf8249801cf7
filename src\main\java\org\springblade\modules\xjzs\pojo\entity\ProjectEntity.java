/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 项目信息表 实体类
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@TableName("xjzs_project")
@Schema(description = "Project对象")
@EqualsAndHashCode(callSuper = true)
public class ProjectEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 项目名称
	 */
	@Schema(description = "项目名称")
	private String name;
	/**
	 * 项目类别
	 */
	@Schema(description = "项目类别")
	private String type;
	/**
	 * 采购内容
	 */
	@Schema(description = "采购内容")
	private String content;
	/**
	 * 预算金额
	 */
	@Schema(description = "预算金额")
	private Long budget;
	/**
	 * 采购方式
	 */
	@Schema(description = "采购方式")
	private String procurementMethod;
	/**
	 * 实施单位
	 */
	@Schema(description = "实施单位")
	private String dept;
	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;
	/**
	 * 算法类型
	 */
	@Schema(description = "算法类型")
	private String algorithmCategory;
	/**
	 * 项目状态：0-未开始，1-询价中，2-已完成，9-已中止
	 */
	@Schema(description = "项目状态：0-未开始，1-询价中，2-已完成，9-已中止")
	private Integer projectStatus;
	/**
	 * 经办人ID，关联blade_user表
	 */
	@Schema(description = "经办人ID")
	private Long handlerId;

}
