/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.xjzs.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 计算系数 实体类
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Data
@TableName("xjzs_calculation_coefficient")
@Schema(description = "CalculationCoefficient对象")
@EqualsAndHashCode(callSuper = true)
public class CalculationCoefficientEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 类型
	 */
	@Schema(description = "类型")
	private String type;
	/**
	 * 系数名称
	 */
	@Schema(description = "系数名称")
	private String coefficientName;
	/**
	 * 系数类型
	 */
	@Schema(description = "系数类型")
	private String coefficientType;
	/**
	 * 系数值
	 */
	@Schema(description = "系数值")
	private BigDecimal coefficientValue;
	/**
	 * 系数规则
	 */
	@Schema(description = "系数规则")
	private String coefficientRule;
	/**
	 * 描述
	 */
	@Schema(description = "描述")
	private String description;

}
