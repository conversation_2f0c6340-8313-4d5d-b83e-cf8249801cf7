<template>
  <basic-container>

  <div class="page-header">
    <div class="header-left">
      <h2 class="page-title">询价列表</h2>
    </div>
    <div class="header-right">
      <el-button type="primary" icon="el-icon-plus" @click="handleStartInquiry">发起询价</el-button>
    </div>
  </div>
  <avue-crud :option="option"
             v-model:page="page"
             v-model="form"
             :table-loading="loading"
             :data="data"
             :permission="permissionList"
             :before-open="beforeOpen"
             ref="crud"
             @row-update="rowUpdate"
             @row-save="rowSave"
             @row-del="rowDel"
             @selection-change="selectionChange"
             @current-change="currentChange"
             @size-change="sizeChange"
             @refresh-change="refreshChange"
             @on-load="onLoad">
    <!-- 自定义项目名称列 -->
    <template #projectId="{row}">
      <span>{{ getProjectNameForDisplay(row.projectId) }}</span>
    </template>
    <!-- <template #menu-left>
      <el-button type="primary"
                 icon="el-icon-plus"
                 v-if="permission.inquiry_add"
                 @click="handleStartInquiry">发起询价
      </el-button>
    </template> -->

    <!-- 自定义操作列 -->
    <template #menu="{ row }">
      <div>
        <el-button type="text" size="small" icon="el-icon-view" class="action-btn" @click="handleView(row)">查看</el-button>
        <el-button type="text" size="small" icon="el-icon-download" class="action-btn" @click="handleExport(row)">导出询价函</el-button>
        <el-button type="text" size="small" icon="el-icon-upload" class="action-btn" @click="handleUpload(row)">上传回函</el-button>
      </div>
    </template>
  </avue-crud>



  <!-- 发起询价对话框 -->
  <el-dialog
          title="发起询价"
          v-model="dialogVisible.inquiry"
          width="70%"
          :before-close="handleClose">
    <el-form :key="inquiryFormKey" :model="inquiryForm" ref="inquiryFormRef" label-width="120px" :rules="rules">
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目名称" prop="projectId">
            <el-select
                    v-model="inquiryForm.projectId"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请选择或输入项目名称"
                    :remote-method="searchProjects"
                    :loading="projectSearchLoading"
                    @change="handleSelectProject"
                    clearable
                    style="width: 100%">
              <el-option
                      v-for="item in projectSearchResults"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                <div class="project-option-item">
                  <div class="project-name">{{ item.name }}</div>
                  <div class="project-info" v-if="item.content">采购内容: {{ item.content }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目类别" prop="projectType">
            <el-select v-model="inquiryForm.projectType" placeholder="请选择项目类别" style="width: 100%">
              <el-option label="货物类" value="货物类"></el-option>
              <el-option label="服务类" value="服务类"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预算金额" prop="projectBudget">
            <el-input-number
                    v-model="inquiryForm.projectBudget"
                    :min="0"
                    :precision="2"
                    placeholder="请输入预算金额"
                    style="width: calc(50% - 35px)"
            />
            <span style="display: inline-block; width: 30px; text-align: left; font-size: 14px; font-weight: bold; padding-left: 5px">万元</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购方式" prop="procurementMethod">
            <el-select v-model="inquiryForm.procurementMethod" placeholder="请选择采购方式" style="width: 100%">
              <el-option label="公开招标" value="公开招标"></el-option>
              <el-option label="询比" value="询比"></el-option>
              <el-option label="竞争谈判" value="竞争谈判"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止日期" prop="deadline">
            <el-date-picker
                    v-model="inquiryForm.deadline"
                    type="date"
                    placeholder="选择截止日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: calc(50% - 35px)">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 采购内容 - 货物类 -->
      <template v-if="inquiryForm.projectType === '货物类'">
        <el-divider content-position="left">采购内容（货物类）</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称/型号" prop="productName">
              <el-input v-model="inquiryForm.productName" placeholder="请输入产品名称或型号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技术规格" prop="specifications">
              <el-input v-model="inquiryForm.specifications" placeholder="请输入技术规格"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="质量标准" prop="qualityStandard">
              <el-input v-model="inquiryForm.qualityStandard" placeholder="请输入质量标准"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="inquiryForm.quantity" :min="1" placeholder="请输入数量" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 采购内容 - 服务类 -->
      <template v-if="inquiryForm.projectType === '服务类'">
        <el-divider content-position="left">采购内容（服务类）</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务名称" prop="serviceName">
              <el-input v-model="inquiryForm.serviceName" placeholder="请输入服务名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务内容" prop="serviceContent">
              <el-input v-model="inquiryForm.serviceContent" placeholder="请输入服务内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务要求" prop="serviceRequirements">
              <el-input v-model="inquiryForm.serviceRequirements" placeholder="请输入服务要求"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务期限" prop="servicePeriod">
              <el-input-number
                      v-model="inquiryForm.servicePeriod"
                      :min="0"
                      :precision="1"
                      placeholder="请输入服务期限（如：1年）"
                      style="width: calc(35% - 35px)"
              />
              <span style="display: inline-block; width: 30px; text-align: left; font-size: 14px; font-weight: bold; padding-left: 5px">年</span>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 联系信息 -->
      <el-divider content-position="left">联系信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactName">
            <el-input v-model="inquiryForm.contactName" placeholder="请输入联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="inquiryForm.contactPhone" placeholder="请输入联系电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
    <span class="dialog-footer">
      <el-button @click="dialogVisible.inquiry = false">取 消</el-button>
      <el-button type="primary" @click="submitInquiry">提 交</el-button>
    </span>
    </template>
  </el-dialog>

  <!-- 上传报价回函对话框 -->
  <el-dialog
          title="上传报价回函"
          v-model="dialogVisible.upload"
          width="30%"
          :before-close="handleClose">
    <el-upload
            class="upload-demo"
            drag
            action="/api/xjzs/projectInquiryRecord/upload-quotation"
            :headers="uploadHeaders"
            :data="uploadData"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload">
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip">只能上传 doc/docx 文件，且不超过 10MB</div>
      </template>
    </el-upload>
    <template #footer>
    <span class="dialog-footer">
      <el-button @click="dialogVisible.upload = false">取 消</el-button>
    </span>
    </template>
  </el-dialog>

  <!-- 询价详情对话框 -->
  <el-dialog
          title="查看"
          v-model="dialogVisible.supplier"
          width="70%"
          :before-close="handleClose">

    <!-- 询价基本信息 -->
    <div v-if="inquiryDetail" class="inquiry-detail-section">
      <el-divider content-position="left">项目基本信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="detail-item">
            <span class="detail-label">项目名称：</span>
            <span class="detail-value">{{ inquiryDetail.projectName || '未知项目' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <span class="detail-label">项目类别：</span>
            <span class="detail-value">{{ inquiryDetail.projectType || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <span class="detail-label">预算金额：</span>
            <span class="detail-value budget-highlight">{{ formatBudget(inquiryDetail.projectBudget) }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <span class="detail-label">采购方式：</span>
            <span class="detail-value">{{ inquiryDetail.procurementMethod || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <span class="detail-label">截止日期：</span>
            <span class="detail-value">{{ formatDate(inquiryDetail.deadline) }}</span>
          </div>
        </el-col>
      </el-row>

      <!-- 采购内容 - 货物类 -->
      <template v-if="inquiryDetail.projectType === '货物类'">
        <el-divider content-position="left">采购内容（货物类）</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">产品名称/型号：</span>
              <span class="detail-value">{{ inquiryDetail.productName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">技术规格：</span>
              <span class="detail-value">{{ inquiryDetail.specifications || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">质量标准：</span>
              <span class="detail-value">{{ inquiryDetail.quantityStandard || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">数量：</span>
              <span class="detail-value">{{ inquiryDetail.quantity || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </template>

      <!-- 采购内容 - 服务类 -->
      <template v-if="inquiryDetail.projectType === '服务类'">
        <el-divider content-position="left">采购内容（服务类）</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">服务名称：</span>
              <span class="detail-value">{{ inquiryDetail.serviceName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">服务内容：</span>
              <span class="detail-value">{{ inquiryDetail.serviceContent || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">服务要求：</span>
              <span class="detail-value">{{ inquiryDetail.serviceRequirements || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">服务期限：</span>
              <span class="detail-value">{{ inquiryDetail.servicePeriod || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </template>

      <el-divider content-position="left">联系信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="detail-item">
            <span class="detail-label">联系人：</span>
            <span class="detail-value">{{ inquiryDetail.contactName || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="detail-item">
            <span class="detail-label">联系电话：</span>
            <span class="detail-value">{{ inquiryDetail.contactPhone || '-' }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 报价信息 -->
    <el-divider content-position="left">报价信息</el-divider>
    <div v-if="inquiryDetail && inquiryDetail.quotationCompany" class="quotation-info">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="detail-item">
            <span class="detail-label">报价公司：</span>
            <span class="detail-value">{{ inquiryDetail.quotationCompany }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="detail-item">
            <span class="detail-label">报价总金额：</span>
            <span class="detail-value price-highlight">¥{{ inquiryDetail.quotationTotalAmount }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="detail-item">
            <span class="detail-label">报价时间：</span>
            <span class="detail-value">{{ formatDate(inquiryDetail.quotationTime) }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-else class="no-quotation">
      <el-empty description="暂无报价信息" :image-size="100"></el-empty>
    </div>

    <template #footer>
    <span class="dialog-footer">
      <el-button @click="dialogVisible.supplier = false">关 闭</el-button>
    </span>
    </template>
  </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/xjzs/projectInquiryRecord";
  import {exportInquiry, exportInquiryReply} from "@/api/xjzs/inquiry";
  import {searchProjects, getProjectById} from "@/api/xjzs/search";
  import option from "@/option/xjzs/inquiry";
  import {mapGetters} from "vuex";
  import {getToken} from '@/utils/auth';
  import {formatDateNow} from "@/utils/date";
  import {downloadFile} from "@/utils/util";
  import website from '@/config/website';
  import { Base64 } from 'js-base64';

  export default {
    name: "Inquiry",
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: [],
        dialogVisible: {
          inquiry: false,
          upload: false,
          supplier: false
        },
        inquiryForm: {
          projectId: '', // 项目 ID
          projectName: '', // 项目名称，用于显示和提交
          projectType: '', // 项目类别：货物类/服务类
          projectBudget: null, // 预算金额（万元）
          procurementMethod: '', // 采购方式
          // 货物类字段
          productName: '', // 产品名称/型号
          specifications: '', // 技术规格
          quantity: 1, // 数量
          qualityStandard: '', // 质量标准
          // 服务类字段
          serviceName: '', // 服务名称
          serviceContent: '', // 服务内容
          servicePeriod: '', // 服务期限
          serviceRequirements: '', // 服务要求
          // 报价要求
          unitPriceRequirement: '', // 单价要求
          totalPriceRequirement: '', // 总价要求
          requiredDocuments: '', // 需附文件
          // 联系信息
          contactName: '', // 联系人
          contactPhone: '', // 联系电话
          deadline: '', // 截止日期
          otherNotes: '', // 其他说明
          // 保留原有字段以兼容后端
          packagingRequirements: '',
          deliveryPeriod: ''
        },
        rules: {
          projectId: [
            { required: true, message: '请选择项目', trigger: 'change' }
          ],
          projectType: [
            { required: true, message: '请选择项目类别', trigger: 'change' }
          ],
          projectBudget: [
            { required: true, message: '请输入预算金额', trigger: 'blur' }
          ],
          procurementMethod: [
            { required: true, message: '请选择采购方式', trigger: 'change' }
          ],
          deadline: [
            { required: true, message: '请选择截止日期', trigger: 'change' }
          ],
          contactName: [
            { required: true, message: '请输入联系人', trigger: 'blur' }
          ],
          contactPhone: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
          ]
        },
        currentRow: null,
        supplierList: [],
        inquiryDetail: null,
        uploadHeaders: {},
        uploadData: {
          inquiryId: null
        },
        // 项目搜索相关
        projectSearchLoading: false,
        projectSearchResults: [],
        projectSearchTimeout: null,
        // 项目缓存，用于存储项目ID和名称的映射关系
        projectCache: {}
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.inquiry_add, false),
          viewBtn: this.validData(this.permission.inquiry_view, false),
          delBtn: this.validData(this.permission.inquiry_delete, false),
          editBtn: this.validData(this.permission.inquiry_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      // 组件挂载时加载项目列表
      this.searchProjects('');
      // 初始化上传认证头
      this.updateUploadHeaders();
    },
    watch: {
      // 监听项目类别变化，更新验证规则
      'inquiryForm.projectType'(newVal, oldVal) {
        if (newVal !== oldVal) {
          // 清空相关字段
          if (newVal === '货物类') {
            // 清空服务类字段
            this.inquiryForm.serviceName = '';
            this.inquiryForm.serviceContent = '';
            this.inquiryForm.serviceRequirements = '';
            this.inquiryForm.servicePeriod = '';
          } else if (newVal === '服务类') {
            // 清空货物类字段
            this.inquiryForm.productName = '';
            this.inquiryForm.specifications = '';
            this.inquiryForm.qualityStandard = '';
            this.inquiryForm.quantity = 1;
          }

          // 更新验证规则
          this.rules = this.getDynamicRules();
          this.inquiryFormKey = Math.random(); // 强制刷新表单
        }
      }
    },
    methods: {
      rowSave(row, done, loading) {
        // 将row数据转换为projectInquiryRecord格式
        const projectInquiryRecord = this.convertToProjectInquiryRecord(row);
        add(projectInquiryRecord).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(error => {
          loading();
          console.error('保存项目询价记录失败:', error);
        });
      },
      rowUpdate(row, _index, done, loading) {
        // 将row数据转换为projectInquiryRecord格式
        const projectInquiryRecord = this.convertToProjectInquiryRecord(row);
        update(projectInquiryRecord).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }).catch(error => {
          loading();
          console.error('更新项目询价记录失败:', error);
        });
      },
      // 将表单数据转换为projectInquiryRecord格式
      convertToProjectInquiryRecord(formData) {
        return {
          // 基本信息
          id: formData.id,
          projectId: formData.projectId, // 使用选择的项目 ID
          projectName: formData.projectName, // 保存项目名称
          projectType: formData.projectType, // 项目类别
          projectBudget: formData.projectBudget, // 预算金额
          procurementMethod: formData.procurementMethod, // 采购方式
          // 货物类字段
          productName: formData.productName,
          specifications: formData.specifications,
          quantity: formData.quantity,
          quantityStandard: formData.qualityStandard,
          // 服务类字段
          serviceName: formData.serviceName,
          serviceContent: formData.serviceContent,
          servicePeriod: formData.servicePeriod,
          serviceRequirements: formData.serviceRequirements,
          // 联系信息
          contactName: formData.contactName,
          contactPhone: formData.contactPhone,
          deadline: formData.deadline
        };
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          })
          .catch(error => {
            console.error('删除项目询价记录失败:', error);
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          })
          .catch(error => {
            console.error('批量删除项目询价记录失败:', error);
          });
      },
      handleStartInquiry() {
        // 重置表单数据
        this.inquiryForm = {
          projectId: '',
          projectName: '',
          projectType: '',
          projectBudget: null,
          procurementMethod: '',
          productName: '',
          specifications: '',
          quantity: 1,
          qualityStandard: '',
          serviceName: '',
          serviceContent: '',
          servicePeriod: '',
          serviceRequirements: '',
          unitPriceRequirement: '',
          totalPriceRequirement: '',
          requiredDocuments: '',
          contactName: '',
          contactPhone: '',
          deadline: '',
          otherNotes: '',
          packagingRequirements: '',
          deliveryPeriod: ''
        };

        // 初始化验证规则
        this.rules = this.getDynamicRules();
        this.inquiryFormKey = Math.random(); // 强制刷新表单
        this.dialogVisible.inquiry = true;
      },
      handleView(row) {
        this.currentRow = row;
        this.loadInquiryDetail(row.id);
        this.dialogVisible.supplier = true;
      },
      handleExport(row) {
        this.$confirm("是否导出询价函?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          const projectName = this.getProjectNameForDisplay(row.projectId);
          exportInquiry(row.id).then(res => {
            downloadFile(res.data, `询价函_${projectName}_${formatDateNow()}.docx`);
          });
          exportInquiryReply(row.id).then(res => {
            downloadFile(res.data, `回函_${projectName}_${formatDateNow()}.docx`);
          });
        });
      },
      handleUpload(row) {
        console.log('上传行数据:', row)
        this.currentRow = row;
        this.uploadData.inquiryId = row.id;
        // 动态更新认证头
        this.updateUploadHeaders();
        console.log('上传认证头:', this.uploadHeaders);
        console.log('上传数据:', this.uploadData);
        this.dialogVisible.upload = true;
      },
      // 更新上传认证头
      updateUploadHeaders() {
        const token = getToken();
        if (token) {
          this.uploadHeaders = {
            'Blade-Auth': 'bearer ' + token,
            'Blade-Requested-With': 'BladeHttpRequest',
            'Authorization': `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`
          };
        }
      },
      handleClose(done) {
        done();
      },
      // 动态验证规则
      getDynamicRules() {
        const baseRules = {
          projectId: [
            { required: true, message: '请选择项目', trigger: 'change' }
          ],
          projectType: [
            { required: true, message: '请选择项目类别', trigger: 'change' }
          ],
          projectBudget: [
            { required: true, message: '请输入预算金额', trigger: 'blur' }
          ],
          procurementMethod: [
            { required: true, message: '请选择采购方式', trigger: 'change' }
          ],
          deadline: [
            { required: true, message: '请选择截止日期', trigger: 'change' }
          ],
          contactName: [
            { required: true, message: '请输入联系人', trigger: 'blur' }
          ],
          contactPhone: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
          ]
        };

        // 根据项目类别添加动态验证规则
        if (this.inquiryForm.projectType === '货物类') {
          baseRules.productName = [
            { required: true, message: '请输入产品名称/型号', trigger: 'blur' }
          ];
          baseRules.specifications = [
            { required: true, message: '请输入技术规格', trigger: 'blur' }
          ];
          baseRules.qualityStandard = [
            { required: true, message: '请输入质量标准', trigger: 'blur' }
          ];
          baseRules.quantity = [
            { required: true, message: '请输入数量', trigger: 'blur' }
          ];
        } else if (this.inquiryForm.projectType === '服务类') {
          baseRules.serviceName = [
            { required: true, message: '请输入服务名称', trigger: 'blur' }
          ];
          baseRules.serviceContent = [
            { required: true, message: '请输入服务内容', trigger: 'blur' }
          ];
          baseRules.serviceRequirements = [
            { required: true, message: '请输入服务要求', trigger: 'blur' }
          ];
          baseRules.servicePeriod = [
            { required: true, message: '请输入服务期限', trigger: 'blur' }
          ];
        }

        return { ...baseRules };
      },
      submitInquiry() {
        // 更新验证规则
        this.rules = this.getDynamicRules();
        this.$refs.inquiryFormRef.validate((valid) => {
          if (valid) {
            // 将inquiryForm数据转换为projectInquiryRecord格式
            const projectInquiryRecord = this.convertToProjectInquiryRecord(this.inquiryForm);

            add(projectInquiryRecord).then(() => {
              this.dialogVisible.inquiry = false;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "询价发起成功!"
              });
              // 重置表单
              this.$refs.inquiryFormRef.resetFields();
              // 重置项目类别
              this.inquiryForm.projectType = '';
            }).catch(error => {
              console.error('提交项目询价记录失败:', error);
            });
          }
        });
      },
      beforeUpload(file) {
        const isWord = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                       file.type === 'application/msword';
        const isLt10M = file.size / 1024 / 1024 < 10;

        if (!isWord) {
          this.$message.error('上传文件只能是 doc/docx 格式!');
          return false;
        }
        if (!isLt10M) {
          this.$message.error('上传文件大小不能超过 10MB!');
          return false;
        }
        return true;
      },
      handleUploadSuccess(response, _file, _fileList) {
        console.log('上传响应:', response);
        if (response && response.success) {
          this.$message.success(response.msg || '报价回函上传成功!');
          this.dialogVisible.upload = false;
          this.onLoad(this.page);
        } else {
          this.$message.error(response.msg || '上传处理失败');
        }
      },
      handleUploadError(err, _file, _fileList) {
        console.error('上传错误详情:', err);
        console.error('错误状态:', err.status);
        console.error('错误响应:', err.response);

        let errorMessage = '上传失败';
        if (err.status === 401) {
          errorMessage = '请求未授权，请重新登录';
        } else if (err.status === 403) {
          errorMessage = '没有权限访问该接口';
        } else if (err.status === 404) {
          errorMessage = '接口不存在';
        } else if (err.status === 500) {
          errorMessage = '服务器内部错误';
        } else if (err.message) {
          errorMessage = '上传失败: ' + err.message;
        }

        this.$message.error(errorMessage);
      },
      // 加载询价详情和报价信息
      loadInquiryDetail(inquiryId) {
        // 加载询价详情（包含报价信息）
        getDetail(inquiryId).then(res => {
          if (res.data && res.data.data) {
            this.inquiryDetail = res.data.data;
            console.log('询价详情:', this.inquiryDetail);
          }
        }).catch(error => {
          console.error('获取询价详情失败:', error);
          this.$message.error('获取询价详情失败');
        });
      },

      // 格式化预算金额
      formatBudget(budget) {
        if (!budget) return '-';
        return `${budget.toFixed(2)}万元`;
      },

      // 格式化日期，只显示年月日
      formatDate(dateString) {
        if (!dateString) return '-';
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) return '-';

          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');

          return `${year}-${month}-${day}`;
        } catch (error) {
          console.error('日期格式化错误:', error);
          return '-';
        }
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      // 项目名称搜索方法
      searchProjects(query) {
        if (query !== '') {
          this.projectSearchLoading = true;
          // 清除之前的定时器
          if (this.projectSearchTimeout) {
            clearTimeout(this.projectSearchTimeout);
          }
          // 设置定时器，防止频繁请求
          this.projectSearchTimeout = setTimeout(() => {
            searchProjects(query).then(res => {
              this.projectSearchResults = res.data.data.records || [];
              this.projectSearchLoading = false;
            }).catch(error => {
              console.error('搜索项目失败:', error);
              this.projectSearchLoading = false;
            });
          }, 300);
        } else {
          // 如果查询条件为空，加载所有项目
          this.projectSearchLoading = true;
          searchProjects('').then(res => {
            this.projectSearchResults = res.data.data.records || [];
            this.projectSearchLoading = false;
          }).catch(error => {
            console.error('加载项目列表失败:', error);
            this.projectSearchLoading = false;
          });
        }
      },
      // 选择项目方法
      handleSelectProject(projectId) {
        if (projectId) {
          // 从搜索结果中找到选中的项目
          const selectedProject = this.projectSearchResults.find(item => item.id === projectId);
          if (selectedProject) {
            this.inquiryForm.projectName = selectedProject.name;
            this.inquiryForm.procurementMethod = selectedProject.procurementMethod;
            this.inquiryForm.projectType = selectedProject.type == '货物' ? '货物类' : '服务类'; // TODO
            this.inquiryForm.projectBudget = selectedProject.budget / 10000; // 万元
            if(this.inquiryForm.projectType == '服务类') {
              this.inquiryForm.serviceContent = selectedProject.content.endsWith('。') ? selectedProject.content.slice(0, -1) : selectedProject.content;
            }
          }
        } else {
          // 清空选择时，同时清空项目名称
          this.inquiryForm.projectName = '';
          this.inquiryForm.procurementMethod = '';
          this.inquiryForm.projectType = '';
          this.inquiryForm.projectBudget = '';
          this.inquiryForm.serviceContent = '';
        }
      },
      // 用于表格显示的项目名称
      getProjectNameForDisplay(projectId) {
        if (!projectId) return '未知项目';

        // 如果缓存中已有该项目名称，直接返回
        if (this.projectCache[projectId]) {
          return this.projectCache[projectId];
        }

        // 如果缓存中没有，则调用获取方法并返回加载中的提示
        this.getProjectName(projectId);
        return '加载中...';
      },

      // 根据项目ID获取项目名称并缓存
      getProjectName(projectId) {
        // 如果缓存中已有该项目名称，直接返回
        if (this.projectCache[projectId]) {
          return this.projectCache[projectId];
        }

        // 如果缓存中没有，则调用API获取
        getProjectById(projectId).then(res => {
          if (res.data && res.data.data) {
            const projectName = res.data.data.name;
            // 将项目名称存入缓存
            this.projectCache[projectId] = projectName;
            // 强制更新视图
            this.$forceUpdate();
          }
        }).catch(error => {
          console.error('获取项目详情失败:', error);
          // 如果获取失败，则将项目ID作为名称存入缓存
          this.projectCache[projectId] = `项目 ID: ${projectId}`;
        });

        // 返回加载中的提示
        return '加载中...';
      },

      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, params).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          // 如果有数据，则遍历每条记录，获取项目名称
          if (this.data && this.data.length > 0) {
            this.data.forEach(item => {
              if (item.projectId && !item.projectName) {
                // 如果有projectId但没有projectName，则获取项目名称
                this.getProjectName(item.projectId);
              }
            });
          }

          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载项目询价记录失败:', error);
          this.loading = false;
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
.page-header {
  margin: 10px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.header-right {
  margin-left: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.page-desc {
  font-size: 14px;
  color: #666;
}

.steps-container {
  background-color: #f8f9fb;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.inquiry-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-item {
  flex: 1;
  text-align: center;
}

.step-text {
  font-size: 14px;
  color: #606266;
}

.step-item.active .step-text {
  color: #409EFF;
  font-weight: 500;
}

.step-arrow {
  color: #c0c4cc;
  margin: 0 5px;
}



.mt-4 {
  margin-top: 16px;
}

.inquiry-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 20px;
}

.inquiry-content {
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.full-width-table {
  width: 100%;
}

.full-width-table :deep(.el-table) {
  width: 100% !important;
}

.action-btn {
  color: #409EFF;
  margin: 0 5px;
}

.project-option-item {
  width: 500px;
  padding: 5px 0;
}

.project-name {
  font-weight: bold;
  color: #409EFF;
}

.project-info {
  font-size: 12px;
  color: #999;
  margin-top: 3px;
}

/* 覆盖el-select的默认样式 */
:deep(.el-select-dropdown__item) {
  padding: 0 10px;
  height: auto;
  line-height: 1.5;
  padding-top: 8px;
  padding-bottom: 8px;
  width: 70% !important;
}

/* 询价详情样式 */
.inquiry-detail-section {
  margin-bottom: 20px;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  min-width: 60px;
  flex-shrink: 0;
}

.detail-value {
  color: #303133;
  word-break: break-all;
  font-weight: bold;
}

.price-highlight {
  color: #E6A23C;
  font-weight: 600;
}

.budget-highlight {
  color: #67C23A;
  font-weight: 600;
}

.divider-value {
  font-weight: bold;
}


.quotation-info {
  margin-bottom: 20px;
}

.no-quotation {
  text-align: center;
  padding: 40px 0;
}

/* 发起询价对话框样式 */
.inquiry-form-section {
  margin-bottom: 20px;
}

.inquiry-form-section .el-divider {
  margin: 20px 0 15px 0;
}

.inquiry-form-section .el-divider__text {
  font-weight: 600;
  color: #409EFF;
}

/* 项目类别选择样式 */
.project-type-selector {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

/* 动态表单区域样式 */
.dynamic-form-section {
  background-color: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin: 10px 0;
}

/* 报价要求区域样式 */
.quotation-requirements {
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  padding: 15px;
  margin: 10px 0;
}

/* 表单项标签样式增强 */
.el-form-item__label {
  font-weight: 500;
}

/* 必填项标识 */
.el-form-item.is-required .el-form-item__label:before {
  color: #f56c6c;
}

</style>
