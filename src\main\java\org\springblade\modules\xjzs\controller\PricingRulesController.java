/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.xjzs.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.xjzs.pojo.entity.PricingRulesEntity;
import org.springblade.modules.xjzs.pojo.vo.PricingRulesVO;
import org.springblade.modules.xjzs.excel.PricingRulesExcel;
import org.springblade.modules.xjzs.wrapper.PricingRulesWrapper;
import org.springblade.modules.xjzs.service.IPricingRulesService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 计价规则表 控制器
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("/xjzs/pricing-rules")
@Tag(name = "计价规则表", description = "计价规则表接口")
@Slf4j
public class PricingRulesController extends BladeController {

    private final IPricingRulesService pricingRulesService;

    /**
     * 计价规则表 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "详情", description = "传入pricingRules")
    public R<PricingRulesVO> detail(PricingRulesEntity pricingRules) {
        PricingRulesEntity detail = pricingRulesService.getOne(Condition.getQueryWrapper(pricingRules));
        return R.data(PricingRulesWrapper.build().entityVO(detail));
    }

    /**
     * 计价规则表 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "分页", description = "传入pricingRules")
    public R<IPage<PricingRulesVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> pricingRules, Query query) {
        IPage<PricingRulesEntity> pages = pricingRulesService.page(Condition.getPage(query),
                Condition.getQueryWrapper(pricingRules, PricingRulesEntity.class));
        return R.data(PricingRulesWrapper.build().pageVO(pages));
    }

    /**
     * 计价规则表 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "分页", description = "传入pricingRules")
    public R<IPage<PricingRulesVO>> page(PricingRulesVO pricingRules, Query query) {
        IPage<PricingRulesVO> pages = pricingRulesService.selectPricingRulesPage(Condition.getPage(query), pricingRules);
        return R.data(pages);
    }

    /**
     * 计价规则表 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "新增", description = "传入pricingRules")
    public R save(@Valid @RequestBody PricingRulesEntity pricingRules) {
        return R.status(pricingRulesService.save(pricingRules));
    }

    /**
     * 计价规则表 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "修改", description = "传入pricingRules")
    public R update(@Valid @RequestBody PricingRulesEntity pricingRules) {
        return R.status(pricingRulesService.updateById(pricingRules));
    }

    /**
     * 计价规则表 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "新增或修改", description = "传入pricingRules")
    public R submit(@Valid @RequestBody PricingRulesEntity pricingRules) {
        return R.status(pricingRulesService.saveOrUpdate(pricingRules));
    }

    /**
     * 计价规则表 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "逻辑删除", description = "传入ids")
    public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
        return R.status(pricingRulesService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 获取所有计价规则列表（不分页）
     */
    @GetMapping("/all")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "获取所有计价规则", description = "获取所有计价规则列表，用于下拉选择")
    public R<List<PricingRulesVO>> getAllPricingRules() {
        List<PricingRulesEntity> list = pricingRulesService.list();
        return R.data(PricingRulesWrapper.build().listVO(list));
    }

    /**
     * 导出数据
     */
    @PreAuth(RoleConstant.HAS_ROLE_ADMIN)
    @GetMapping("/export-pricing-rules")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "导出数据", description = "传入pricingRules")
    public void exportPricingRules(@Parameter(hidden = true) @RequestParam Map<String, Object> pricingRules, BladeUser bladeUser,
            HttpServletResponse response) {
        QueryWrapper<PricingRulesEntity> queryWrapper = Condition.getQueryWrapper(pricingRules, PricingRulesEntity.class);
        List<PricingRulesExcel> list = pricingRulesService.exportPricingRules(queryWrapper);
        ExcelUtil.export(response, "计价规则表数据" + DateUtil.time(), "计价规则表数据表", list, PricingRulesExcel.class);
    }

}
