package org.springblade.modules.dify.resp;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * Dify工作流流式调用响应.
 */
@Data
public class WorkflowStreamResponse implements Serializable {

    /**
     * 事件类型: workflow_started, node_started, node_finished, text_chunk, workflow_finished, error
     */
    private String event;

    /**
     * 工作流运行ID
     */
    private String workflow_run_id;

    /**
     * 任务ID
     */
    private String task_id;

    /**
     * 数据内容，包含具体的节点信息、输出等
     */
    private JSONObject data;

    /**
     * 错误信息（当event为error时）
     */
    private String error;

    /**
     * 错误描述（当event为error时）
     */
    private String description;

    // 为了兼容前端，添加一些便捷方法
    
    /**
     * 获取文本内容（从data中提取）
     */
    public String getAnswer() {
        if (data != null) {
            // 对于text_chunk事件，文本内容在data.text中
            if ("text_chunk".equals(event) && data.containsKey("text")) {
                return data.getString("text");
            }

            // 对于node_finished事件，检查outputs
            if ("node_finished".equals(event) && data.containsKey("outputs")) {
                JSONObject outputs = data.getJSONObject("outputs");
                if (outputs != null) {
                    // 尝试不同的字段名
                    if (outputs.containsKey("text")) {
                        return outputs.getString("text");
                    }
                    if (outputs.containsKey("result")) {
                        return outputs.getString("result");
                    }
                    if (outputs.containsKey("answer")) {
                        return outputs.getString("answer");
                    }
                }
            }

            // 对于workflow_finished事件，检查data.outputs
            if ("workflow_finished".equals(event) && data.containsKey("outputs")) {
                Object outputs = data.get("outputs");
                if (outputs instanceof String) {
                    return (String) outputs;
                } else if (outputs instanceof JSONObject) {
                    JSONObject outputsObj = (JSONObject) outputs;

                    // 检查是否有output字段，且为数组（多个商品结果的情况）
                    if (outputsObj.containsKey("output")) {
                        Object outputObj = outputsObj.get("output");
                        if (outputObj instanceof JSONArray) {
                            JSONArray outputArray = (JSONArray) outputObj;
                            if (outputArray.size() > 0) {
                                StringBuilder result = new StringBuilder();

                                // 检查是否是货物类计算（包含思维链）
                                boolean isGoodsCalculation = false;
                                for (int i = 0; i < outputArray.size(); i++) {
                                    Object item = outputArray.get(i);
                                    if (item != null && item.toString().contains("<think>")) {
                                        isGoodsCalculation = true;
                                        break;
                                    }
                                }

                                // 根据计算类型决定是否添加分隔符
                                for (int i = 0; i < outputArray.size(); i++) {
                                    if (i > 0 && isGoodsCalculation) {
                                        // 货物类计算：添加分隔符
                                        result.append("\n\n").append("=".repeat(50)).append("\n\n");
                                    }
                                    Object item = outputArray.get(i);
                                    if (item != null) {
                                        result.append(item.toString());
                                    }
                                }
                                return result.toString();
                            }
                        }
                    }

                    // 尝试其他常见字段
                    if (outputsObj.containsKey("text")) {
                        return outputsObj.getString("text");
                    }
                    if (outputsObj.containsKey("result")) {
                        return outputsObj.getString("result");
                    }
                }
            }
        }
        return "";
    }

    /**
     * 获取创建时间
     */
    public Long getCreated_at() {
        if (data != null && data.containsKey("created_at")) {
            return data.getLong("created_at");
        }
        return System.currentTimeMillis() / 1000; // 返回当前时间戳（秒）
    }

    /**
     * 设置错误信息（用于错误处理）
     */
    public void setErrorInfo(String errorMessage) {
        this.event = "error";
        this.error = errorMessage;
        this.description = errorMessage;
    }
}
