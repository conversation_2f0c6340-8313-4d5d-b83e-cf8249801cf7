import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/xjzs/supplier/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/xjzs/supplier/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/xjzs/supplier/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/xjzs/supplier/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/xjzs/supplier/submit',
    method: 'post',
    data: row
  })
}

