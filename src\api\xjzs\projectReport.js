import axios from 'axios';

export const checkReportExists = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/checkExists',
    method: 'get',
    params: {
      projectId
    }
  });
};
export const checkReport = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/checkReport',
    method: 'get',
    params: {
      projectId
    }
  });
};


export const saveProjectReport = (data) => {
  return axios({
    url: '/xjzs/projectReport/save',
    method: 'post',
    data
  });
};

export const getProjectReport = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/detail',
    method: 'get',
    params: {
      projectId
    }
  });
};

export const exportProjectReport = (projectId) => {
  return axios({
    url: '/xjzs/projectReport/export',
    method: 'get',
    responseType: 'blob',
    params: {
      projectId
    }
  });
};

export const exportProjectReportPdf = (projectId,tableItems,tabledata) => {
  return axios({
    url: '/xjzs/projectReport/export-pdf',
    method: 'get',
    responseType: 'blob',
    params: {
      projectId,tableItems,tabledata
    }
  });
};

// 新的合规性检查接口 - 使用Dify agent（流式处理）
export const checkComplianceWithDify = (projectId, onProgress) => {
  return new Promise((resolve, reject) => {
    let allContent = ''; // 累积所有内容

    fetch(`/api/xjzs/projectReport/checkComplianceWithDify?projectId=${projectId}`, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream'
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      function readStream() {
        return reader.read().then(({ done, value }) => {
          if (done) {
            // 流结束，返回累积的内容
            resolve({
              data: {
                success: true,
                detail: allContent || '合规性检查完成',
                raw_response: { answer: allContent }
              }
            });
            return;
          }

          // 处理流数据
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop();

          for (const line of lines) {
            if (line.trim()) {
              try {
                // 处理Server-Sent Events格式，移除"data:"前缀
                let jsonStr = line.trim();
                if (jsonStr.startsWith('data:')) {
                  jsonStr = jsonStr.substring(5); // 移除"data:"前缀
                }

                // 跳过空行或非JSON行
                if (!jsonStr || jsonStr === '[DONE]') {
                  continue;
                }

                const data = JSON.parse(jsonStr);

                // 调用进度回调
                if (onProgress) {
                  onProgress(data);
                }

                // 累积内容 - 处理text_chunk事件（实时流式内容）
                if (data.event === 'text_chunk' && data.data && data.data.text) {
                  allContent += data.data.text;
                }

                // 处理最终结果
                if (data.event === 'workflow_finished') {
                  // 如果有最终输出，使用最终输出；否则使用累积的内容
                  if (data.data && data.data.outputs && data.data.outputs.text) {
                    allContent = data.data.outputs.text;
                  }
                }
              } catch (e) {
                // 静默处理解析错误
              }
            }
          }

          return readStream();
        });
      }

      return readStream();
    })
    .catch(error => {
      reject(error);
    });
  });
};

// 工程类合规性检查接口 - 使用Dify agent（流式处理）
export const checkEngineeringComplianceWithDify = (projectId, onProgress) => {
  return new Promise((resolve, reject) => {
    let allContent = ''; // 累积所有内容

    fetch(`/api/xjzs/projectReport/checkEngineeringComplianceWithDify?projectId=${projectId}`, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream'
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      function readStream() {
        return reader.read().then(({ done, value }) => {
          if (done) {
            // 流结束，返回与培训类相同的结构
            resolve({
              data: {
                success: true,
                detail: allContent || '工程类合规性检查完成',
                raw_response: { answer: allContent }
              }
            });
            return;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop(); // 保留最后一个可能不完整的行

          for (const line of lines) {
            if (line.trim()) {
              try {
                // 处理Server-Sent Events格式，移除"data:"前缀
                let jsonStr = line.trim();
                if (jsonStr.startsWith('data:')) {
                  jsonStr = jsonStr.substring(5); // 移除"data:"前缀
                }

                // 跳过空行或非JSON行
                if (!jsonStr || jsonStr === '[DONE]') {
                  continue;
                }

                const data = JSON.parse(jsonStr);

                // 调用进度回调
                if (onProgress) {
                  onProgress(data);
                }

                // 累积内容 - 处理text_chunk事件（实时流式内容）
                if (data.event === 'text_chunk' && data.data && data.data.text) {
                  allContent += data.data.text;
                }

                // 处理最终结果
                if (data.event === 'workflow_finished') {
                  // 如果有最终输出，使用最终输出；否则使用累积的内容
                  if (data.data && data.data.outputs && data.data.outputs.text) {
                    allContent = data.data.outputs.text;
                  }
                }
              } catch (e) {
                // 静默处理解析错误
              }
            }
          }

          return readStream();
        });
      }

      return readStream();
    })
    .catch(error => {
      console.error('工程类合规性检查请求失败:', error);
      reject(error);
    });
  });
};

// 采购类合规性检查接口 - 使用Dify agent（流式处理）
export const checkProcurementComplianceWithDify = (projectId, onProgress) => {
  return new Promise((resolve, reject) => {
    let allContent = ''; // 累积所有内容

    fetch(`/api/xjzs/projectReport/checkProcurementComplianceWithDify?projectId=${projectId}`, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream'
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      function readStream() {
        return reader.read().then(({ done, value }) => {
          if (done) {
            // 流结束，返回与其他类型相同的结构
            resolve({
              data: {
                success: true,
                detail: allContent || '暂无分析内容',
                raw_response: { answer: allContent }
              }
            });
            return;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop(); // 保留最后一个可能不完整的行

          for (const line of lines) {
            if (line.trim()) {
              try {
                // 处理Server-Sent Events格式，移除"data:"前缀
                let jsonStr = line.trim();
                if (jsonStr.startsWith('data:')) {
                  jsonStr = jsonStr.substring(5); // 移除"data:"前缀
                }

                // 跳过空行或非JSON行
                if (!jsonStr || jsonStr === '[DONE]') {
                  continue;
                }

                const data = JSON.parse(jsonStr);

                // 调用进度回调
                if (onProgress) {
                  onProgress(data);
                }

                // 累积内容 - 处理text_chunk事件（实时流式内容）
                if (data.event === 'text_chunk' && data.data && data.data.text) {
                  allContent += data.data.text;
                }

                // 处理最终结果
                if (data.event === 'workflow_finished') {
                  // 如果有最终输出，使用最终输出；否则使用累积的内容
                  if (data.data && data.data.outputs) {
                    // 检查 outputs.text 字段
                    if (data.data.outputs.text) {
                      allContent = data.data.outputs.text;
                    }
                    // 检查 outputs.output 数组字段
                    else if (data.data.outputs.output && Array.isArray(data.data.outputs.output) && data.data.outputs.output.length > 0) {
                      // 合并所有数组元素的内容
                      allContent = data.data.outputs.output.join('\n\n---分析结果分隔线---\n\n');
                    }
                  }
                }
              } catch (e) {
                // 静默处理解析错误
              }
            }
          }

          return readStream();
        });
      }

      return readStream();
    })
    .catch(error => {
      reject(error);
    });
  });
};