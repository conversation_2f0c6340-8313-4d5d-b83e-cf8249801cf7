package org.springblade.modules.xjzs.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.xjzs.pojo.dto.AuditParams;
import org.springblade.modules.xjzs.pojo.dto.AuditResult;
import org.springblade.modules.xjzs.service.IAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 工程造价咨询服务实现类
 * 包含二级分类：预算、结算审核、全过程跟踪审计、竣工决算审计
 */
@Service
@Slf4j
public class EngineeringCostConsultingServiceImpl implements IAuditService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public String getServiceType() {
        return "工程造价咨询";
    }
    
    @Override
    public AuditResult calculate(AuditParams params) {
        try {
            log.info("开始计算工程造价咨询审计费用: {}", params);
            
            // 解析表格数据
            List<Map<String, Object>> tableData = null;
            if (StringUtil.isNotBlank(params.getTableData())) {
                try {
                    tableData = objectMapper.readValue(params.getTableData(), 
                        new TypeReference<List<Map<String, Object>>>() {});
                } catch (Exception e) {
                    log.error("解析工程造价咨询数据失败", e);
                }
            }
            
            if (tableData == null || tableData.isEmpty()) {
                return AuditResult.fail("工程造价咨询数据为空");
            }
            
            // 创建计算结果
            AuditResult result = AuditResult.success(BigDecimal.ZERO);
            
            // 添加计算思路说明
            List<String> calculationProcess = new ArrayList<>();
            calculationProcess.add("=== 工程造价咨询费用计算开始 ===");
            
            // 总费用
            BigDecimal totalCost = BigDecimal.ZERO;
            
            // 遍历每一行数据
            for (int i = 0; i < tableData.size(); i++) {
                Map<String, Object> row = tableData.get(i);
                calculationProcess.add("\n--- 第" + (i + 1) + "行数据计算 ---");
                
                // 获取咨询类型（二级分类）
                String consultingType = (String) row.get("consultingType");
                if (StringUtil.isBlank(consultingType)) {
                    calculationProcess.add("警告：第" + (i + 1) + "行咨询类型为空，跳过计算");
                    continue;
                }
                
                calculationProcess.add("确定咨询类型：" + consultingType);
                
                // 根据咨询类型计算费用
                BigDecimal rowCost = BigDecimal.ZERO;
                switch (consultingType) {
                    case "预算":
                        rowCost = calculateBudgetFee(row, result, calculationProcess);
                        break;
                    case "结算审核":
                        rowCost = calculateSettlementAuditFee(row, result, calculationProcess);
                        break;
                    case "全过程跟踪审计":
                        rowCost = calculateFullProcessTrackingFee(row, result, calculationProcess);
                        break;
                    case "竣工决算审计":
                        rowCost = calculateCompletionSettlementFee(row, result, calculationProcess);
                        break;
                    default:
                        calculationProcess.add("错误：未知的咨询类型 - " + consultingType);
                        continue;
                }
                
                calculationProcess.add("第" + (i + 1) + "行计算结果：" + rowCost.toPlainString() + " 元");
                totalCost = totalCost.add(rowCost);
            }
            
            // 设置最终总费用
            calculationProcess.add("\n=== 工程造价咨询费用计算完成 ===");
            calculationProcess.add("最终总费用：" + totalCost.toPlainString() + " 元");
            result.setTotalCost(totalCost);
            result.setCalculationProcess(calculationProcess);
            
            return result;
        } catch (Exception e) {
            log.error("计算工程造价咨询审计费用时发生错误", e);
            return AuditResult.fail("计算错误: " + e.getMessage());
        }
    }
    
    /**
     * 计算预算费用
     */
    private BigDecimal calculateBudgetFee(Map<String, Object> row, AuditResult result, List<String> calculationProcess) {
        calculationProcess.add("开始计算预算费用：");
        
        // 获取建设工程造价
        BigDecimal constructionCost = getDecimalValue(row, "constructionCost");
        if (constructionCost == null || constructionCost.compareTo(BigDecimal.ZERO) <= 0) {
            calculationProcess.add("错误：建设工程造价无效或为空");
            return BigDecimal.ZERO;
        }
        
        calculationProcess.add("建设工程造价：" + constructionCost.toPlainString() + " 万元");
        
        // 获取计价方法
        String pricingMethod = (String) row.get("pricingMethod");
        if (StringUtil.isBlank(pricingMethod)) {
            pricingMethod = "工程量清单计价";
        }
        
        calculationProcess.add("计价方法：" + pricingMethod);
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if ("工程量清单计价".equals(pricingMethod)) {
            fee = calculateBudgetFeeByQuantityList(constructionCost, calculationProcess);
        } else if ("定额计价".equals(pricingMethod)) {
            fee = calculateBudgetFeeByQuota(constructionCost, calculationProcess);
        } else {
            calculationProcess.add("错误：未知的计价方法 - " + pricingMethod);
            return BigDecimal.ZERO;
        }
        
        // 添加计算明细
        Map<String, Object> detail = new HashMap<>();
        detail.put("type", "预算");
        detail.put("constructionCost", constructionCost);
        detail.put("pricingMethod", pricingMethod);
        detail.put("fee", fee);
        detail.put("description", "预算费用: " + constructionCost + "万元 (" + pricingMethod + ") = " + fee + "元");
        result.addDetail(detail);
        
        return fee;
    }
    
    /**
     * 工程量清单计价方式计算预算费用
     */
    private BigDecimal calculateBudgetFeeByQuantityList(BigDecimal constructionCost, List<String> calculationProcess) {
        calculationProcess.add("采用工程量清单计价方式计算：");
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if (constructionCost.compareTo(new BigDecimal("500")) <= 0) {
            fee = constructionCost.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            calculationProcess.add("建设工程造价 ≤ 500万元，费率 = 0.8%");
            calculationProcess.add("计算：" + constructionCost + " × 10000 × 0.008 = " + fee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("1000")) <= 0) {
            BigDecimal baseFee = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("500"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            fee = baseFee.add(excessFee);
            calculationProcess.add("500万元 < 建设工程造价 ≤ 1000万元，分段计算：");
            calculationProcess.add("基础费用：500 × 10000 × 0.008 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.007 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + baseFee.toPlainString() + " + " + excessFee.toPlainString() + " = " + fee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("5000")) <= 0) {
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            BigDecimal baseFee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("1000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            calculationProcess.add("1000万元 < 建设工程造价 ≤ 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.008 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.007 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：" + excessAmount + " × 10000 × 0.005 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        } else {
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            BigDecimal baseFee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            BigDecimal baseFee3 = new BigDecimal("4000").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("5000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            fee = baseFee1.add(baseFee2).add(baseFee3).add(excessFee);
            calculationProcess.add("建设工程造价 > 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.008 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.007 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：4000 × 10000 × 0.005 = " + baseFee3.toPlainString() + " 元");
            calculationProcess.add("第四段：" + excessAmount + " × 10000 × 0.003 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        }
        
        return fee;
    }
    
    /**
     * 定额计价方式计算预算费用
     */
    private BigDecimal calculateBudgetFeeByQuota(BigDecimal constructionCost, List<String> calculationProcess) {
        calculationProcess.add("采用定额计价方式计算：");
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if (constructionCost.compareTo(new BigDecimal("500")) <= 0) {
            fee = constructionCost.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.01"));
            calculationProcess.add("建设工程造价 ≤ 500万元，费率 = 1.0%");
            calculationProcess.add("计算：" + constructionCost + " × 10000 × 0.01 = " + fee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("1000")) <= 0) {
            BigDecimal baseFee = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.01"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("500"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.009"));
            fee = baseFee.add(excessFee);
            calculationProcess.add("500万元 < 建设工程造价 ≤ 1000万元，分段计算：");
            calculationProcess.add("基础费用：500 × 10000 × 0.01 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.009 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("5000")) <= 0) {
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.01"));
            BigDecimal baseFee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.009"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("1000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            calculationProcess.add("1000万元 < 建设工程造价 ≤ 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.01 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.009 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：" + excessAmount + " × 10000 × 0.007 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        } else {
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.01"));
            BigDecimal baseFee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.009"));
            BigDecimal baseFee3 = new BigDecimal("4000").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("5000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            fee = baseFee1.add(baseFee2).add(baseFee3).add(excessFee);
            calculationProcess.add("建设工程造价 > 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.01 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.009 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：4000 × 10000 × 0.007 = " + baseFee3.toPlainString() + " 元");
            calculationProcess.add("第四段：" + excessAmount + " × 10000 × 0.005 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        }
        
        return fee;
    }
    
    /**
     * 计算结算审核费用
     */
    private BigDecimal calculateSettlementAuditFee(Map<String, Object> row, AuditResult result, List<String> calculationProcess) {
        calculationProcess.add("开始计算结算审核费用：");
        
        // 获取建设工程造价
        BigDecimal constructionCost = getDecimalValue(row, "constructionCost");
        if (constructionCost == null || constructionCost.compareTo(BigDecimal.ZERO) <= 0) {
            calculationProcess.add("错误：建设工程造价无效或为空");
            return BigDecimal.ZERO;
        }
        
        calculationProcess.add("建设工程造价：" + constructionCost.toPlainString() + " 万元");
        
        // 计算基本费
        BigDecimal baseFee = calculateSettlementBaseFee(constructionCost, calculationProcess);
        
        // 计算效率费（假设效率系数为1.0，实际应从参数中获取）
        BigDecimal efficiencyCoefficient = getDecimalValue(row, "efficiencyCoefficient");
        if (efficiencyCoefficient == null) {
            efficiencyCoefficient = BigDecimal.ONE;
        }
        
        calculationProcess.add("效率系数：" + efficiencyCoefficient.toPlainString());
        
        BigDecimal efficiencyFee = baseFee.multiply(efficiencyCoefficient);
        calculationProcess.add("效率费计算：基本费 × 效率系数 = " + baseFee.toPlainString() + " × " + 
            efficiencyCoefficient.toPlainString() + " = " + efficiencyFee.toPlainString() + " 元");
        
        BigDecimal totalFee = baseFee.add(efficiencyFee);
        calculationProcess.add("结算审核总费用：基本费 + 效率费 = " + baseFee.toPlainString() + " + " + 
            efficiencyFee.toPlainString() + " = " + totalFee.toPlainString() + " 元");
        
        // 添加计算明细
        Map<String, Object> detail = new HashMap<>();
        detail.put("type", "结算审核");
        detail.put("constructionCost", constructionCost);
        detail.put("baseFee", baseFee);
        detail.put("efficiencyCoefficient", efficiencyCoefficient);
        detail.put("efficiencyFee", efficiencyFee);
        detail.put("totalFee", totalFee);
        detail.put("description", "结算审核费用: 基本费" + baseFee + "元 + 效率费" + efficiencyFee + "元 = " + totalFee + "元");
        result.addDetail(detail);
        
        return totalFee;
    }
    
    /**
     * 计算结算审核基本费
     */
    private BigDecimal calculateSettlementBaseFee(BigDecimal constructionCost, List<String> calculationProcess) {
        calculationProcess.add("计算结算审核基本费：");
        
        BigDecimal baseFee = BigDecimal.ZERO;
        
        if (constructionCost.compareTo(new BigDecimal("500")) <= 0) {
            baseFee = constructionCost.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.004"));
            calculationProcess.add("建设工程造价 ≤ 500万元，费率 = 0.4%");
            calculationProcess.add("基本费计算：" + constructionCost + " × 10000 × 0.004 = " + baseFee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("1000")) <= 0) {
            BigDecimal fee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.004"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("500"));
            BigDecimal fee2 = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0035"));
            baseFee = fee1.add(fee2);
            calculationProcess.add("500万元 < 建设工程造价 ≤ 1000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.004 = " + fee1.toPlainString() + " 元");
            calculationProcess.add("第二段：" + excessAmount + " × 10000 × 0.0035 = " + fee2.toPlainString() + " 元");
            calculationProcess.add("基本费总计：" + baseFee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("5000")) <= 0) {
            BigDecimal fee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.004"));
            BigDecimal fee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0035"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("1000"));
            BigDecimal fee3 = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025"));
            baseFee = fee1.add(fee2).add(fee3);
            calculationProcess.add("1000万元 < 建设工程造价 ≤ 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.004 = " + fee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.0035 = " + fee2.toPlainString() + " 元");
            calculationProcess.add("第三段：" + excessAmount + " × 10000 × 0.0025 = " + fee3.toPlainString() + " 元");
            calculationProcess.add("基本费总计：" + baseFee.toPlainString() + " 元");
        } else {
            BigDecimal fee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.004"));
            BigDecimal fee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0035"));
            BigDecimal fee3 = new BigDecimal("4000").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("5000"));
            BigDecimal fee4 = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0015"));
            baseFee = fee1.add(fee2).add(fee3).add(fee4);
            calculationProcess.add("建设工程造价 > 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.004 = " + fee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.0035 = " + fee2.toPlainString() + " 元");
            calculationProcess.add("第三段：4000 × 10000 × 0.0025 = " + fee3.toPlainString() + " 元");
            calculationProcess.add("第四段：" + excessAmount + " × 10000 × 0.0015 = " + fee4.toPlainString() + " 元");
            calculationProcess.add("基本费总计：" + baseFee.toPlainString() + " 元");
        }
        
        return baseFee;
    }
    
    /**
     * 计算全过程跟踪审计费用
     */
    private BigDecimal calculateFullProcessTrackingFee(Map<String, Object> row, AuditResult result, List<String> calculationProcess) {
        calculationProcess.add("开始计算全过程跟踪审计费用：");
        
        // 获取建设工程造价
        BigDecimal constructionCost = getDecimalValue(row, "constructionCost");
        if (constructionCost == null || constructionCost.compareTo(BigDecimal.ZERO) <= 0) {
            calculationProcess.add("错误：建设工程造价无效或为空");
            return BigDecimal.ZERO;
        }
        
        calculationProcess.add("建设工程造价：" + constructionCost.toPlainString() + " 万元");
        
        // 计算工程预算审核费
        BigDecimal budgetAuditFee = calculateFullProcessBudgetAuditFee(constructionCost, calculationProcess);
        
        // 计算过程跟踪审计费
        BigDecimal processTrackingFee = calculateFullProcessTrackingAuditFee(constructionCost, calculationProcess);
        
        // 计算工程结算审核效率费
        BigDecimal settlementEfficiencyFee = calculateFullProcessSettlementEfficiencyFee(constructionCost, calculationProcess);
        
        BigDecimal totalFee = budgetAuditFee.add(processTrackingFee).add(settlementEfficiencyFee);
        calculationProcess.add("全过程跟踪审计总费用：" + budgetAuditFee.toPlainString() + " + " + 
            processTrackingFee.toPlainString() + " + " + settlementEfficiencyFee.toPlainString() + 
            " = " + totalFee.toPlainString() + " 元");
        
        // 添加计算明细
        Map<String, Object> detail = new HashMap<>();
        detail.put("type", "全过程跟踪审计");
        detail.put("constructionCost", constructionCost);
        detail.put("budgetAuditFee", budgetAuditFee);
        detail.put("processTrackingFee", processTrackingFee);
        detail.put("settlementEfficiencyFee", settlementEfficiencyFee);
        detail.put("totalFee", totalFee);
        detail.put("description", "全过程跟踪审计费用: " + totalFee + "元");
        result.addDetail(detail);
        
        return totalFee;
    }
    
    /**
     * 计算竣工决算审计费用
     */
    private BigDecimal calculateCompletionSettlementFee(Map<String, Object> row, AuditResult result, List<String> calculationProcess) {
        calculationProcess.add("开始计算竣工决算审计费用：");
        
        // 获取项目总投资
        BigDecimal totalInvestment = getDecimalValue(row, "totalInvestment");
        if (totalInvestment == null || totalInvestment.compareTo(BigDecimal.ZERO) <= 0) {
            calculationProcess.add("错误：项目总投资无效或为空");
            return BigDecimal.ZERO;
        }
        
        calculationProcess.add("项目总投资：" + totalInvestment.toPlainString() + " 万元");
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if (totalInvestment.compareTo(new BigDecimal("500")) <= 0) {
            fee = totalInvestment.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            calculationProcess.add("项目总投资 ≤ 500万元，费率 = 0.8%");
            calculationProcess.add("计算：" + totalInvestment + " × 10000 × 0.008 = " + fee.toPlainString() + " 元");
        } else if (totalInvestment.compareTo(new BigDecimal("1000")) <= 0) {
            BigDecimal baseFee = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            BigDecimal excessAmount = totalInvestment.subtract(new BigDecimal("500"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            fee = baseFee.add(excessFee);
            calculationProcess.add("500万元 < 项目总投资 ≤ 1000万元，分段计算：");
            calculationProcess.add("基础费用：500 × 10000 × 0.008 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.007 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        } else if (totalInvestment.compareTo(new BigDecimal("5000")) <= 0) {
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            BigDecimal baseFee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            BigDecimal excessAmount = totalInvestment.subtract(new BigDecimal("1000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            calculationProcess.add("1000万元 < 项目总投资 ≤ 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.008 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.007 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：" + excessAmount + " × 10000 × 0.005 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        } else {
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.008"));
            BigDecimal baseFee2 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.007"));
            BigDecimal baseFee3 = new BigDecimal("4000").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.005"));
            BigDecimal excessAmount = totalInvestment.subtract(new BigDecimal("5000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            fee = baseFee1.add(baseFee2).add(baseFee3).add(excessFee);
            calculationProcess.add("项目总投资 > 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.008 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：500 × 10000 × 0.007 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：4000 × 10000 × 0.005 = " + baseFee3.toPlainString() + " 元");
            calculationProcess.add("第四段：" + excessAmount + " × 10000 × 0.003 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("总计：" + fee.toPlainString() + " 元");
        }
        
        // 添加计算明细
        Map<String, Object> detail = new HashMap<>();
        detail.put("type", "竣工决算审计");
        detail.put("totalInvestment", totalInvestment);
        detail.put("fee", fee);
        detail.put("description", "竣工决算审计费用: " + totalInvestment + "万元 = " + fee + "元");
        result.addDetail(detail);
        
        return fee;
    }
    
    /**
     * 计算全过程跟踪审计中的工程预算审核费
     * @param constructionCost 建设工程造价（万元）
     * @param calculationProcess 计算过程记录
     * @return 工程预算审核费（元）
     */
    private BigDecimal calculateFullProcessBudgetAuditFee(BigDecimal constructionCost, List<String> calculationProcess) {
        calculationProcess.add("计算工程预算审核费：");
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if (constructionCost.compareTo(new BigDecimal("500")) <= 0) {
            // ≤500万元：费率 = 0.3%
            fee = constructionCost.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            calculationProcess.add("建设工程造价 ≤ 500万元，费率 = 0.3%");
            calculationProcess.add("计算：" + constructionCost + " × 10000 × 0.003 = " + fee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("5000")) <= 0) {
            // 500~5000万元：分段计算
            BigDecimal baseFee = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("500"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0022"));
            fee = baseFee.add(excessFee);
            calculationProcess.add("500万元 < 建设工程造价 ≤ 5000万元，分段计算：");
            calculationProcess.add("基础费用：500 × 10000 × 0.003 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.0022 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("小计：" + fee.toPlainString() + " 元");
        } else {
            // >5000万元：分段计算
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.003"));
            BigDecimal baseFee2 = new BigDecimal("4500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0022"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("5000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.002"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            calculationProcess.add("建设工程造价 > 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.003 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：4500 × 10000 × 0.0022 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：" + excessAmount + " × 10000 × 0.002 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("小计：" + fee.toPlainString() + " 元");
        }
        
        return fee.setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算全过程跟踪审计中的过程跟踪审计费
     * @param constructionCost 建设工程造价（万元）
     * @param calculationProcess 计算过程记录
     * @return 过程跟踪审计费（元）
     */
    private BigDecimal calculateFullProcessTrackingAuditFee(BigDecimal constructionCost, List<String> calculationProcess) {
        calculationProcess.add("计算过程跟踪审计费：");
        
        // 过程跟踪审计费 = 建设工程造价 × 0.15%
        BigDecimal fee = constructionCost.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0015"));
        calculationProcess.add("过程跟踪审计费率 = 0.15%");
        calculationProcess.add("计算：" + constructionCost + " × 10000 × 0.0015 = " + fee.toPlainString() + " 元");
        
        return fee.setScale(2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算全过程跟踪审计中的工程结算审核效率费
     * @param constructionCost 建设工程造价（万元）
     * @param calculationProcess 计算过程记录
     * @return 工程结算审核效率费（元）
     */
    private BigDecimal calculateFullProcessSettlementEfficiencyFee(BigDecimal constructionCost, List<String> calculationProcess) {
        calculationProcess.add("计算工程结算审核效率费：");
        
        BigDecimal fee = BigDecimal.ZERO;
        
        if (constructionCost.compareTo(new BigDecimal("500")) <= 0) {
            // ≤500万元：费率 = 0.25%
            fee = constructionCost.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025"));
            calculationProcess.add("建设工程造价 ≤ 500万元，费率 = 0.25%");
            calculationProcess.add("计算：" + constructionCost + " × 10000 × 0.0025 = " + fee.toPlainString() + " 元");
        } else if (constructionCost.compareTo(new BigDecimal("5000")) <= 0) {
            // 500~5000万元：分段计算
            BigDecimal baseFee = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("500"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.002"));
            fee = baseFee.add(excessFee);
            calculationProcess.add("500万元 < 建设工程造价 ≤ 5000万元，分段计算：");
            calculationProcess.add("基础费用：500 × 10000 × 0.0025 = " + baseFee.toPlainString() + " 元");
            calculationProcess.add("超出部分：" + excessAmount + " × 10000 × 0.002 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("小计：" + fee.toPlainString() + " 元");
        } else {
            // >5000万元：分段计算
            BigDecimal baseFee1 = new BigDecimal("500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0025"));
            BigDecimal baseFee2 = new BigDecimal("4500").multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.002"));
            BigDecimal excessAmount = constructionCost.subtract(new BigDecimal("5000"));
            BigDecimal excessFee = excessAmount.multiply(new BigDecimal("10000")).multiply(new BigDecimal("0.0015"));
            fee = baseFee1.add(baseFee2).add(excessFee);
            calculationProcess.add("建设工程造价 > 5000万元，分段计算：");
            calculationProcess.add("第一段：500 × 10000 × 0.0025 = " + baseFee1.toPlainString() + " 元");
            calculationProcess.add("第二段：4500 × 10000 × 0.002 = " + baseFee2.toPlainString() + " 元");
            calculationProcess.add("第三段：" + excessAmount + " × 10000 × 0.0015 = " + excessFee.toPlainString() + " 元");
            calculationProcess.add("小计：" + fee.toPlainString() + " 元");
        }
        
        return fee.setScale(2, RoundingMode.HALF_UP);
    }

    // ... 其他辅助方法（全过程跟踪审计的子方法）
    
    /**
     * 从Map中获取BigDecimal值的辅助方法
     */
    private BigDecimal getDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return new BigDecimal(value.toString());
        } else if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        return null;
    }
    
    // 省略全过程跟踪审计的具体子方法实现...
}